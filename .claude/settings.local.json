{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(rm:*)", "Bash(dart run build_runner build:*)", "Bash(grep:*)", "Bash(dart analyze:*)", "Bash(find:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(ls:*)", "Bash(ANDROID_HOME=/Users/<USER>/Library/Android/sdk ./gradlew clean)", "Bash(adb -s emulator-5554 install -r build/app/outputs/apk/debug/app-debug.apk)", "Bash(/Users/<USER>/Library/Android/sdk/platform-tools/adb -s emulator-5554 install -r build/app/outputs/apk/debug/app-debug.apk)", "Bash(/Users/<USER>/Library/Android/sdk/platform-tools/adb -s emulator-5554 install -r /Users/<USER>/Repo/HotBrain/brain_training_app/build/app/outputs/apk/debug/app-debug.apk)", "Bash(/Users/<USER>/Library/Android/sdk/platform-tools/adb:*)", "Bash(for sound in a e i o u k h l q)", "Bash(do echo \"Creating $sound.wav\")", "Bash(ffmpeg:*)", "Bash(done)", "Bash(git checkout:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(dart run:*)", "Bash(adb logcat:*)"], "deny": []}}