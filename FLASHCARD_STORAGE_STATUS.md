# Flash Cards Storage Implementation Status

## Current Issues ❌
- **All cards save to local SQLite only** (guest & signed users)
- **No Firebase Remote Config** for storage limits (missing dependency)
- **No storage limit enforcement** (hard-coded 1000, should be 200 default)
- **No IAP system** for storage expansion
- **No reward points system** for ad-based expansion

## Required Implementation 🔧

### 1. Add Dependencies
```yaml
# pubspec.yaml
firebase_remote_config: ^4.0.0
in_app_purchase: ^3.0.0
```

### 2. Storage Strategy
- **Guest users**: Local SQLite only
- **Signed users**: Firebase + local sync
- **Limits**: 200 default (Remote Config), expandable via subscription/points

### 3. Key Services Needed
```dart
// Remote config for limits
class RemoteConfigService {
  Future<int> getFlashCardLimit(bool isPremium) async;
}

// Storage routing
class FlashCardStorageService {
  Future<bool> canCreateCard(String userId) async;
  Future<void> saveCard(FlashCard card) async; // Route to local/Firebase
}

// Monetization
class SubscriptionService {
  Future<bool> isPremiumUser(String userId) async;
  Future<void> expandStorageWithPoints(String userId, int points) async;
}
```

### 4. Implementation Order
1. Add Remote Config service + dependency
2. Implement storage limit checking in FlashCardsService
3. Add Firebase storage routing for signed users
4. Implement IAP subscription system
5. Add reward points system with ad integration

## How to Continue Development

### Phase 1: Storage Limits (Immediate)
```bash
# 1. Add Remote Config
flutter pub add firebase_remote_config

# 2. Create RemoteConfigService
# - Set up default values (free: 200, premium: 2000)
# - Integrate with FlashCardsService.createCard()

# 3. Add validation in deck_creation_screen.dart
# - Check limits before allowing card creation
# - Show upgrade prompts when limit reached
```

### Phase 2: Firebase Integration
```bash
# 1. Extend FirestoreService with flash card methods
# 2. Modify FlashCardsService to route based on user.isGuest
# 3. Implement sync mechanism for signed users
```

### Phase 3: Monetization
```bash
# 1. Add IAP dependency
# 2. Create SubscriptionService
# 3. Integrate with Buzzvil for reward points
# 4. Add upgrade UI flows
```

### Next Session Commands
```bash
# Check current implementation
grep -r "createCard\|saveCard" lib/features/games/flash_cards/
grep -r "storage.*limit" lib/
grep -r "Remote.*Config" lib/

# Start with Remote Config implementation
# Focus on: lib/core/services/remote_config_service.dart
# Then: lib/features/games/flash_cards/flash_cards_provider.dart (add limit checks)
```

**Priority**: Start with Remote Config service and storage limit enforcement as these are foundational requirements.