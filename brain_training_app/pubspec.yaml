name: brain_booster
description: "과학적으로 검증된 Dual N-back으로 뇌 노화를 막고 지능을 높이는 두뇌 훈련 앱"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Local Authentication (Firebase removed)
  # Note: Social auth removed to focus on local storage
  
  # Database
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # HTTP Client
  dio: ^5.4.0
  
  # JSON Serialization
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0
  
  # Navigation
  go_router: ^16.0.0
  
  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  
  # UI Components
  cupertino_icons: ^1.0.8
  
  # Audio
  audioplayers: ^6.5.0
  
  # Utilities
  path_provider: ^2.1.1
  path: ^1.9.0
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^10.0.0-beta.4
  crypto: ^3.0.5
  
  # Charts & Analytics
  fl_chart: ^1.0.0
  
  # Animations
  lottie: ^3.3.1
  flutter_dotenv: ^5.2.1
  uuid: ^4.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.4.7
  json_serializable: ^6.9.0
  hive_generator: ^2.0.1
  
  # Testing
  sqflite_common_ffi: ^2.3.0
  
  # Linting
  flutter_lints: ^6.0.0
  
  # Integration Tests
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/sounds/
    - assets/fonts/
    - .env
  
  

flutter_intl:
  enabled: true
