# Brain Training App - TODO List

## ✅ 완료된 작업 (Completed Jobs)

### 🏗️ 기본 프로젝트 구조 및 설정 (2025-01-02 완료)
- [x] **프로젝트 초기 설정**
  - [x] Flutter 프로젝트 생성 및 기본 구조 구성
  - [x] pubspec.yaml 의존성 설정
  - [x] 폴더 구조 정리 (features, core, shared)
  - [x] 기본 테마 및 상수 정의

### 🔐 인증 시스템 (2025-01-02 완료)
- [x] **Firebase Auth 연동**
  - [x] AuthService 구현
  - [x] 이메일/비밀번호 로그인
  - [x] 소셜 로그인 (Google, Apple) 기반 구조
  - [x] AuthProvider (Riverpod) 상태 관리
  - [x] 인증 화면 UI 구현

### 🎯 Dual N-Back 게임 (2025-01-02 완료)
- [x] **게임 로직 완전 구현**
  - [x] 시각적 자극 (3x3 그리드) 생성 및 표시
  - [x] 청각적 자극 (9개 문자음) 생성 및 재생
  - [x] N-level 조정 시스템 (1-back부터 시작)
  - [x] 사용자 입력 처리 (시각/청각 매치 판단)
  - [x] 정확도 계산 및 레벨 자동 조정
  - [x] 게임 세션 관리 및 통계 저장

- [x] **게임 UI 완전 구현**
  - [x] 3x3 그리드 시각적 표시
  - [x] 시각/청각 매치 버튼
  - [x] 진행 상황 표시 (현재 trial/총 trials)
  - [x] 실시간 점수 및 정확도 표시
  - [x] 게임 일시정지/재시작 기능
  - [x] 결과 화면 및 통계 표시
  - [X] '다시하기' 버튼을 누르면 그 게임을 다시하는게 아니라 N 을 새로 선택 할 수 있도록 Duan NBack 첫화면으로 이동

- [x] **상태 관리**
  - [x] DualNBackProvider 완전 구현
  - [x] 게임 세션 데이터 저장
  - [x] 성과 통계 업데이트

### 📚 Flash Cards 시스템 (2025-01-02 완료)
- [x] **간격 반복 알고리즘 완전 구현**
  - [x] SM-2 알고리즘 완전 구현
  - [x] 카드 리뷰 스케줄링
  - [x] 난이도별 복습 간격 조정
  - [x] 망각 곡선 기반 복습 주기

- [x] **카드 관리 시스템 완전 구현**
  - [x] 카드 덱 생성/편집/삭제
  - [x] 카드 추가/편집/삭제
  - [x] 카테고리별 카드 분류
  - [x] 기본 카드 덱 (영어 단어, 수학 등) 제공

- [x] **학습 UI 완전 구현**
  - [x] 카드 플립 애니메이션
  - [x] 난이도 평가 버튼 (1-5점)
  - [x] 학습 진도 시각화
  - [x] 일일 목표 설정 및 추적
  - [x] 덱 관리 화면

### 🎮 기본 게임 시스템 (2025-01-02 완료)
- [x] **홈 화면 구현**
  - [x] 게임 선택 인터페이스
  - [x] 사용자 통계 표시
  - [x] 일일 스트릭 표시
  - [x] 최근 성과 요약

- [x] **프로필 시스템**
  - [x] 사용자 프로필 화면
  - [x] 통계 표시 (게임별 성과)
  - [x] 설정 페이지 기본 구조

- [x] **데이터 모델 및 서비스**
  - [x] 사용자 모델 (AppUser) 정의
  - [x] 게임 세션 모델 정의
  - [x] 성취 모델 기본 구조
  - [x] DatabaseService 구현 (SQLite)
  - [x] 상태 관리 (Riverpod) 완전 구현

### 📦 패키지 및 호환성 (2025-01-02 완료)
- [x] **최신 패키지 업데이트**
  - [x] Flutter 3.13+ 호환성 확보
  - [x] 모든 의존성 최신 버전 업데이트
  - [x] 호환성 이슈 해결

---

## 🔥 긴급 수정 사항 (Priority: Critical)

### Firebase 프로젝트 설정 및 연동
- [x] **Firebase 프로젝트 생성 및 기본 설정**
  - [x] Firebase Console에서 새 프로젝트 생성
  - [x] Google Analytics 활성화 (선택)
  - [x] 프로젝트 ID 및 웹 API 키 확인

- [ ] **Android 앱 등록**
  - [x] Firebase Console > 프로젝트 설정 > 일반 > Android 앱 추가
  - [x] 패키지명: `com.example.brain_training_app` (또는 실제 패키지명)
  - [x] `google-services.json` 파일 다운로드
  - [x] `android/app/google-services.json`에 파일 배치
  - [x] `android/build.gradle`에 Google Services 플러그인 추가:
    ```gradle
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
    }
    ```
  - [ ] `android/app/build.gradle` 하단에 플러그인 적용:
    ```gradle
    apply plugin: 'com.google.gms.google-services'
    ```

- [ ] **iOS 앱 등록**
  - [x] Firebase Console > 프로젝트 설정 > 일반 > iOS 앱 추가
  - [x] Bundle ID: `com.example.brainTrainingApp` (또는 실제 Bundle ID)
  - [ ] `GoogleService-Info.plist` 파일 다운로드
  - [ ] Xcode에서 `ios/Runner/GoogleService-Info.plist`에 파일 추가
  - [ ] Target 멤버십 확인 (Runner 체크)

- [ ] **Firebase Authentication 설정**
  - [ ] Firebase Console > Authentication > 시작하기
  - [ ] 로그인 방법 활성화:
    - [ ] 이메일/비밀번호 활성화
    - [ ] Google 로그인 활성화 (SHA-1 인증서 지문 등록 필요)
    - [ ] Apple 로그인 활성화 (iOS만)
    - [ ] 익명 로그인 활성화 (게스트 모드용)
  - [ ] 승인된 도메인 설정 (배포 시)

- [ ] **Firebase Analytics 설정**
  - [ ] Firebase Console > Analytics > 시작하기
  - [ ] 이벤트 추적 설정:
    - [ ] 게임 시작/완료 이벤트
    - [ ] 레벨업 이벤트
    - [ ] 성취 달성 이벤트
    - [ ] 사용자 유지율 추적

- [ ] **Firestore Database 설정 (선택사항)**
  - [ ] 클라우드 동기화가 필요한 경우:
    - [ ] Firestore Database 생성
    - [ ] 보안 규칙 설정
    - [ ] 사용자 데이터 백업 구조 설계

- [ ] **Firebase 초기화 코드 추가**
  - [ ] `lib/main.dart`에 Firebase 초기화:
    ```dart
    import 'package:firebase_core/firebase_core.dart';
    
    void main() async {
      WidgetsFlutterBinding.ensureInitialized();
      await Firebase.initializeApp();
      runApp(BrainTrainingApp());
    }
    ```

- [ ] **빌드 설정 검증**
  - [ ] Android: `flutter build apk` 성공 확인
  - [ ] iOS: `flutter build ios` 성공 확인
  - [ ] Firebase 연결 테스트 (Authentication 로그인/로그아웃)

### 컴파일 에러 수정 (2025-07-02 완료)
- [x] **타입 에러 수정**
  - [x] `shared/providers/game_providers.dart` - GameStats 생성자에 totalXP 파라미터 추가
  - [x] 모든 타입 참조 문제 해결
  - [x] 사용하지 않는 변수 `totalXP` 활용

- [x] **누락된 속성 접근 수정**
  - [x] `core/utils/extensions.dart` - DateTime.isToday 확장 메서드 이미 구현됨
  - [x] 모든 UserStats 속성 접근 문제 해결

- [x] **코드 정리**
  - [x] `main.dart` - 사용하지 않는 import 제거 (Firebase, Auth 모듈)
  - [x] `flash_cards_provider.dart` - print → debugPrint 변경
  - [x] 모든 중요 컴파일 에러 해결 완료

### 성취 시스템 게임 연동 (2025-07-02 완료)
- [x] **Dual N-Back 게임 연동**
  - [x] 게임 완료 시 AchievementService.checkAchievements() 호출 추가
  - [x] 새로운 성취 달성 시 알림 표시 시스템
  - [x] 레벨업 및 정확도 성취 자동 체크

- [x] **Flash Cards 게임 연동**
  - [x] 학습 세션 완료 시 성취 체크 연동
  - [x] 카드 학습 수량 기반 성취 추적
  - [x] 덱 완료 성취 체크 시스템

- [x] **성취 알림 시스템 구현**
  - [x] AchievementNotification 위젯 - 아름다운 성취 달성 알림 UI
  - [x] AchievementNotificationOverlay - 오버레이 기반 알림 관리
  - [x] NotificationService - 성취 알림 서비스 및 다중 성취 처리
  - [x] 애니메이션과 자동 해제 기능 포함

### 메인 네비게이션 시스템 (2025-07-02 완료)
- [x] **4탭 네비게이션 구현**
  - [x] 홈, 게임, 성취, 프로필 탭 구성
  - [x] BottomNavigationBar 고정형(fixed) 타입 설정
  - [x] IndexedStack을 사용한 탭 간 상태 보존
  - [x] 적절한 아이콘 및 라벨 설정

- [x] **홈 화면 성취 미리보기 개선**
  - [x] 실제 성취 데이터 연동 (recentAchievementsProvider)
  - [x] 최근 3개 성취 표시
  - [x] 성취 달성 시간 표시 (timeAgo 확장)
  - [x] XP 보상 표시
  - [x] 성취 탭으로 이동하는 '전체보기' 버튼

- [x] **알림 서비스 컨텍스트 설정**
  - [x] NotificationService에 BuildContext 설정
  - [x] 성취 달성 시 오버레이 알림 표시 준비

- [ ] **Deprecated 메서드 업데이트 (낮은 우선순위)**
  - [ ] withOpacity → withValues 변경 (현재는 경고만 발생)

## 📱 Phase 1: 핵심 게임 구현 (95% 완료)

이 단계가 거의 완료되었습니다! 주요 게임들, 성취 시스템, 네비게이션이 모두 구현되어 앱의 핵심 기능이 완전히 작동합니다.

### 기본 성취 시스템 (2025-07-02 완료)
- [x] **성취 조건 체크 시스템**
  - [x] 게임 완료 후 성취 조건 확인
  - [x] 새로운 성취 언락 알림 준비
  - [x] 성취 진행률 표시

- [x] **성취 데이터 관리**
  - [x] 13개 기본 성취 항목 추가 (스트릭, 게임별, 레벨, 총합)
  - [x] 성취별 XP 보상 시스템
  - [x] 성취 통계 페이지 (AchievementScreen)

- [x] **성취 시스템 구현**
  - [x] AchievementService - 성취 체크 로직
  - [x] Achievement Providers - Riverpod 상태 관리
  - [x] 데이터베이스 연동 (저장/불러오기)
  - [x] UI 구현 (전체/최근 달성 탭)

## ⚡ 다음 우선순위 작업 (Priority: Medium)

### 작업기억 훈련 게임 추가
- [ ] **숫자 순서 기억 게임**
  - [ ] 숫자 시퀀스 표시 및 재현
  - [ ] 난이도별 시퀀스 길이 조정
  - [ ] 역순 재현 모드

### 주의력 훈련 게임 추가  
- [ ] **선택적 주의력 게임**
  - [ ] 타겟 자극 찾기
  - [ ] 방해 요소 무시하기
  - [ ] Stroop 효과 활용 게임

## 🎮 Phase 2: 게임 확장 (Priority: Medium)

### 작업기억 훈련 게임
- [ ] **숫자 순서 기억 게임**
  - [ ] 숫자 시퀀스 표시 및 재현
  - [ ] 난이도별 시퀀스 길이 조정
  - [ ] 역순 재현 모드

- [ ] **시각적 패턴 인식 게임**
  - [ ] 패턴 매트릭스 표시
  - [ ] 패턴 변화 감지
  - [ ] 복잡도 증가 시스템

- [ ] **다중 작업 처리 게임**
  - [ ] 동시 작업 수행 훈련
  - [ ] 우선순위 기반 작업 처리
  - [ ] 간섭 요소 추가

### 주의력 훈련 게임
- [ ] **선택적 주의력 게임**
  - [ ] 타겟 자극 찾기
  - [ ] 방해 요소 무시하기
  - [ ] Stroop 효과 활용 게임

- [ ] **지속적 주의력 테스트**
  - [ ] 장시간 집중력 측정
  - [ ] 경계 상태 유지 훈련
  - [ ] 주의력 감소 패턴 분석

- [ ] **시각 탐색 과제**
  - [ ] 복잡한 화면에서 특정 객체 찾기
  - [ ] 탐색 효율성 측정
  - [ ] 점진적 난이도 증가

### 리더보드 시스템
- [ ] **순위 계산 시스템**
  - [ ] 일간/주간/월간 리더보드
  - [ ] 게임별 개별 순위
  - [ ] 종합 점수 시스템

- [ ] **소셜 기능**
  - [ ] 친구 추가 및 비교
  - [ ] 순위 변동 알림
  - [ ] 성과 공유 기능

### 상세 통계 페이지
- [ ] **게임별 상세 분석**
  - [ ] 시간대별 성과 그래프
  - [ ] 난이도별 정확도 분석
  - [ ] 학습 곡선 시각화

- [ ] **진행 상황 추적**
  - [ ] 장기간 트렌드 분석
  - [ ] 취약 영역 식별
  - [ ] 개선 제안 시스템

## 🚀 Phase 3: 고급 기능 (Priority: Low)

### 버즈빌 SDK 연동
- [ ] **Buzzvil SDK 연동 및 BenefitHub 구현**
  - [ ] 상세 구현 계획은 [BUZZVIL.md](./BUZZVIL.md) 참조
  - [ ] **진행상황:** 안드로이드 연동 및 기본 구현 완료. 테스트 및 고도화 필요.

- [ ] **수익화 모델**
  - [ ] 힌트 구매 시스템
  - [ ] 추가 기회 제공
  - [ ] 프리미엄 구독 모델

### 다국어 지원 (i18n)
- [ ] **ARB 파일 작성**
  - [ ] 한국어 (기본)
  - [ ] 영어
  - [ ] 일본어
  - [ ] 중국어 (간체)

- [ ] **지역화 시스템**
  - [ ] 언어별 폰트 최적화
  - [ ] 문화적 차이 고려
  - [ ] RTL 언어 지원 준비

### 오프라인 모드 최적화
- [ ] **데이터 동기화**
  - [ ] 온라인/오프라인 상태 감지
  - [ ] 로컬 데이터 우선 사용
  - [ ] 백그라운드 동기화

- [ ] **캐싱 시스템**
  - [ ] 게임 에셋 캐싱
  - [ ] 사용자 데이터 캐싱
  - [ ] 효율적인 저장소 관리

### 사용자 정의 테마
- [ ] **테마 시스템 확장**
  - [ ] 컬러 팔레트 선택
  - [ ] 폰트 크기 조정
  - [ ] 접근성 옵션

- [ ] **개인화 기능**
  - [ ] 배경 이미지 설정
  - [ ] 사운드 팩 선택
  - [ ] UI 레이아웃 조정

## 🎯 Phase 4: 출시 준비 (Priority: Medium)

### 통합 테스트 작성
- [ ] **테스트 케이스 작성**
  - [ ] 사용자 인증 플로우 테스트
  - [ ] 게임 로직 정확성 테스트
  - [ ] 데이터 저장/불러오기 테스트
  - [ ] 상태 관리 테스트

- [ ] **자동화 테스트**
  - [ ] CI/CD 파이프라인 구축
  - [ ] 자동 빌드 및 테스트
  - [ ] 성능 회귀 테스트

### 성능 최적화
- [ ] **앱 성능 분석**
  - [ ] 메모리 사용량 최적화
  - [ ] CPU 사용률 개선
  - [ ] 배터리 소모 최소화

- [ ] **사용자 경험 개선**
  - [ ] 로딩 시간 단축
  - [ ] 애니메이션 최적화
  - [ ] 반응성 향상

### 앱스토어 준비
- [ ] **앱 메타데이터**
  - [ ] 앱 아이콘 디자인 (1024x1024)
  - [ ] 스크린샷 (모든 디바이스 크기)
  - [ ] 앱 설명 작성 (한국어/영어)

- [ ] **법적 요구사항**
  - [ ] 개인정보 처리방침 작성
  - [ ] 이용약관 작성
  - [ ] 연령 등급 신청

### 베타 테스트
- [ ] **테스트 그룹 구성**
  - [ ] 내부 테스터 모집
  - [ ] 외부 베타 테스터 모집
  - [ ] 피드백 수집 시스템

- [ ] **테스트 결과 분석**
  - [ ] 사용성 테스트 결과 분석
  - [ ] 버그 리포트 정리
  - [ ] 개선사항 우선순위 결정

## 📝 추가 고려사항

### 접근성
- [ ] **시각 장애인 지원**
  - [ ] 스크린 리더 호환성
  - [ ] 고대비 테마
  - [ ] 폰트 크기 조정

- [ ] **청각 장애인 지원**
  - [ ] 시각적 피드백 강화
  - [ ] 진동 알림 지원
  - [ ] 자막 시스템

### 보안
- [ ] **데이터 보안**
  - [ ] 사용자 데이터 암호화
  - [ ] 안전한 API 통신
  - [ ] 인증 토큰 관리

### 분석 및 모니터링
- [ ] **사용자 행동 분석**
  - [ ] Firebase Analytics 심화 활용
  - [ ] 사용자 여정 추적
  - [ ] A/B 테스트 시스템

- [ ] **앱 모니터링**
  - [ ] 크래시 리포팅
  - [ ] 성능 모니터링
  - [ ] 사용자 피드백 수집

---

## 📊 진행상황 추적

- **전체 진행률**: 85% ⬆️ (+5%)
- **기본 구조 및 인증**: 100% ✅
- **Phase 1 (핵심 게임)**: 100% ✅ ⬆️
  - Dual N-Back 게임: 100% ✅
  - Flash Cards 시스템: 100% ✅ (리워드 포인트 연동)
  - 성취 시스템: 100% ✅ (게임 연동 완료)
  - 메인 네비게이션: 100% ✅ (4탭 구성)
  - **🆕 리워드 포인트 시스템**: 100% ✅ (완전 구현)
- **Phase 2 (게임 확장)**: 15%
- **Phase 3 (고급 기능)**: 25% ⬆️ (+20% 수익화 기반)
- **Phase 4 (출시 준비)**: 10%

## 🎯 다음 우선순위
1. **중간**: 작업기억 훈련 게임 추가
2. **중간**: 주의력 훈련 게임 추가
3. **낮음**: 리더보드 시스템 구현
4. **낮음**: Firebase 설정 (실제 배포 시)

## 🎉 최신 완료 작업 (2025-01-08)

### ✅ **리워드 포인트 시스템 완전 구현** 
**📍 Phase: 수익화 기반 구축 | 우선순위: 높음 | 기간: 1일**

#### 🎯 시스템 개요
사용자가 게임 플레이와 일일 활동을 통해 포인트를 획득하고, 이를 사용해 플래시카드 저장소 확장 등의 프리미엄 기능을 해제할 수 있는 종합적인 리워드 시스템을 구현했습니다.

#### 🏗️ 구현된 핵심 구조
- **📊 데이터 모델**: RewardPoint, RewardBalance, RewardTransaction, UnlockableReward, UserUnlock
- **🔧 서비스 계층**: RewardService, DailyLoginService 
- **💾 이중 저장소**: SQLite (로컬/게스트) + Firestore (로그인 사용자)
- **🎮 자동 연동**: 게임 완료 시 자동 포인트 지급
- **🏪 리워드 상점**: 직관적인 구매 인터페이스

#### 💰 포인트 획득 시스템
- **일일 로그인**: 10포인트/일 + 스트릭 보너스
- **게임 완료**: 5-10포인트 (점수 기반)
- **연속 로그인**: 7일마다 50포인트 보너스
- **확장 준비**: 광고 시청, IAP 보너스, 성취 보상

#### 🔓 해제 가능한 보상
- **저장소 확장**: +100슬롯(500P), +500슬롯(2000P), +1000슬롯(3500P)  
- **게임 도구**: 힌트 팩 5개(100P)
- **향후 확장**: 프리미엄 테마, 쿨다운 스킵 등

#### 📱 사용자 경험
- **실시간 알림**: 로그인 보너스, 스트릭 달성 알림
- **시각적 피드백**: 잔액 표시, 구매 가능 여부 표시
- **진행률 추적**: 총 획득/사용 포인트, 거래 내역

#### 🔧 기술적 구현
- **데이터베이스 스키마**: v2.0으로 업그레이드 (4개 새 테이블)
- **상태 관리**: Riverpod을 통한 반응형 UI 업데이트
- **오류 처리**: 견고한 예외 처리 및 폴백 메커니즘
- **성능 최적화**: 캐싱과 지연 로딩으로 최적화

#### 📁 생성/수정된 파일
**🆕 새로 생성:**
- `lib/shared/models/reward_point.dart` - 리워드 포인트 모델
- `lib/core/services/reward_service.dart` - 리워드 관리 서비스  
- `lib/core/services/daily_login_service.dart` - 일일 로그인 추적
- `lib/features/rewards/reward_store_screen.dart` - 리워드 상점 UI

**✏️ 기존 수정:**
- `lib/core/services/database_service.dart` - 리워드 CRUD 연산 추가
- `lib/core/services/firestore_service.dart` - 클라우드 동기화 지원
- `lib/features/games/flash_cards/flash_cards_provider.dart` - 동적 저장소 한도
- `lib/shared/providers/game_providers.dart` - 자동 리워드 지급
- `lib/shared/widgets/home_screen.dart` - 일일 로그인 보너스 체크

#### 🎯 비즈니스 임팩트
- **사용자 참여**: 일일 로그인 인센티브로 리텐션 향상
- **수익화 준비**: IAP 및 광고 수익 기반 마련
- **진행감**: 명확한 진행 경로와 목표 제공
- **확장성**: 향후 기능 추가를 위한 견고한 아키텍처

#### ✅ 테스트 상태
- **빌드 성공**: Flutter 컴파일 오류 없음
- **데이터베이스**: 마이그레이션 및 CRUD 연산 검증
- **UI 통합**: 모든 화면 정상 작동
- **서비스 연동**: 게임-리워드 자동 연동 확인

---

## 🚀 이전 완료 작업 (2025-07-03)
- ✅ **Firebase 초기화 문제 해결 (2025-07-03)**
  - Firebase.initializeApp() 주석 해제 및 활성화
  - firebase_options.dart 파일 구문 오류 수정
  - 앱 로딩 문제 해결 (Chrome에서 정상 실행 확인)
- ✅ 성취 시스템 게임 연동 완료 (Dual N-Back, Flash Cards)
- ✅ 성취 알림 시스템 구현 (오버레이, 애니메이션)
- ✅ 4탭 메인 네비게이션 구현 (홈, 게임, 성취, 프로필)
- ✅ 홈 화면 성취 미리보기 개선 (실제 데이터 연동)
- ✅ NotificationService 구현 및 컨텍스트 설정

## 📱 현재 앱 상태
- **컴파일**: ✅ 성공 (Chrome에서 정상 실행)
- **Firebase 초기화**: ✅ 성공 (인증 플로우 준비 완료)
- **핵심 기능**: ✅ 두 게임 + 성취 시스템 완전 작동
- **네비게이션**: ✅ 4탭 구성 완료
- **알림 시스템**: ✅ 성취 달성 시 실시간 알림
- **데이터베이스**: ✅ 모든 모델 및 서비스 구현
- **UI/UX**: ✅ 모든 기본 화면 구현 완료
- **상태 관리**: ✅ Riverpod으로 완전 구현

마지막 업데이트: 2025-07-03
