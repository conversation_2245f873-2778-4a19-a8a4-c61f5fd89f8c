# 데이터베이스 마이그레이션 가이드

## 📋 개요

이 문서는 Brain Training App의 SQLite 데이터베이스 스키마 변경 및 마이그레이션을 안전하게 수행하는 방법을 설명합니다.

## 🏗️ 아키텍처 개요

### 핵심 컴포넌트

1. **DatabaseMigrationManager**: 마이그레이션 로직 관리
2. **DatabaseBackupService**: 자동 백업 및 복구
3. **DatabaseIntegrityService**: 데이터 무결성 검증
4. **EnhancedDatabaseService**: 통합 데이터베이스 관리

## 🔄 마이그레이션 프로세스

### 자동 마이그레이션 플로우

```
앱 시작 → 데이터베이스 버전 확인 → 백업 생성 → 마이그레이션 실행 → 무결성 검증 → 완료
```

### 마이그레이션 실패 시

```
실패 감지 → 자동 롤백 → 백업에서 복구 → 에러 로그 → 사용자 알림
```

## 📝 새로운 마이그레이션 추가 방법

### 1. 마이그레이션 함수 정의

```dart
// database_migration_manager.dart의 MigrationRegistry 클래스에 추가

static Future<void> _migrateToVersion5(Database db) async {
  print('[Migration] Adding new feature tables for version 5');
  
  // 새 테이블 생성
  await db.execute('''
    CREATE TABLE new_feature_table (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      feature_data TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  ''');
  
  // 기존 테이블 수정 (필요한 경우)
  await db.execute('ALTER TABLE users ADD COLUMN new_field TEXT');
  
  // 인덱스 추가
  await db.execute('CREATE INDEX idx_new_feature_user_id ON new_feature_table (user_id)');
}
```

### 2. 검증 함수 정의 (선택사항)

```dart
static Future<bool> _validateVersion5(Database db) async {
  // 새 테이블 존재 확인
  final result = await db.rawQuery(
    "SELECT name FROM sqlite_master WHERE type='table' AND name='new_feature_table'"
  );
  
  if (result.isEmpty) {
    print('[Migration] Validation failed: new_feature_table not found');
    return false;
  }
  
  // 새 컬럼 존재 확인
  final columnResult = await db.rawQuery('PRAGMA table_info(users)');
  final hasNewField = columnResult.any((col) => col['name'] == 'new_field');
  
  if (!hasNewField) {
    print('[Migration] Validation failed: new_field column not found');
    return false;
  }
  
  return true;
}
```

### 3. 마이그레이션 등록

```dart
// MigrationRegistry.registerAllMigrations() 메서드에 추가

static void registerAllMigrations(DatabaseMigrationManager manager) {
  // 기존 마이그레이션들...
  
  // 새 마이그레이션 추가
  manager.registerMigration(5, _migrateToVersion5, validation: _validateVersion5);
}
```

### 4. 데이터베이스 버전 업데이트

```dart
// database_service.dart에서 버전 업데이트
class DatabaseService {
  static const int _databaseVersion = 5; // 새 버전으로 업데이트
  // ...
}
```

## 🧪 마이그레이션 테스트

### 단위 테스트 추가

```dart
// database_migration_test.dart에 새 테스트 그룹 추가

group('Version 4 to 5 Migration', () {
  test('should add new feature table', () async {
    final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 4);
    
    // 마이그레이션 실행
    final migrationManager = DatabaseMigrationManager();
    MigrationRegistry.registerAllMigrations(migrationManager);
    await migrationManager.performMigration(db, 4, 5);
    
    // 새 테이블 존재 확인
    final result = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='new_feature_table'"
    );
    expect(result.isNotEmpty, isTrue);
    
    await db.close();
  });
});
```

## 🛡️ 안전 가이드라인

### ✅ 권장사항

1. **항상 백업 생성**: 마이그레이션 전 자동 백업 생성
2. **검증 함수 구현**: 마이그레이션 후 데이터 무결성 확인
3. **단계별 테스트**: 각 마이그레이션 버전을 개별적으로 테스트
4. **롤백 계획**: 실패 시 자동 롤백 메커니즘 활용
5. **로깅 강화**: 상세한 마이그레이션 로그 기록

### ❌ 주의사항

1. **직접 스키마 변경 금지**: `onUpgrade`에서 직접 SQL 실행 금지
2. **데이터 손실 방지**: DROP TABLE 전 데이터 백업 필수
3. **버전 건너뛰기 금지**: 순차적 마이그레이션만 허용
4. **외래키 제약조건**: 변경 시 기존 데이터 정합성 확인
5. **대용량 데이터**: 큰 테이블 변경 시 성능 고려

## 📊 마이그레이션 모니터링

### 헬스체크 수행

```dart
final databaseService = ref.read(enhancedDatabaseServiceProvider);

// 데이터베이스 상태 확인
final health = await databaseService.checkDatabaseHealth();

if (health.needsAttention) {
  print('Database needs attention: ${health.description}');
  
  // 자동 수리 시도
  final repaired = await databaseService.repairDatabase();
  if (repaired) {
    print('Database repaired successfully');
  }
}
```

### 마이그레이션 히스토리 조회

```dart
final migrationHistory = await databaseService.getMigrationHistory();

for (final entry in migrationHistory) {
  print('Version ${entry.version}: ${entry.status} at ${entry.appliedAt}');
}
```

### 백업 관리

```dart
// 수동 백업 생성
final backupPath = await databaseService.createManualBackup('before_feature_update');

// 백업 목록 조회
final backups = await databaseService.listBackups();

// 백업에서 복원
await databaseService.restoreFromBackup(backupPath);
```

## 🔧 트러블슈팅

### 일반적인 문제들

#### 1. 마이그레이션 실패

**증상**: 앱 시작 시 데이터베이스 초기화 실패
**해결**: 
```dart
// 백업에서 자동 복구
final backups = await databaseService.listBackups();
if (backups.isNotEmpty) {
  await databaseService.restoreFromBackup(backups.first.path);
}
```

#### 2. 데이터 무결성 오류

**증상**: 무결성 체크 실패
**해결**:
```dart
final health = await databaseService.checkDatabaseHealth();
if (!health.checkResult.isValid) {
  // 데이터 수리 시도
  await databaseService.repairDatabase();
}
```

#### 3. 성능 저하

**증상**: 마이그레이션 후 쿼리 속도 저하
**해결**:
```dart
// 인덱스 재구성 및 테이블 분석
await db.execute('REINDEX');
await db.execute('ANALYZE');
```

## 📈 성능 최적화

### 마이그레이션 성능 팁

1. **배치 처리**: 대량 데이터 변경 시 트랜잭션 사용
2. **인덱스 관리**: 마이그레이션 전 인덱스 삭제, 후 재생성
3. **임시 테이블**: 복잡한 변경 시 임시 테이블 활용
4. **VACUUM**: 마이그레이션 후 데이터베이스 정리

### 예제: 대용량 데이터 마이그레이션

```dart
static Future<void> _migrateLargeTable(Database db) async {
  await db.transaction((txn) async {
    // 배치 크기 설정
    const batchSize = 1000;
    int offset = 0;
    
    while (true) {
      final batch = await txn.rawQuery('''
        SELECT * FROM large_table 
        LIMIT $batchSize OFFSET $offset
      ''');
      
      if (batch.isEmpty) break;
      
      // 배치 단위로 처리
      for (final row in batch) {
        // 데이터 변환 및 새 테이블에 삽입
        await txn.insert('new_large_table', transformRow(row));
      }
      
      offset += batchSize;
    }
  });
}
```

## 🚀 배포 전 체크리스트

- [ ] 모든 마이그레이션 테스트 통과
- [ ] 다양한 데이터베이스 버전에서 테스트
- [ ] 성능 벤치마크 확인
- [ ] 백업/복구 메커니즘 테스트
- [ ] 롤백 시나리오 테스트
- [ ] 문서 업데이트

## 📚 참고 자료

- [SQLite Migration Best Practices](https://sqlite.org/lang_altertable.html)
- [Flutter Database Migration Guide](https://flutter.dev/docs)
- [프로젝트 내 테스트 코드](../../../test/database_migration_test.dart)

---

**⚠️ 중요**: 프로덕션 환경에서 마이그레이션을 수행하기 전, 반드시 충분한 테스트를 거쳐야 합니다.