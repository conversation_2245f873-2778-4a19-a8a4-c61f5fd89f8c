import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

class StorageService {
  static const String _boxName = 'app_storage';
  static const String _firstRunKey = 'is_first_run';
  
  Box? _box;

  Future<void> init() async {
    _box = await Hive.openBox(_boxName);
  }

  Future<bool> get isFirstRun async {
    await _ensureBoxOpen();
    return _box?.get(_firstRunKey, defaultValue: true) ?? true;
  }

  Future<void> markFirstRunComplete() async {
    await _ensureBoxOpen();
    await _box?.put(_firstRunKey, false);
  }

  Future<void> _ensureBoxOpen() async {
    if (_box == null || !_box!.isOpen) {
      await init();
    }
  }

  Future<void> clearAll() async {
    await _ensureBoxOpen();
    await _box?.clear();
  }

  Future<String?> getString(String key) async {
    await _ensureBoxOpen();
    return _box?.get(key) as String?;
  }

  Future<void> setString(String key, String value) async {
    await _ensureBoxOpen();
    await _box?.put(key, value);
  }

  Future<void> remove(String key) async {
    await _ensureBoxOpen();
    await _box?.delete(key);
  }

  Future<int?> getInt(String key) async {
    await _ensureBoxOpen();
    return _box?.get(key) as int?;
  }

  Future<void> setInt(String key, int value) async {
    await _ensureBoxOpen();
    await _box?.put(key, value);
  }

  Future<bool?> getBool(String key) async {
    await _ensureBoxOpen();
    return _box?.get(key) as bool?;
  }

  Future<void> setBool(String key, bool value) async {
    await _ensureBoxOpen();
    await _box?.put(key, value);
  }

  // Daily login tracking
  Future<String?> getLastLoginDate(String userId) async {
    await _ensureBoxOpen();
    return _box?.get('last_login_$userId') as String?;
  }

  Future<void> setLastLoginDate(String userId, String date) async {
    await _ensureBoxOpen();
    await _box?.put('last_login_$userId', date);
  }

  Future<int> getLoginStreak(String userId) async {
    await _ensureBoxOpen();
    return _box?.get('login_streak_$userId', defaultValue: 0) ?? 0;
  }

  Future<void> setLoginStreak(String userId, int streak) async {
    await _ensureBoxOpen();
    await _box?.put('login_streak_$userId', streak);
  }
}