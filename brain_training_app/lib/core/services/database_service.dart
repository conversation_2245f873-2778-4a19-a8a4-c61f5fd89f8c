import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/models/user.dart';
import '../../shared/models/game_session.dart';
import '../../shared/models/achievement.dart';
import '../../shared/models/flash_card.dart';
import '../../shared/models/reward_point.dart';
import '../constants/game_constants.dart';
import 'database_migration_manager.dart';
import 'database_backup_service.dart';
import 'database_integrity_service.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService();
});

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'brain_training.db';
  static const int _databaseVersion = 4; // Updated to latest version
  
  late final DatabaseMigrationManager _migrationManager;
  late final DatabaseBackupService _backupService;
  late final DatabaseIntegrityService _integrityService;
  
  DatabaseService() {
    _migrationManager = DatabaseMigrationManager();
    _backupService = DatabaseBackupService();
    _integrityService = DatabaseIntegrityService();
    
    // Register all migrations
    MigrationRegistry.registerAllMigrations(_migrationManager);
  }

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    print('[DatabaseService] Initializing database at: $path');
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _performSafeMigration,
    );
  }
  
  Future<void> _performSafeMigration(Database db, int oldVersion, int newVersion) async {
    print('[DatabaseService] Safe migration from version $oldVersion to $newVersion');
    
    try {
      // Create backup before migration
      await _backupService.createBackup(db, 'pre_migration_v${oldVersion}_to_v$newVersion');
      
      // Perform integrity check before migration
      final preCheckResult = await _integrityService.performFullIntegrityCheck(db);
      if (!preCheckResult.isValid) {
        print('[DatabaseService] Warning: Database integrity issues detected before migration');
        for (final error in preCheckResult.errors) {
          print('[DatabaseService] Pre-migration error: $error');
        }
      }
      
      // Perform migration using the migration manager
      await _migrationManager.performMigration(db, oldVersion, newVersion);
      
      // Perform integrity check after migration
      final postCheckResult = await _integrityService.performFullIntegrityCheck(db);
      if (!postCheckResult.isValid) {
        throw Exception('Database integrity check failed after migration: ${postCheckResult.errors.join(', ')}');
      }
      
      print('[DatabaseService] Migration completed successfully');
      
    } catch (e) {
      print('[DatabaseService] Migration failed: $e');
      
      // The migration manager handles rollback internally
      rethrow;
    }
  }

  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        display_name TEXT,
        photo_url TEXT,
        created_at INTEGER NOT NULL,
        last_login_at INTEGER,
        is_guest INTEGER DEFAULT 0,
        stats TEXT NOT NULL,
        preferences TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE game_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        game_type TEXT NOT NULL,
        level INTEGER NOT NULL,
        accuracy REAL NOT NULL,
        duration INTEGER NOT NULL,
        score INTEGER NOT NULL,
        start_time INTEGER NOT NULL,
        end_time INTEGER NOT NULL,
        game_specific_data TEXT NOT NULL,
        xp_earned INTEGER DEFAULT 0,
        is_completed INTEGER DEFAULT 0,
        events TEXT DEFAULT '[]',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE achievements (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        icon_path TEXT NOT NULL,
        type TEXT NOT NULL,
        criteria TEXT NOT NULL,
        xp_reward INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE user_achievements (
        user_id TEXT NOT NULL,
        achievement_id TEXT NOT NULL,
        unlocked_at INTEGER NOT NULL,
        progress INTEGER DEFAULT 0,
        PRIMARY KEY (user_id, achievement_id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (achievement_id) REFERENCES achievements (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE card_decks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER,
        is_active INTEGER DEFAULT 1,
        daily_goal INTEGER DEFAULT 20,
        settings TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE flash_cards (
        id TEXT PRIMARY KEY,
        deck_id TEXT NOT NULL,
        front TEXT NOT NULL,
        back TEXT NOT NULL,
        hint TEXT,
        tags TEXT DEFAULT '[]',
        created_at INTEGER NOT NULL,
        updated_at INTEGER,
        review TEXT NOT NULL,
        FOREIGN KEY (deck_id) REFERENCES card_decks (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE card_study_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        deck_id TEXT NOT NULL,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        reviews TEXT DEFAULT '[]',
        cards_reviewed INTEGER DEFAULT 0,
        correct_answers INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (deck_id) REFERENCES card_decks (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE reward_points (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        amount INTEGER NOT NULL,
        source TEXT NOT NULL,
        earned_at INTEGER NOT NULL,
        description TEXT,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE reward_transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        amount INTEGER NOT NULL,
        description TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        is_earned INTEGER NOT NULL,
        source TEXT,
        unlockable_reward TEXT,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE reward_balances (
        user_id TEXT PRIMARY KEY,
        total_earned INTEGER NOT NULL,
        total_spent INTEGER NOT NULL,
        current_balance INTEGER NOT NULL,
        last_updated INTEGER NOT NULL,
        earned_by_type TEXT DEFAULT '{}',
        spent_on_rewards TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE user_unlocks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        reward TEXT NOT NULL,
        points_spent INTEGER NOT NULL,
        unlocked_at INTEGER NOT NULL,
        transaction_id TEXT,
        quantity INTEGER DEFAULT 1,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await _insertDefaultAchievements(db);
  }

  // Legacy upgrade method - now replaced by the new migration system
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // This method is deprecated and replaced by _performSafeMigration
    // Keeping it for backward compatibility but it should not be called
    print('[DatabaseService] Warning: Legacy _upgradeDatabase called - this should not happen');
    await _performSafeMigration(db, oldVersion, newVersion);
  }

  Future<void> _insertDefaultAchievements(Database db) async {
    final achievements = [
      {
        'id': 'first_game',
        'title': '첫 게임',
        'description': '첫 번째 게임을 완료하세요',
        'icon_path': 'assets/icons/first_game.png',
        'type': 'total',
        'criteria': jsonEncode({'games_played': 1}),
        'xp_reward': 100,
      },
      {
        'id': 'streak_7',
        'title': '7일 연속',
        'description': '7일 연속으로 게임을 플레이하세요',
        'icon_path': 'assets/icons/streak_7.png',
        'type': 'streak',
        'criteria': jsonEncode({'streak_days': 7}),
        'xp_reward': 500,
      },
      {
        'id': 'dual_nback_master',
        'title': 'Dual N-Back 마스터',
        'description': 'Dual N-Back 게임에서 레벨 5에 도달하세요',
        'icon_path': 'assets/icons/dual_nback_master.png',
        'type': 'gameSpecific',
        'criteria': jsonEncode({'game_type': 'dualNBack', 'level': 5}),
        'xp_reward': 1000,
      },
    ];

    for (final achievement in achievements) {
      await db.insert('achievements', achievement);
    }
  }

  // User methods
  Future<void> saveUser(User user) async {
    print('[DEBUG] DatabaseService.saveUser() - Saving user: $user');
    
    try {
      final db = await database;
      print('[DEBUG] DatabaseService.saveUser() - Database connection established');
      
      final data = {
        'id': user.id,
        'email': user.email,
        'display_name': user.displayName,
        'photo_url': user.photoURL,
        'created_at': user.createdAt.millisecondsSinceEpoch,
        'last_login_at': user.lastLoginAt?.millisecondsSinceEpoch,
        'is_guest': user.isGuest == true ? 1 : 0,
        'stats': jsonEncode(user.stats.toJson()),
        'preferences': jsonEncode(user.preferences.toJson()),
      };
      
      print('[DEBUG] DatabaseService.saveUser() - Insert data: $data');
      
      await db.insert(
        'users',
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      print('[DEBUG] DatabaseService.saveUser() - User saved successfully');
    } catch (e) {
      print('[ERROR] DatabaseService.saveUser() - Error: $e');
      rethrow;
    }
  }

  Future<User?> getUser(String userId) async {
    print('[DEBUG] DatabaseService.getUser() - Getting user for ID: $userId');
    
    try {
      final db = await database;
      print('[DEBUG] DatabaseService.getUser() - Database connection established');
      
      final results = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );
      
      print('[DEBUG] DatabaseService.getUser() - Query results: ${results.length} rows');

      if (results.isEmpty) {
        print('[DEBUG] DatabaseService.getUser() - No user found');
        return null;
      }

      final row = results.first;
      print('[DEBUG] DatabaseService.getUser() - Found user row: $row');
      
      final user = User(
        id: row['id'] as String,
        email: row['email'] as String,
        displayName: row['display_name'] as String?,
        photoURL: row['photo_url'] as String?,
        createdAt: DateTime.fromMillisecondsSinceEpoch(row['created_at'] as int),
        lastLoginAt: row['last_login_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(row['last_login_at'] as int)
            : null,
        isGuest: (row['is_guest'] as int) == 1,
        stats: UserStats.fromJson(jsonDecode(row['stats'] as String)),
        preferences: UserPreferences.fromJson(jsonDecode(row['preferences'] as String)),
      );
      
      print('[DEBUG] DatabaseService.getUser() - Constructed user object: $user');
      return user;
    } catch (e) {
      print('[ERROR] DatabaseService.getUser() - Error: $e');
      rethrow;
    }
  }

  Future<void> updateUserStats(String userId, UserStats stats) async {
    final db = await database;
    await db.update(
      'users',
      {'stats': jsonEncode(stats.toJson())},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<void> updateUserPreferences(String userId, UserPreferences preferences) async {
    final db = await database;
    await db.update(
      'users',
      {'preferences': jsonEncode(preferences.toJson())},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  // Game session methods
  Future<void> saveGameSession(GameSession session) async {
    final db = await database;
    await db.insert(
      'game_sessions',
      {
        'id': session.id,
        'user_id': session.userId,
        'game_type': session.gameType.name,
        'level': session.level,
        'accuracy': session.accuracy,
        'duration': session.duration.inMilliseconds,
        'score': session.score,
        'start_time': session.startTime.millisecondsSinceEpoch,
        'end_time': session.endTime.millisecondsSinceEpoch,
        'game_specific_data': jsonEncode(session.gameSpecificData),
        'xp_earned': session.xpEarned,
        'is_completed': session.isCompleted ? 1 : 0,
        'events': jsonEncode(session.events.map((e) => e.toJson()).toList()),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<GameSession>> getGameSessions(String userId, {GameType? gameType}) async {
    final db = await database;
    
    String whereClause = 'user_id = ?';
    List<dynamic> whereArgs = [userId];
    
    if (gameType != null) {
      whereClause += ' AND game_type = ?';
      whereArgs.add(gameType.name);
    }
    
    final results = await db.query(
      'game_sessions',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'start_time DESC',
    );

    return results.map((row) {
      return GameSession(
        id: row['id'] as String,
        userId: row['user_id'] as String,
        gameType: GameType.values.firstWhere((e) => e.name == row['game_type']),
        level: row['level'] as int,
        accuracy: row['accuracy'] as double,
        duration: Duration(milliseconds: row['duration'] as int),
        score: row['score'] as int,
        startTime: DateTime.fromMillisecondsSinceEpoch(row['start_time'] as int),
        endTime: DateTime.fromMillisecondsSinceEpoch(row['end_time'] as int),
        gameSpecificData: jsonDecode(row['game_specific_data'] as String),
        xpEarned: row['xp_earned'] as int,
        isCompleted: (row['is_completed'] as int) == 1,
        events: (jsonDecode(row['events'] as String) as List)
            .map((e) => GameEvent.fromJson(e))
            .toList(),
      );
    }).toList();
  }

  // Achievement methods
  Future<List<Achievement>> getAllAchievements() async {
    final db = await database;
    final results = await db.query('achievements');

    return results.map((row) {
      return Achievement(
        id: row['id'] as String,
        title: row['title'] as String,
        description: row['description'] as String,
        iconPath: row['icon_path'] as String,
        type: AchievementType.values.firstWhere((e) => e.name == row['type']),
        criteria: jsonDecode(row['criteria'] as String),
        xpReward: row['xp_reward'] as int,
      );
    }).toList();
  }

  Future<List<UserAchievement>> getUserAchievements(String userId) async {
    final db = await database;
    final results = await db.query(
      'user_achievements',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    return results.map((row) {
      return UserAchievement(
        userId: row['user_id'] as String,
        achievementId: row['achievement_id'] as String,
        unlockedAt: DateTime.fromMillisecondsSinceEpoch(row['unlocked_at'] as int),
        progress: row['progress'] as int,
      );
    }).toList();
  }

  Future<void> unlockAchievement(String userId, String achievementId) async {
    final db = await database;
    await db.insert(
      'user_achievements',
      {
        'user_id': userId,
        'achievement_id': achievementId,
        'unlocked_at': DateTime.now().millisecondsSinceEpoch,
        'progress': 1,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Flash card methods
  Future<void> saveCardDeck(CardDeck deck) async {
    final db = await database;
    await db.insert(
      'card_decks',
      {
        'id': deck.id,
        'user_id': deck.userId,
        'name': deck.name,
        'description': deck.description,
        'category': deck.category,
        'created_at': deck.createdAt.millisecondsSinceEpoch,
        'updated_at': deck.updatedAt?.millisecondsSinceEpoch,
        'is_active': deck.isActive ? 1 : 0,
        'daily_goal': deck.dailyGoal,
        'settings': jsonEncode(deck.settings),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<CardDeck>> getCardDecks(String userId) async {
    final db = await database;
    final results = await db.query(
      'card_decks',
      where: 'user_id = ? AND is_active = 1',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return results.map((row) {
      return CardDeck(
        id: row['id'] as String,
        userId: row['user_id'] as String,
        name: row['name'] as String,
        description: row['description'] as String,
        category: row['category'] as String?,
        createdAt: DateTime.fromMillisecondsSinceEpoch(row['created_at'] as int),
        updatedAt: row['updated_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int)
            : null,
        isActive: (row['is_active'] as int) == 1,
        dailyGoal: row['daily_goal'] as int,
        settings: jsonDecode(row['settings'] as String),
      );
    }).toList();
  }

  Future<CardDeck?> getCardDeck(String deckId) async {
    final db = await database;
    final results = await db.query(
      'card_decks',
      where: 'id = ? AND is_active = 1',
      whereArgs: [deckId],
      limit: 1,
    );

    if (results.isEmpty) return null;

    final row = results.first;
    return CardDeck(
      id: row['id'] as String,
      userId: row['user_id'] as String,
      name: row['name'] as String,
      description: row['description'] as String,
      category: row['category'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(row['created_at'] as int),
      updatedAt: row['updated_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int)
          : null,
      isActive: (row['is_active'] as int) == 1,
      dailyGoal: row['daily_goal'] as int,
      settings: jsonDecode(row['settings'] as String),
    );
  }

  Future<void> saveFlashCard(FlashCard card) async {
    final db = await database;
    await db.insert(
      'flash_cards',
      {
        'id': card.id,
        'deck_id': card.deckId,
        'front': card.front,
        'back': card.back,
        'hint': card.hint,
        'tags': jsonEncode(card.tags),
        'created_at': card.createdAt.millisecondsSinceEpoch,
        'updated_at': card.updatedAt?.millisecondsSinceEpoch,
        'review': jsonEncode(card.review.toJson()),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<FlashCard>> getFlashCards(String deckId) async {
    final db = await database;
    final results = await db.query(
      'flash_cards',
      where: 'deck_id = ?',
      whereArgs: [deckId],
      orderBy: 'created_at ASC',
    );

    return results.map((row) {
      return FlashCard(
        id: row['id'] as String,
        deckId: row['deck_id'] as String,
        front: row['front'] as String,
        back: row['back'] as String,
        hint: row['hint'] as String?,
        tags: (jsonDecode(row['tags'] as String) as List).cast<String>(),
        createdAt: DateTime.fromMillisecondsSinceEpoch(row['created_at'] as int),
        updatedAt: row['updated_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int)
            : null,
        review: CardReview.fromJson(jsonDecode(row['review'] as String)),
      );
    }).toList();
  }

  Future<List<FlashCard>> getAllFlashCards() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('flash_cards');

    return maps.map((row) {
      return FlashCard(
        id: row['id'] as String,
        deckId: row['deck_id'] as String,
        front: row['front'] as String,
        back: row['back'] as String,
        hint: row['hint'] as String?,
        tags: List<String>.from(jsonDecode(row['tags'] as String? ?? '[]')),
        createdAt: DateTime.fromMillisecondsSinceEpoch(row['created_at'] as int),
        updatedAt: row['updated_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int)
            : null,
        review: CardReview.fromJson(jsonDecode(row['review'] as String)),
      );
    }).toList();
  }

  Future<void> deleteCardDeck(String deckId) async {
    final db = await database;
    await db.delete(
      'card_decks',
      where: 'id = ?',
      whereArgs: [deckId],
    );
  }

  Future<void> deleteFlashCard(String cardId) async {
    final db = await database;
    await db.delete(
      'flash_cards',
      where: 'id = ?',
      whereArgs: [cardId],
    );
  }

  // User Achievement methods
  Future<void> saveUserAchievement(UserAchievement userAchievement) async {
    final db = await database;
    await db.insert(
      'user_achievements',
      {
        'user_id': userAchievement.userId,
        'achievement_id': userAchievement.achievementId,
        'unlocked_at': userAchievement.unlockedAt.millisecondsSinceEpoch,
        'progress': userAchievement.progress,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserStats?> getUserStats(String userId) async {
    final user = await getUser(userId);
    return user?.stats;
  }

  // Reward Point methods
  Future<void> saveRewardPoint(RewardPoint rewardPoint) async {
    final db = await database;
    await db.insert(
      'reward_points',
      {
        'id': rewardPoint.id,
        'user_id': rewardPoint.userId,
        'type': rewardPoint.type.name,
        'amount': rewardPoint.amount,
        'source': rewardPoint.source,
        'earned_at': rewardPoint.earnedAt.millisecondsSinceEpoch,
        'description': rewardPoint.description,
        'metadata': jsonEncode(rewardPoint.metadata ?? {}),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<RewardPoint>> getRewardPoints(String userId) async {
    final db = await database;
    final results = await db.query(
      'reward_points',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'earned_at DESC',
    );

    return results.map((row) {
      return RewardPoint(
        id: row['id'] as String,
        userId: row['user_id'] as String,
        type: RewardType.values.firstWhere((e) => e.name == row['type']),
        amount: row['amount'] as int,
        source: row['source'] as String,
        earnedAt: DateTime.fromMillisecondsSinceEpoch(row['earned_at'] as int),
        description: row['description'] as String?,
        metadata: jsonDecode(row['metadata'] as String),
      );
    }).toList();
  }

  Future<void> saveRewardTransaction(RewardTransaction transaction) async {
    final db = await database;
    await db.insert(
      'reward_transactions',
      {
        'id': transaction.id,
        'user_id': transaction.userId,
        'amount': transaction.amount,
        'description': transaction.description,
        'timestamp': transaction.timestamp.millisecondsSinceEpoch,
        'is_earned': transaction.isEarned ? 1 : 0,
        'source': transaction.source,
        'unlockable_reward': transaction.unlockableReward?.name,
        'metadata': jsonEncode(transaction.metadata ?? {}),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<RewardTransaction>> getRewardTransactions(String userId) async {
    final db = await database;
    final results = await db.query(
      'reward_transactions',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'timestamp DESC',
    );

    return results.map((row) {
      return RewardTransaction(
        id: row['id'] as String,
        userId: row['user_id'] as String,
        amount: row['amount'] as int,
        description: row['description'] as String,
        timestamp: DateTime.fromMillisecondsSinceEpoch(row['timestamp'] as int),
        isEarned: (row['is_earned'] as int) == 1,
        source: row['source'] as String?,
        unlockableReward: row['unlockable_reward'] != null
            ? UnlockableReward.values.firstWhere((e) => e.name == row['unlockable_reward'])
            : null,
        metadata: jsonDecode(row['metadata'] as String),
      );
    }).toList();
  }

  Future<void> saveRewardBalance(RewardBalance balance) async {
    final db = await database;
    await db.insert(
      'reward_balances',
      {
        'user_id': balance.userId,
        'total_earned': balance.totalEarned,
        'total_spent': balance.totalSpent,
        'current_balance': balance.currentBalance,
        'last_updated': balance.lastUpdated.millisecondsSinceEpoch,
        'earned_by_type': jsonEncode(balance.earnedByType.map((k, v) => MapEntry(k.name, v))),
        'spent_on_rewards': jsonEncode(balance.spentOnRewards.map((k, v) => MapEntry(k.name, v))),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<RewardBalance?> getRewardBalance(String userId) async {
    final db = await database;
    final results = await db.query(
      'reward_balances',
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (results.isEmpty) return null;

    final row = results.first;
    final earnedByTypeMap = jsonDecode(row['earned_by_type'] as String) as Map<String, dynamic>;
    final spentOnRewardsMap = jsonDecode(row['spent_on_rewards'] as String) as Map<String, dynamic>;

    return RewardBalance(
      userId: row['user_id'] as String,
      totalEarned: row['total_earned'] as int,
      totalSpent: row['total_spent'] as int,
      currentBalance: row['current_balance'] as int,
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(row['last_updated'] as int),
      earnedByType: earnedByTypeMap.map((k, v) => MapEntry(
        RewardType.values.firstWhere((e) => e.name == k),
        v as int,
      )),
      spentOnRewards: spentOnRewardsMap.map((k, v) => MapEntry(
        UnlockableReward.values.firstWhere((e) => e.name == k),
        v as int,
      )),
    );
  }

  Future<void> saveUserUnlock(UserUnlock unlock) async {
    final db = await database;
    await db.insert(
      'user_unlocks',
      {
        'id': unlock.id,
        'user_id': unlock.userId,
        'reward': unlock.reward.name,
        'points_spent': unlock.pointsSpent,
        'unlocked_at': unlock.unlockedAt.millisecondsSinceEpoch,
        'transaction_id': unlock.transactionId,
        'quantity': unlock.quantity,
        'metadata': jsonEncode(unlock.metadata ?? {}),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<UserUnlock>> getUserUnlocks(String userId) async {
    final db = await database;
    final results = await db.query(
      'user_unlocks',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'unlocked_at DESC',
    );

    return results.map((row) {
      return UserUnlock(
        id: row['id'] as String,
        userId: row['user_id'] as String,
        reward: UnlockableReward.values.firstWhere((e) => e.name == row['reward']),
        pointsSpent: row['points_spent'] as int,
        unlockedAt: DateTime.fromMillisecondsSinceEpoch(row['unlocked_at'] as int),
        transactionId: row['transaction_id'] as String?,
        quantity: row['quantity'] as int,
        metadata: jsonDecode(row['metadata'] as String),
      );
    }).toList();
  }

  // Enhanced database management methods
  Future<DatabaseHealth> checkDatabaseHealth() async {
    final db = await database;
    return await _integrityService.assessDatabaseHealth(db);
  }
  
  Future<List<BackupInfo>> listBackups() async {
    return await _backupService.listBackups();
  }
  
  Future<String> createManualBackup(String reason) async {
    final db = await database;
    return await _backupService.createBackup(db, reason);
  }
  
  Future<void> restoreFromBackup(String backupPath) async {
    final db = await database;
    await db.close();
    _database = null;
    
    await _backupService.restoreBackup(backupPath, join(await getDatabasesPath(), _databaseName));
    
    // Reinitialize database
    await database;
  }
  
  Future<bool> repairDatabase() async {
    final db = await database;
    final health = await checkDatabaseHealth();
    
    if (health.needsAttention) {
      return await _integrityService.repairDatabase(db, health.checkResult);
    }
    
    return true;
  }
  
  Future<List<MigrationLogEntry>> getMigrationHistory() async {
    final db = await database;
    return await _migrationManager.getMigrationHistory(db);
  }
  
  Future<bool> isDatabaseUpToDate() async {
    final db = await database;
    return await _migrationManager.isDatabaseUpToDate(db, _databaseVersion);
  }
  
  // Cleanup
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}