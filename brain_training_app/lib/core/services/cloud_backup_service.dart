import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'local_database_service.dart';
import '../models/app_user.dart';
import '../../shared/models/game_result.dart';
import '../../shared/models/flash_card.dart';
import '../../shared/models/reward_point.dart';

class CloudBackupService {
  static const String _backupFileName = 'brain_booster_backup.json';
  static const String _dbBackupFileName = 'brain_booster_db_backup.db';

  /// Gets the platform-specific backup directory
  /// iOS: Documents directory (synced with iCloud if enabled)
  /// Android: App-specific external directory (synced with Google Drive if enabled)
  Future<Directory> getBackupDirectory() async {
    if (Platform.isIOS) {
      // iOS: Use Documents directory which is automatically backed up to iCloud
      return await getApplicationDocumentsDirectory();
    } else if (Platform.isAndroid) {
      // Android: Use external storage directory for automatic Google Drive backup
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        final backupDir = Directory(path.join(externalDir.path, 'backups'));
        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }
        return backupDir;
      } else {
        // Fallback to application documents directory
        return await getApplicationDocumentsDirectory();
      }
    } else {
      // Other platforms: Use application documents directory
      return await getApplicationDocumentsDirectory();
    }
  }

  /// Creates a complete backup of user data
  Future<File> createBackup(String userId) async {
    try {
      final localDb = LocalDatabaseService();
      final backupDir = await getBackupDirectory();
      
      // Get all user data
      final user = await localDb.getUser(userId);
      final gameResults = await localDb.getGameResults(userId);
      final cardDecks = await localDb.getCardDecks(userId);
      final allCards = await localDb.getAllUserFlashCards(userId);
      final rewardPoints = await localDb.getRewardPoints(userId);
      final rewardTransactions = await localDb.getRewardTransactions(userId);
      final rewardBalance = await localDb.getRewardBalance(userId);
      final userUnlocks = await localDb.getUserUnlocks(userId);

      // Create backup data structure
      final backupData = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'user_id': userId,
        'data': {
          'user': user?.toJson(),
          'game_results': gameResults.map((gr) => gr.toJson()).toList(),
          'card_decks': cardDecks.map((cd) => cd.toJson()).toList(),
          'flash_cards': allCards.map((fc) => fc.toJson()).toList(),
          'reward_points': rewardPoints.map((rp) => rp.toJson()).toList(),
          'reward_transactions': rewardTransactions.map((rt) => rt.toJson()).toList(),
          'reward_balance': rewardBalance?.toJson(),
          'user_unlocks': userUnlocks.map((uu) => uu.toJson()).toList(),
        },
      };

      // Save JSON backup
      final backupFile = File(path.join(backupDir.path, _backupFileName));
      await backupFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(backupData),
      );

      debugPrint('Backup created successfully at: ${backupFile.path}');
      return backupFile;
    } catch (e) {
      debugPrint('Error creating backup: $e');
      rethrow;
    }
  }

  /// Creates a database file backup
  Future<File> createDatabaseBackup() async {
    try {
      final backupDir = await getBackupDirectory();
      final localDb = LocalDatabaseService();
      final db = await localDb.database;
      
      // Get the database path
      final dbPath = db.path;
      final sourceFile = File(dbPath);
      
      // Copy database file to backup directory
      final backupFile = File(path.join(backupDir.path, _dbBackupFileName));
      await sourceFile.copy(backupFile.path);
      
      debugPrint('Database backup created successfully at: ${backupFile.path}');
      return backupFile;
    } catch (e) {
      debugPrint('Error creating database backup: $e');
      rethrow;
    }
  }

  /// Restores data from a backup file
  Future<void> restoreFromBackup(File backupFile) async {
    try {
      final localDb = LocalDatabaseService();
      
      // Read backup file
      final backupContent = await backupFile.readAsString();
      final backupData = jsonDecode(backupContent);
      
      // Validate backup format
      if (backupData['version'] == null || backupData['data'] == null) {
        throw Exception('Invalid backup file format');
      }
      
      final data = backupData['data'];
      final userId = backupData['user_id'];
      
      if (userId == null) {
        throw Exception('No user ID found in backup');
      }

      // Clear existing data for this user (optional - could be configurable)
      // await _clearUserData(userId, localDb);

      // Restore user data
      if (data['user'] != null) {
        final user = AppUser.fromJson(data['user']);
        await localDb.saveUser(user);
      }

      // Restore game results
      if (data['game_results'] != null) {
        for (final gameResultJson in data['game_results']) {
          final gameResult = GameResult.fromJson(gameResultJson);
          await localDb.saveGameResult(gameResult);
        }
      }

      // Restore card decks
      if (data['card_decks'] != null) {
        for (final deckJson in data['card_decks']) {
          final deck = CardDeck.fromJson(deckJson);
          await localDb.saveCardDeck(deck);
        }
      }

      // Restore flash cards
      if (data['flash_cards'] != null) {
        for (final cardJson in data['flash_cards']) {
          final card = FlashCard.fromJson(cardJson);
          await localDb.saveFlashCard(card);
        }
      }

      // Restore reward points
      if (data['reward_points'] != null) {
        for (final pointJson in data['reward_points']) {
          final point = RewardPoint.fromJson(pointJson);
          await localDb.saveRewardPoint(point);
        }
      }

      // Restore reward transactions
      if (data['reward_transactions'] != null) {
        for (final transactionJson in data['reward_transactions']) {
          final transaction = RewardTransaction.fromJson(transactionJson);
          await localDb.saveRewardTransaction(transaction);
        }
      }

      // Restore reward balance
      if (data['reward_balance'] != null) {
        final balance = RewardBalance.fromJson(data['reward_balance']);
        await localDb.saveRewardBalance(balance);
      }

      // Restore user unlocks
      if (data['user_unlocks'] != null) {
        for (final unlockJson in data['user_unlocks']) {
          final unlock = UserUnlock.fromJson(unlockJson);
          await localDb.saveUserUnlock(unlock);
        }
      }

      debugPrint('Backup restored successfully');
    } catch (e) {
      debugPrint('Error restoring from backup: $e');
      rethrow;
    }
  }

  /// Restores from a database backup file
  Future<void> restoreFromDatabaseBackup(File backupFile) async {
    try {
      final localDb = LocalDatabaseService();
      final db = await localDb.database;
      
      // Close current database
      await localDb.close();
      
      // Replace current database with backup
      final dbPath = db.path;
      await backupFile.copy(dbPath);
      
      debugPrint('Database backup restored successfully');
    } catch (e) {
      debugPrint('Error restoring from database backup: $e');
      rethrow;
    }
  }

  /// Lists available backup files
  Future<List<File>> listBackupFiles() async {
    try {
      final backupDir = await getBackupDirectory();
      final List<File> backupFiles = [];
      
      // List JSON backups
      final jsonBackup = File(path.join(backupDir.path, _backupFileName));
      if (await jsonBackup.exists()) {
        backupFiles.add(jsonBackup);
      }
      
      // List database backups
      final dbBackup = File(path.join(backupDir.path, _dbBackupFileName));
      if (await dbBackup.exists()) {
        backupFiles.add(dbBackup);
      }
      
      // List any additional backup files (with timestamps)
      if (await backupDir.exists()) {
        final entities = await backupDir.list().toList();
        for (final entity in entities) {
          if (entity is File && 
              (entity.path.endsWith('.json') || entity.path.endsWith('.db')) &&
              !backupFiles.contains(entity)) {
            backupFiles.add(entity);
          }
        }
      }
      
      return backupFiles;
    } catch (e) {
      debugPrint('Error listing backup files: $e');
      return [];
    }
  }

  /// Creates a timestamped backup file
  Future<File> createTimestampedBackup(String userId) async {
    try {
      final localDb = LocalDatabaseService();
      final backupDir = await getBackupDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      
      // Get all user data
      final user = await localDb.getUser(userId);
      final gameResults = await localDb.getGameResults(userId);
      final cardDecks = await localDb.getCardDecks(userId);
      final allCards = await localDb.getAllUserFlashCards(userId);
      final rewardPoints = await localDb.getRewardPoints(userId);
      final rewardTransactions = await localDb.getRewardTransactions(userId);
      final rewardBalance = await localDb.getRewardBalance(userId);
      final userUnlocks = await localDb.getUserUnlocks(userId);

      // Create backup data structure
      final backupData = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'user_id': userId,
        'data': {
          'user': user?.toJson(),
          'game_results': gameResults.map((gr) => gr.toJson()).toList(),
          'card_decks': cardDecks.map((cd) => cd.toJson()).toList(),
          'flash_cards': allCards.map((fc) => fc.toJson()).toList(),
          'reward_points': rewardPoints.map((rp) => rp.toJson()).toList(),
          'reward_transactions': rewardTransactions.map((rt) => rt.toJson()).toList(),
          'reward_balance': rewardBalance?.toJson(),
          'user_unlocks': userUnlocks.map((uu) => uu.toJson()).toList(),
        },
      };

      // Save timestamped backup
      final fileName = 'brain_booster_backup_$timestamp.json';
      final backupFile = File(path.join(backupDir.path, fileName));
      await backupFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(backupData),
      );

      debugPrint('Timestamped backup created at: ${backupFile.path}');
      return backupFile;
    } catch (e) {
      debugPrint('Error creating timestamped backup: $e');
      rethrow;
    }
  }

  /// Deletes old backup files, keeping only the most recent ones
  Future<void> cleanupOldBackups({int keepCount = 5}) async {
    try {
      final backupDir = await getBackupDirectory();
      if (!await backupDir.exists()) return;

      final entities = await backupDir.list().toList();
      final backupFiles = entities
          .whereType<File>()
          .where((f) => f.path.contains('brain_booster_backup_') && f.path.endsWith('.json'))
          .toList();

      if (backupFiles.length <= keepCount) return;

      // Sort by modification time (newest first)
      backupFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // Delete old backups
      for (int i = keepCount; i < backupFiles.length; i++) {
        await backupFiles[i].delete();
        debugPrint('Deleted old backup: ${backupFiles[i].path}');
      }
    } catch (e) {
      debugPrint('Error cleaning up old backups: $e');
    }
  }

  /// Gets backup file information
  Future<Map<String, dynamic>> getBackupInfo(File backupFile) async {
    try {
      final content = await backupFile.readAsString();
      final data = jsonDecode(content);
      final stats = await backupFile.stat();
      
      return {
        'file_name': path.basename(backupFile.path),
        'file_path': backupFile.path,
        'file_size': stats.size,
        'created_at': stats.modified.toIso8601String(),
        'backup_version': data['version'],
        'backup_timestamp': data['timestamp'],
        'user_id': data['user_id'],
        'data_counts': {
          'game_results': (data['data']['game_results'] as List?)?.length ?? 0,
          'card_decks': (data['data']['card_decks'] as List?)?.length ?? 0,
          'flash_cards': (data['data']['flash_cards'] as List?)?.length ?? 0,
          'reward_points': (data['data']['reward_points'] as List?)?.length ?? 0,
          'reward_transactions': (data['data']['reward_transactions'] as List?)?.length ?? 0,
          'user_unlocks': (data['data']['user_unlocks'] as List?)?.length ?? 0,
        },
      };
    } catch (e) {
      debugPrint('Error getting backup info: $e');
      return {
        'file_name': path.basename(backupFile.path),
        'file_path': backupFile.path,
        'error': e.toString(),
      };
    }
  }

  /// Clears all data for a specific user (used before restore)
  Future<void> _clearUserData(String userId, LocalDatabaseService localDb) async {
    try {
      // This is a destructive operation - use with caution
      final gameResults = await localDb.getGameResults(userId);
      for (final result in gameResults) {
        // Delete individual game results if needed
      }
      
      final cardDecks = await localDb.getCardDecks(userId);
      for (final deck in cardDecks) {
        await localDb.deleteCardDeck(deck.id);
      }
      
      // Clear other user-specific data as needed
      debugPrint('Cleared existing data for user: $userId');
    } catch (e) {
      debugPrint('Error clearing user data: $e');
      rethrow;
    }
  }
}