import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math';
import '../models/app_user.dart';
import '../../shared/models/user.dart';
import 'local_database_service.dart';
import 'package:uuid/uuid.dart';

final localAuthServiceProvider = Provider<LocalAuthService>((ref) {
  return LocalAuthService();
});

class LocalAuthService {
  static const _storage = FlutterSecureStorage();
  static const String _currentUserKey = 'current_user_id';
  static const String _userPasswordKey = 'user_password_';
  static const String _guestUserKey = 'guest_user_id';
  
  final LocalDatabaseService _localDb = LocalDatabaseService();
  final Uuid _uuid = const Uuid();

  AppUser? _currentUser;
  
  Stream<AppUser?> get authStateChanges {
    // For local auth, we'll use a simple stream controller
    // In a real implementation, you might want to use a StreamController
    return Stream.value(_currentUser);
  }

  AppUser? get currentUser => _currentUser;

  /// Initialize the auth service and restore logged in user
  Future<void> initialize() async {
    try {
      final userId = await _storage.read(key: _currentUserKey);
      if (userId != null) {
        final user = await _localDb.getUser(userId);
        if (user != null) {
          _currentUser = user.copyWith(lastLoginAt: DateTime.now());
          await _localDb.updateUser(_currentUser!);
        }
      }
    } catch (e) {
      print('[ERROR] LocalAuthService.initialize() - Error: $e');
    }
  }

  /// Sign in with email and password
  Future<AppUser?> signInWithEmailAndPassword(String email, String password) async {
    try {
      // Find user by email
      final users = await _getAllUsers();
      final user = users.where((u) => u.email.toLowerCase() == email.toLowerCase()).firstOrNull;
      
      if (user == null) {
        throw const AuthException('등록되지 않은 이메일입니다');
      }

      // Verify password
      final storedPasswordHash = await _storage.read(key: '$_userPasswordKey${user.id}');
      final passwordHash = _hashPassword(password);
      
      if (storedPasswordHash != passwordHash) {
        throw const AuthException('비밀번호가 올바르지 않습니다');
      }

      // Update last login time
      _currentUser = user.copyWith(lastLoginAt: DateTime.now());
      await _localDb.updateUser(_currentUser!);
      await _storage.write(key: _currentUserKey, value: _currentUser!.id);

      return _currentUser;
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('로그인 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Create user with email and password
  Future<AppUser?> createUserWithEmailAndPassword(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      // Check if email already exists
      final users = await _getAllUsers();
      final existingUser = users.where((u) => u.email.toLowerCase() == email.toLowerCase()).firstOrNull;
      
      if (existingUser != null) {
        throw const AuthException('이미 사용 중인 이메일입니다');
      }

      // Validate password strength
      if (password.length < 6) {
        throw const AuthException('비밀번호는 최소 6자 이상이어야 합니다');
      }

      // Create new user
      final userId = _uuid.v4();
      final now = DateTime.now();
      
      final newUser = AppUser(
        id: userId,
        email: email,
        displayName: displayName,
        photoURL: null,
        createdAt: now,
        lastLoginAt: now,
        isGuest: false,
        stats: const UserStats(),
        preferences: const UserPreferences(),
      );

      // Save user and password
      await _localDb.saveUser(newUser);
      await _storage.write(key: '$_userPasswordKey$userId', value: _hashPassword(password));
      await _storage.write(key: _currentUserKey, value: userId);

      _currentUser = newUser;
      return _currentUser;
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('계정 생성 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Sign in as guest
  Future<AppUser?> signInAsGuest() async {
    try {
      // Check if there's already a guest user
      String? guestUserId = await _storage.read(key: _guestUserKey);
      AppUser? guestUser;

      if (guestUserId != null) {
        guestUser = await _localDb.getUser(guestUserId);
      }

      if (guestUser == null) {
        // Create new guest user
        guestUserId = _uuid.v4();
        final now = DateTime.now();
        
        guestUser = AppUser(
          id: guestUserId,
          email: '<EMAIL>',
          displayName: '게스트 사용자',
          photoURL: null,
          createdAt: now,
          lastLoginAt: now,
          isGuest: true,
          stats: const UserStats(),
          preferences: const UserPreferences(),
        );

        await _localDb.saveUser(guestUser);
        await _storage.write(key: _guestUserKey, value: guestUserId);
      } else {
        // Update last login time for existing guest
        guestUser = guestUser.copyWith(lastLoginAt: DateTime.now());
        await _localDb.updateUser(guestUser);
      }

      await _storage.write(key: _currentUserKey, value: guestUser.id);
      _currentUser = guestUser;
      
      return _currentUser;
    } catch (e) {
      throw AuthException('게스트 로그인 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _storage.delete(key: _currentUserKey);
      _currentUser = null;
    } catch (e) {
      throw AuthException('로그아웃 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Update user profile
  Future<void> updateProfile({String? displayName, String? photoURL}) async {
    if (_currentUser == null) {
      throw const AuthException('사용자가 로그인되어 있지 않습니다');
    }

    try {
      _currentUser = _currentUser!.copyWith(
        displayName: displayName ?? _currentUser!.displayName,
        photoURL: photoURL ?? _currentUser!.photoURL,
      );
      
      await _localDb.updateUser(_currentUser!);
    } catch (e) {
      throw AuthException('프로필 업데이트 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Change password
  Future<void> changePassword(String currentPassword, String newPassword) async {
    if (_currentUser == null) {
      throw const AuthException('사용자가 로그인되어 있지 않습니다');
    }

    if (_currentUser!.isGuest) {
      throw const AuthException('게스트 사용자는 비밀번호를 변경할 수 없습니다');
    }

    try {
      // Verify current password
      final storedPasswordHash = await _storage.read(key: '$_userPasswordKey${_currentUser!.id}');
      final currentPasswordHash = _hashPassword(currentPassword);
      
      if (storedPasswordHash != currentPasswordHash) {
        throw const AuthException('현재 비밀번호가 올바르지 않습니다');
      }

      // Validate new password strength
      if (newPassword.length < 6) {
        throw const AuthException('새 비밀번호는 최소 6자 이상이어야 합니다');
      }

      // Update password
      await _storage.write(key: '$_userPasswordKey${_currentUser!.id}', value: _hashPassword(newPassword));
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('비밀번호 변경 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Delete account
  Future<void> deleteAccount() async {
    if (_currentUser == null) {
      throw const AuthException('사용자가 로그인되어 있지 않습니다');
    }

    try {
      final userId = _currentUser!.id;
      
      // Delete user data from database
      // Note: You might want to implement a more comprehensive cleanup
      await _storage.delete(key: '$_userPasswordKey$userId');
      await _storage.delete(key: _currentUserKey);
      
      if (_currentUser!.isGuest) {
        await _storage.delete(key: _guestUserKey);
      }

      _currentUser = null;
    } catch (e) {
      throw AuthException('계정 삭제 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Reset password (for demonstration - in a real app this would require email verification)
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      // Find user by email
      final users = await _getAllUsers();
      final user = users.where((u) => u.email.toLowerCase() == email.toLowerCase()).firstOrNull;
      
      if (user == null) {
        throw const AuthException('등록되지 않은 이메일입니다');
      }

      // In a real app, you would send an email with a reset link
      // For now, we'll just generate a temporary password
      final tempPassword = _generateTemporaryPassword();
      await _storage.write(key: '$_userPasswordKey${user.id}', value: _hashPassword(tempPassword));
      
      // In a real implementation, you would send this password via email
      print('임시 비밀번호가 생성되었습니다: $tempPassword');
      
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('비밀번호 재설정 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Convert guest account to regular account
  Future<AppUser?> convertGuestToRegularAccount(String email, String password, String displayName) async {
    if (_currentUser == null || !_currentUser!.isGuest) {
      throw const AuthException('게스트 사용자만 계정 변환이 가능합니다');
    }

    try {
      // Check if email already exists
      final users = await _getAllUsers();
      final existingUser = users.where((u) => u.email.toLowerCase() == email.toLowerCase() && !u.isGuest).firstOrNull;
      
      if (existingUser != null) {
        throw const AuthException('이미 사용 중인 이메일입니다');
      }

      // Validate password strength
      if (password.length < 6) {
        throw const AuthException('비밀번호는 최소 6자 이상이어야 합니다');
      }

      // Update guest user to regular user
      _currentUser = _currentUser!.copyWith(
        email: email,
        displayName: displayName,
        isGuest: false,
      );

      await _localDb.updateUser(_currentUser!);
      await _storage.write(key: '$_userPasswordKey${_currentUser!.id}', value: _hashPassword(password));
      await _storage.delete(key: _guestUserKey);

      return _currentUser;
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException('계정 변환 중 오류가 발생했습니다: ${e.toString()}');
    }
  }

  /// Helper method to get all users (for internal use)
  Future<List<AppUser>> _getAllUsers() async {
    // This is a simplified implementation
    // In a real app, you might want to cache this or implement it differently
    try {
      // Since we don't have a direct method to get all users,
      // we'll implement this as needed
      // For now, return empty list and handle user lookup differently
      return [];
    } catch (e) {
      return [];
    }
  }

  /// Hash password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate a temporary password
  String _generateTemporaryPassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(Iterable.generate(8, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  /// Check if user exists by email
  Future<bool> userExistsByEmail(String email) async {
    try {
      final users = await _getAllUsers();
      return users.any((u) => u.email.toLowerCase() == email.toLowerCase() && !u.isGuest);
    } catch (e) {
      return false;
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStatistics(String userId) async {
    try {
      final user = await _localDb.getUser(userId);
      if (user == null) return {};

      final gameResults = await _localDb.getGameResults(userId);
      final cardDecks = await _localDb.getCardDecks(userId);
      final flashCards = await _localDb.getAllUserFlashCards(userId);
      final rewardPoints = await _localDb.getRewardPoints(userId);

      return {
        'total_games_played': gameResults.length,
        'total_card_decks': cardDecks.length,
        'total_flash_cards': flashCards.length,
        'total_reward_points': rewardPoints.fold(0, (sum, point) => sum + point.points),
        'account_created': user.createdAt.toIso8601String(),
        'last_login': user.lastLoginAt.toIso8601String(),
        'is_guest': user.isGuest,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}

class AuthException implements Exception {
  final String message;
  
  const AuthException(this.message);
  
  @override
  String toString() => message;
}

extension on Iterable<AppUser> {
  AppUser? get firstOrNull {
    return isEmpty ? null : first;
  }
}