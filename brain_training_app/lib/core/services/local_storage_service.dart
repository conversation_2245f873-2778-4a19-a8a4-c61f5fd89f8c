import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../shared/models/game_result.dart';

class LocalStorageService {
  static const String _gameResultsKey = 'gameResults';

  Future<void> saveGameResult(GameResult result) async {
    final prefs = await SharedPreferences.getInstance();
    final results = await getGameResults();
    results.add(result);
    await prefs.setString(_gameResultsKey, jsonEncode(results.map((r) => r.toJson()).toList()));
  }

  Future<List<GameResult>> getGameResults() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_gameResultsKey);
    if (jsonString != null) {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.map((json) => GameResult.fromJson(json)).toList();
    }
    return [];
  }
}
