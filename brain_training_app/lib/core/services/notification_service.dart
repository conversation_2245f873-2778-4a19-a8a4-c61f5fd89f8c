import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/models/achievement.dart';
import '../../shared/widgets/achievement_notification.dart';

final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

class NotificationService {
  BuildContext? _context;

  void setContext(BuildContext context) {
    _context = context;
  }

  void showAchievementUnlocked(Achievement achievement) {
    if (_context != null) {
      AchievementNotificationOverlay.show(_context!, achievement);
    }
  }

  void showAchievementsUnlocked(List<Achievement> achievements) {
    if (achievements.isEmpty || _context == null) return;

    // Show the first achievement immediately
    showAchievementUnlocked(achievements.first);

    // Show remaining achievements with delay
    for (int i = 1; i < achievements.length; i++) {
      Future.delayed(Duration(seconds: 5 * i), () {
        showAchievementUnlocked(achievements[i]);
      });
    }
  }

  void showSnackBar(String message, {bool isError = false}) {
    if (_context == null) return;

    final theme = Theme.of(_context!);
    ScaffoldMessenger.of(_context!).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError 
            ? theme.colorScheme.error 
            : theme.colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}