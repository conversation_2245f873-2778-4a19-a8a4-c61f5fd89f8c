import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'local_database_service.dart';
import 'local_reward_service.dart';
import '../../shared/models/game_result.dart';
import '../../shared/models/user.dart';

final localGameServiceProvider = Provider<LocalGameService>((ref) {
  final rewardService = ref.read(localRewardServiceProvider);
  return LocalGameService(rewardService);
});

class LocalGameService {
  final LocalDatabaseService _localDb = LocalDatabaseService();
  final LocalRewardService _rewardService;
  final Uuid _uuid = const Uuid();

  LocalGameService(this._rewardService);

  /// Save game result and award points
  Future<void> saveGameResult(GameResult result) async {
    try {
      await _localDb.saveGameResult(result);
      
      // Award completion bonus
      await _rewardService.awardGameCompletionBonus(
        result.userId,
        result.gameType.toString(),
        result.score,
      );

      print('Game result saved and bonus awarded for user: ${result.userId}');
    } catch (e) {
      print('Error saving game result: $e');
      rethrow;
    }
  }

  /// Get user's game results
  Future<List<GameResult>> getUserGameResults(String userId, {GameType? gameType}) async {
    try {
      final allResults = await _localDb.getGameResults(userId);
      
      if (gameType != null) {
        return allResults.where((result) => result.gameType == gameType).toList();
      }
      
      return allResults;
    } catch (e) {
      print('Error getting user game results: $e');
      return [];
    }
  }

  /// Get user's best score for a game type
  Future<int> getUserBestScore(String userId, GameType gameType) async {
    try {
      final results = await getUserGameResults(userId, gameType: gameType);
      if (results.isEmpty) return 0;
      
      return results.map((r) => r.score).reduce((a, b) => a > b ? a : b);
    } catch (e) {
      print('Error getting user best score: $e');
      return 0;
    }
  }

  /// Get user's average accuracy for a game type
  Future<double> getUserAverageAccuracy(String userId, GameType gameType) async {
    try {
      final results = await getUserGameResults(userId, gameType: gameType);
      if (results.isEmpty) return 0.0;
      
      final totalAccuracy = results.map((r) => r.accuracy).reduce((a, b) => a + b);
      return totalAccuracy / results.length;
    } catch (e) {
      print('Error getting user average accuracy: $e');
      return 0.0;
    }
  }

  /// Get user's game statistics
  Future<Map<GameType, GameStats>> getUserGameStats(String userId) async {
    try {
      final results = await getUserGameResults(userId);
      final Map<GameType, GameStats> gameStats = {};

      for (final gameType in GameType.values) {
        final gameResults = results.where((r) => r.gameType == gameType).toList();
        
        if (gameResults.isNotEmpty) {
          final bestScore = gameResults.map((r) => r.score).reduce((a, b) => a > b ? a : b);
          final totalAccuracy = gameResults.map((r) => r.accuracy).reduce((a, b) => a + b);
          final averageAccuracy = totalAccuracy / gameResults.length;
          final totalPlayTime = gameResults.map((r) => r.duration).reduce((a, b) => a + b);
          final lastPlayedAt = gameResults.map((r) => r.timestamp).reduce((a, b) => a.isAfter(b) ? a : b);

          gameStats[gameType] = GameStats(
            gamesPlayed: gameResults.length,
            bestScore: bestScore,
            averageAccuracy: averageAccuracy,
            totalPlayTime: totalPlayTime,
            lastPlayedAt: lastPlayedAt,
            totalXP: gameResults.length * 10, // Simple XP calculation
          );
        }
      }

      return gameStats;
    } catch (e) {
      print('Error getting user game stats: $e');
      return {};
    }
  }

  /// Update user statistics after game completion
  Future<void> updateUserStats(String userId) async {
    try {
      final user = await _localDb.getUser(userId);
      if (user == null) return;

      final gameStats = await getUserGameStats(userId);
      final results = await getUserGameResults(userId);
      
      final totalGamesPlayed = results.length;
      final totalXP = gameStats.values.fold(0, (sum, stats) => sum + stats.totalXP);
      final level = (totalXP / 100).floor() + 1; // 100 XP per level
      
      // Calculate average accuracy across all games
      final totalAccuracy = results.isNotEmpty 
          ? results.map((r) => r.accuracy).reduce((a, b) => a + b) / results.length
          : 0.0;

      // Calculate total play time
      final totalPlayTime = results.isNotEmpty
          ? results.map((r) => r.duration).reduce((a, b) => a + b)
          : Duration.zero;

      final updatedStats = user.stats.copyWith(
        totalGamesPlayed: totalGamesPlayed,
        gameStats: gameStats,
        totalXP: totalXP,
        level: level,
        averageAccuracy: totalAccuracy,
        totalPlayTime: totalPlayTime,
        lastGamePlayedAt: results.isNotEmpty 
            ? results.map((r) => r.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      );

      final updatedUser = user.copyWith(stats: updatedStats);
      await _localDb.updateUser(updatedUser);

      print('User stats updated for user: $userId');
    } catch (e) {
      print('Error updating user stats: $e');
    }
  }

  /// Get user's recent games (last 10)
  Future<List<GameResult>> getUserRecentGames(String userId, {int limit = 10}) async {
    try {
      final results = await getUserGameResults(userId);
      results.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return results.take(limit).toList();
    } catch (e) {
      print('Error getting user recent games: $e');
      return [];
    }
  }

  /// Check if user has played today
  Future<bool> hasPlayedToday(String userId) async {
    try {
      final results = await getUserGameResults(userId);
      final today = DateTime.now();
      
      return results.any((result) {
        final resultDate = result.timestamp;
        return resultDate.year == today.year &&
               resultDate.month == today.month &&
               resultDate.day == today.day;
      });
    } catch (e) {
      print('Error checking if user played today: $e');
      return false;
    }
  }

  /// Get daily game count for user
  Future<int> getDailyGameCount(String userId, {DateTime? date}) async {
    try {
      final targetDate = date ?? DateTime.now();
      final results = await getUserGameResults(userId);
      
      return results.where((result) {
        final resultDate = result.timestamp;
        return resultDate.year == targetDate.year &&
               resultDate.month == targetDate.month &&
               resultDate.day == targetDate.day;
      }).length;
    } catch (e) {
      print('Error getting daily game count: $e');
      return 0;
    }
  }

  /// Calculate user's current streak
  Future<int> calculateCurrentStreak(String userId) async {
    try {
      final results = await getUserGameResults(userId);
      if (results.isEmpty) return 0;

      // Group results by day
      final Map<DateTime, List<GameResult>> resultsByDay = {};
      for (final result in results) {
        final day = DateTime(result.timestamp.year, result.timestamp.month, result.timestamp.day);
        resultsByDay.putIfAbsent(day, () => []).add(result);
      }

      // Sort days in descending order
      final sortedDays = resultsByDay.keys.toList()..sort((a, b) => b.compareTo(a));
      
      int streak = 0;
      DateTime? lastDay;
      
      for (final day in sortedDays) {
        if (lastDay == null) {
          // First day (most recent)
          lastDay = day;
          streak = 1;
        } else {
          // Check if this day is consecutive to the last day
          final difference = lastDay.difference(day).inDays;
          if (difference == 1) {
            streak++;
            lastDay = day;
          } else {
            break; // Streak broken
          }
        }
      }

      return streak;
    } catch (e) {
      print('Error calculating current streak: $e');
      return 0;
    }
  }

  /// Create a game result
  GameResult createGameResult({
    required String userId,
    required GameType gameType,
    required int level,
    required int score,
    required double accuracy,
    required Duration duration,
    Map<String, dynamic> gameSpecificData = const {},
  }) {
    return GameResult(
      id: _uuid.v4(),
      userId: userId,
      gameType: gameType,
      level: level,
      score: score,
      accuracy: accuracy,
      duration: duration,
      timestamp: DateTime.now(),
      gameSpecificData: gameSpecificData,
    );
  }
}