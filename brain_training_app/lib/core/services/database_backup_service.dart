import 'dart:io';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseBackupService {
  static const String _backupDir = 'database_backups';
  static const int _maxBackups = 10;

  Future<String> createBackup(Database db, String reason) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupFileName = 'backup_${timestamp}_$reason.db';
      
      final backupDir = await _getBackupDirectory();
      final backupPath = join(backupDir.path, backupFileName);
      
      // SQLite database file path
      final dbPath = db.path;
      
      // Copy database file
      final dbFile = File(dbPath);
      await dbFile.copy(backupPath);
      
      // Create metadata file
      final metadata = {
        'timestamp': timestamp,
        'reason': reason,
        'original_path': dbPath,
        'backup_path': backupPath,
        'database_version': await _getDatabaseVersion(db),
        'table_count': await _getTableCount(db),
      };
      
      final metadataPath = '$backupPath.meta';
      await File(metadataPath).writeAsString(jsonEncode(metadata));
      
      // Clean old backups
      await _cleanOldBackups();
      
      print('[DatabaseBackup] Created backup: $backupPath');
      return backupPath;
    } catch (e) {
      print('[DatabaseBackup] Error creating backup: $e');
      throw DatabaseBackupException('Failed to create backup: $e');
    }
  }

  Future<void> restoreBackup(String backupPath, String targetDbPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw DatabaseBackupException('Backup file not found: $backupPath');
      }
      
      // Read metadata
      final metadataFile = File('$backupPath.meta');
      if (await metadataFile.exists()) {
        final metadataJson = await metadataFile.readAsString();
        final metadata = jsonDecode(metadataJson);
        print('[DatabaseBackup] Restoring backup from ${DateTime.fromMillisecondsSinceEpoch(metadata['timestamp'])}');
      }
      
      // Close current database connection before restoring
      final targetFile = File(targetDbPath);
      if (await targetFile.exists()) {
        final currentBackupPath = await createBackup(
          await openDatabase(targetDbPath), 
          'pre_restore'
        );
        print('[DatabaseBackup] Created safety backup before restore: $currentBackupPath');
      }
      
      // Copy backup to target location
      await backupFile.copy(targetDbPath);
      
      print('[DatabaseBackup] Successfully restored backup to: $targetDbPath');
    } catch (e) {
      print('[DatabaseBackup] Error restoring backup: $e');
      throw DatabaseBackupException('Failed to restore backup: $e');
    }
  }

  Future<List<BackupInfo>> listBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      if (!await backupDir.exists()) {
        return [];
      }
      
      final files = await backupDir.list().toList();
      final backupFiles = files
          .where((file) => file.path.endsWith('.db'))
          .cast<File>();
      
      final backups = <BackupInfo>[];
      
      for (final file in backupFiles) {
        final metadataFile = File('${file.path}.meta');
        if (await metadataFile.exists()) {
          try {
            final metadataJson = await metadataFile.readAsString();
            final metadata = jsonDecode(metadataJson);
            
            backups.add(BackupInfo(
              path: file.path,
              timestamp: DateTime.fromMillisecondsSinceEpoch(metadata['timestamp']),
              reason: metadata['reason'],
              databaseVersion: metadata['database_version'],
              tableCount: metadata['table_count'],
              fileSize: await file.length(),
            ));
          } catch (e) {
            print('[DatabaseBackup] Error reading metadata for ${file.path}: $e');
          }
        }
      }
      
      backups.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return backups;
    } catch (e) {
      print('[DatabaseBackup] Error listing backups: $e');
      return [];
    }
  }

  Future<void> deleteBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      final metadataFile = File('$backupPath.meta');
      
      if (await backupFile.exists()) {
        await backupFile.delete();
      }
      
      if (await metadataFile.exists()) {
        await metadataFile.delete();
      }
      
      print('[DatabaseBackup] Deleted backup: $backupPath');
    } catch (e) {
      print('[DatabaseBackup] Error deleting backup: $e');
      throw DatabaseBackupException('Failed to delete backup: $e');
    }
  }

  Future<bool> verifyBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        return false;
      }
      
      // Try to open the backup database to verify integrity
      final tempDb = await openDatabase(
        backupPath,
        readOnly: true,
      );
      
      // Perform basic integrity check
      final result = await tempDb.rawQuery('PRAGMA integrity_check');
      await tempDb.close();
      
      return result.isNotEmpty && result.first.values.first == 'ok';
    } catch (e) {
      print('[DatabaseBackup] Backup verification failed: $e');
      return false;
    }
  }

  Future<Directory> _getBackupDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory(join(appDir.path, _backupDir));
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    
    return backupDir;
  }

  Future<void> _cleanOldBackups() async {
    try {
      final backups = await listBackups();
      
      if (backups.length > _maxBackups) {
        final backupsToDelete = backups.skip(_maxBackups);
        
        for (final backup in backupsToDelete) {
          await deleteBackup(backup.path);
        }
        
        print('[DatabaseBackup] Cleaned ${backupsToDelete.length} old backups');
      }
    } catch (e) {
      print('[DatabaseBackup] Error cleaning old backups: $e');
    }
  }

  Future<int> _getDatabaseVersion(Database db) async {
    final result = await db.rawQuery('PRAGMA user_version');
    return result.first['user_version'] as int;
  }

  Future<int> _getTableCount(Database db) async {
    final result = await db.rawQuery(
      "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'"
    );
    return result.first['count'] as int;
  }
}

class BackupInfo {
  final String path;
  final DateTime timestamp;
  final String reason;
  final int databaseVersion;
  final int tableCount;
  final int fileSize;

  BackupInfo({
    required this.path,
    required this.timestamp,
    required this.reason,
    required this.databaseVersion,
    required this.tableCount,
    required this.fileSize,
  });

  String get formattedSize {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

class DatabaseBackupException implements Exception {
  final String message;
  DatabaseBackupException(this.message);
  
  @override
  String toString() => 'DatabaseBackupException: $message';
}