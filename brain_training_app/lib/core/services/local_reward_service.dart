import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'local_database_service.dart';
import '../../shared/models/reward_point.dart';

final localRewardServiceProvider = Provider<LocalRewardService>((ref) {
  return LocalRewardService();
});

class LocalRewardService {
  final LocalDatabaseService _localDb = LocalDatabaseService();
  final Uuid _uuid = const Uuid();

  /// Award points to user
  Future<void> awardPoints(String userId, int points, String reason) async {
    try {
      final rewardPoint = RewardPoint(
        id: _uuid.v4(),
        userId: userId,
        points: points,
        reason: reason,
        earnedAt: DateTime.now(),
      );

      await _localDb.saveRewardPoint(rewardPoint);

      // Update reward transaction
      final transaction = RewardTransaction(
        id: _uuid.v4(),
        userId: userId,
        type: RewardTransactionType.earned,
        amount: points,
        description: reason,
        timestamp: DateTime.now(),
      );

      await _localDb.saveRewardTransaction(transaction);

      // Update balance
      await _updateBalance(userId);
    } catch (e) {
      print('Error awarding points: $e');
      rethrow;
    }
  }

  /// Spend points
  Future<bool> spendPoints(String userId, int points, String description) async {
    try {
      final balance = await getBalance(userId);
      if (balance.availablePoints < points) {
        return false; // Insufficient points
      }

      final transaction = RewardTransaction(
        id: _uuid.v4(),
        userId: userId,
        type: RewardTransactionType.spent,
        amount: points,
        description: description,
        timestamp: DateTime.now(),
      );

      await _localDb.saveRewardTransaction(transaction);
      await _updateBalance(userId);
      
      return true;
    } catch (e) {
      print('Error spending points: $e');
      return false;
    }
  }

  /// Get user's reward balance
  Future<RewardBalance> getBalance(String userId) async {
    try {
      final balance = await _localDb.getRewardBalance(userId);
      if (balance != null) {
        return balance;
      }

      // Create initial balance if doesn't exist
      final newBalance = RewardBalance(
        userId: userId,
        totalPoints: 0,
        spentPoints: 0,
        availablePoints: 0,
        updatedAt: DateTime.now(),
      );

      await _localDb.saveRewardBalance(newBalance);
      return newBalance;
    } catch (e) {
      print('Error getting balance: $e');
      return RewardBalance(
        userId: userId,
        totalPoints: 0,
        spentPoints: 0,
        availablePoints: 0,
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Get user's reward transaction history
  Future<List<RewardTransaction>> getTransactionHistory(String userId) async {
    try {
      return await _localDb.getRewardTransactions(userId);
    } catch (e) {
      print('Error getting transaction history: $e');
      return [];
    }
  }

  /// Get user's earned reward points
  Future<List<RewardPoint>> getRewardPoints(String userId) async {
    try {
      return await _localDb.getRewardPoints(userId);
    } catch (e) {
      print('Error getting reward points: $e');
      return [];
    }
  }

  /// Update user's reward balance
  Future<void> _updateBalance(String userId) async {
    try {
      final transactions = await _localDb.getRewardTransactions(userId);
      
      int totalEarned = 0;
      int totalSpent = 0;

      for (final transaction in transactions) {
        if (transaction.type == RewardTransactionType.earned) {
          totalEarned += transaction.amount;
        } else {
          totalSpent += transaction.amount;
        }
      }

      final balance = RewardBalance(
        userId: userId,
        totalPoints: totalEarned,
        spentPoints: totalSpent,
        availablePoints: totalEarned - totalSpent,
        updatedAt: DateTime.now(),
      );

      await _localDb.saveRewardBalance(balance);
    } catch (e) {
      print('Error updating balance: $e');
      rethrow;
    }
  }

  /// Daily login bonus
  Future<void> awardDailyLoginBonus(String userId) async {
    const dailyLoginPoints = 10;
    const reason = '일일 로그인 보너스';
    
    await awardPoints(userId, dailyLoginPoints, reason);
  }

  /// Game completion bonus
  Future<void> awardGameCompletionBonus(String userId, String gameType, int score) async {
    int bonusPoints = 5; // Base points
    
    // Bonus for high scores
    if (score >= 800) {
      bonusPoints += 5;
    } else if (score >= 600) {
      bonusPoints += 3;
    }

    final reason = '$gameType 게임 완료 보너스 (점수: $score)';
    await awardPoints(userId, bonusPoints, reason);
  }

  /// Streak bonus
  Future<void> awardStreakBonus(String userId, int streakDays) async {
    if (streakDays % 7 == 0) { // Weekly streak
      const weeklyBonusPoints = 50;
      final reason = '연속 로그인 $streakDays일 달성 보너스';
      await awardPoints(userId, weeklyBonusPoints, reason);
    }
  }

  /// Check if user can purchase item
  Future<bool> canPurchase(String userId, int cost) async {
    final balance = await getBalance(userId);
    return balance.availablePoints >= cost;
  }

  /// Purchase flashcard storage expansion
  Future<bool> purchaseStorageExpansion(String userId, int additionalSlots, int cost) async {
    if (await canPurchase(userId, cost)) {
      final success = await spendPoints(userId, cost, '플래시카드 저장소 확장 (+$additionalSlots 슬롯)');
      if (success) {
        // Save unlock data
        final unlock = UserUnlock(
          id: _uuid.v4(),
          userId: userId,
          unlockType: UnlockType.storageExpansion,
          unlockData: {'additionalSlots': additionalSlots},
          unlockedAt: DateTime.now(),
        );
        
        await _localDb.saveUserUnlock(unlock);
        return true;
      }
    }
    return false;
  }

  /// Get user's unlocked storage expansions
  Future<int> getStorageExpansionSlots(String userId) async {
    try {
      final unlocks = await _localDb.getUserUnlocks(userId);
      int totalAdditionalSlots = 0;

      for (final unlock in unlocks) {
        if (unlock.unlockType == UnlockType.storageExpansion) {
          final slots = unlock.unlockData['additionalSlots'] as int? ?? 0;
          totalAdditionalSlots += slots;
        }
      }

      return totalAdditionalSlots;
    } catch (e) {
      print('Error getting storage expansion slots: $e');
      return 0;
    }
  }

  /// Calculate user's total flashcard storage limit
  Future<int> getTotalStorageLimit(String userId) async {
    const baseLimit = 100; // Base limit for all users
    final expansionSlots = await getStorageExpansionSlots(userId);
    return baseLimit + expansionSlots;
  }
}