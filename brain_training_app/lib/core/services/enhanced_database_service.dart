import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'database_service.dart';
import 'database_migration_manager.dart';
import 'database_backup_service.dart';
import 'database_integrity_service.dart';

// Enhanced provider with additional database management capabilities
final enhancedDatabaseServiceProvider = Provider<EnhancedDatabaseService>((ref) {
  return EnhancedDatabaseService(ref.read(databaseServiceProvider));
});

class EnhancedDatabaseService {
  final DatabaseService _databaseService;
  
  EnhancedDatabaseService(this._databaseService);
  
  // Delegate all existing database operations
  Future<void> saveUser(dynamic user) => _databaseService.saveUser(user);
  Future<dynamic> getUser(String userId) => _databaseService.getUser(userId);
  Future<void> updateUserStats(String userId, dynamic stats) => _databaseService.updateUserStats(userId, stats);
  Future<void> updateUserPreferences(String userId, dynamic preferences) => _databaseService.updateUserPreferences(userId, preferences);
  
  Future<void> saveGameSession(dynamic session) => _databaseService.saveGameSession(session);
  Future<List<dynamic>> getGameSessions(String userId, {dynamic gameType}) => _databaseService.getGameSessions(userId, gameType: gameType);
  
  Future<List<dynamic>> getAllAchievements() => _databaseService.getAllAchievements();
  Future<List<dynamic>> getUserAchievements(String userId) => _databaseService.getUserAchievements(userId);
  Future<void> unlockAchievement(String userId, String achievementId) => _databaseService.unlockAchievement(userId, achievementId);
  
  Future<void> saveCardDeck(dynamic deck) => _databaseService.saveCardDeck(deck);
  Future<List<dynamic>> getCardDecks(String userId) => _databaseService.getCardDecks(userId);
  Future<dynamic> getCardDeck(String deckId) => _databaseService.getCardDeck(deckId);
  Future<void> saveFlashCard(dynamic card) => _databaseService.saveFlashCard(card);
  Future<List<dynamic>> getFlashCards(String deckId) => _databaseService.getFlashCards(deckId);
  Future<List<dynamic>> getAllFlashCards() => _databaseService.getAllFlashCards();
  Future<void> deleteCardDeck(String deckId) => _databaseService.deleteCardDeck(deckId);
  Future<void> deleteFlashCard(String cardId) => _databaseService.deleteFlashCard(cardId);
  
  Future<void> saveUserAchievement(dynamic achievement) => _databaseService.saveUserAchievement(achievement);
  Future<dynamic> getUserStats(String userId) => _databaseService.getUserStats(userId);
  
  Future<void> saveRewardPoint(dynamic point) => _databaseService.saveRewardPoint(point);
  Future<List<dynamic>> getRewardPoints(String userId) => _databaseService.getRewardPoints(userId);
  Future<void> saveRewardTransaction(dynamic transaction) => _databaseService.saveRewardTransaction(transaction);
  Future<List<dynamic>> getRewardTransactions(String userId) => _databaseService.getRewardTransactions(userId);
  Future<void> saveRewardBalance(dynamic balance) => _databaseService.saveRewardBalance(balance);
  Future<dynamic> getRewardBalance(String userId) => _databaseService.getRewardBalance(userId);
  Future<void> saveUserUnlock(dynamic unlock) => _databaseService.saveUserUnlock(unlock);
  Future<List<dynamic>> getUserUnlocks(String userId) => _databaseService.getUserUnlocks(userId);
  
  Future<void> close() => _databaseService.close();
  
  // Enhanced database management features
  Future<DatabaseHealth> checkDatabaseHealth() => _databaseService.checkDatabaseHealth();
  Future<List<BackupInfo>> listBackups() => _databaseService.listBackups();
  Future<String> createManualBackup(String reason) => _databaseService.createManualBackup(reason);
  Future<void> restoreFromBackup(String backupPath) => _databaseService.restoreFromBackup(backupPath);
  Future<bool> repairDatabase() => _databaseService.repairDatabase();
  Future<List<MigrationLogEntry>> getMigrationHistory() => _databaseService.getMigrationHistory();
  Future<bool> isDatabaseUpToDate() => _databaseService.isDatabaseUpToDate();
  
  // Additional convenience methods
  Future<DatabaseMaintenanceReport> performDatabaseMaintenance() async {
    final report = DatabaseMaintenanceReport();
    
    try {
      // Check database health
      final health = await checkDatabaseHealth();
      report.healthCheck = health;
      
      // Create maintenance backup
      final backupPath = await createManualBackup('maintenance_${DateTime.now().millisecondsSinceEpoch}');
      report.backupCreated = backupPath;
      
      // Repair if needed
      if (health.needsAttention) {
        final repairSuccess = await repairDatabase();
        report.repairPerformed = repairSuccess;
        
        if (repairSuccess) {
          // Re-check health after repair
          report.healthCheckAfterRepair = await checkDatabaseHealth();
        }
      }
      
      // Clean old backups (keep only the most recent 10)
      final backups = await listBackups();
      if (backups.length > 10) {
        final backupsToDelete = backups.skip(10);
        final backupService = DatabaseBackupService();
        
        for (final backup in backupsToDelete) {
          try {
            await backupService.deleteBackup(backup.path);
            report.backupsDeleted++;
          } catch (e) {
            report.errors.add('Failed to delete backup ${backup.path}: $e');
          }
        }
      }
      
      report.success = true;
      
    } catch (e) {
      report.success = false;
      report.errors.add('Maintenance failed: $e');
    }
    
    report.completedAt = DateTime.now();
    return report;
  }
  
  Future<DatabaseAnalytics> getDatabaseAnalytics() async {
    final health = await checkDatabaseHealth();
    final migrationHistory = await getMigrationHistory();
    final backups = await listBackups();
    
    return DatabaseAnalytics(
      health: health,
      migrationHistory: migrationHistory,
      backupCount: backups.length,
      isUpToDate: await isDatabaseUpToDate(),
      lastMaintenanceDate: backups.isNotEmpty ? backups.first.timestamp : null,
    );
  }
}

class DatabaseMaintenanceReport {
  bool success = false;
  DatabaseHealth? healthCheck;
  DatabaseHealth? healthCheckAfterRepair;
  String? backupCreated;
  bool? repairPerformed;
  int backupsDeleted = 0;
  final List<String> errors = [];
  DateTime? completedAt;
  
  String get summary {
    if (!success) {
      return 'Maintenance failed: ${errors.join(', ')}';
    }
    
    final parts = <String>[];
    if (healthCheck != null) {
      parts.add('Health: ${healthCheck!.level.name}');
    }
    if (repairPerformed == true) {
      parts.add('Repair performed');
    }
    if (backupsDeleted > 0) {
      parts.add('$backupsDeleted old backups removed');
    }
    
    return 'Maintenance completed: ${parts.join(', ')}';
  }
}

class DatabaseAnalytics {
  final DatabaseHealth health;
  final List<MigrationLogEntry> migrationHistory;
  final int backupCount;
  final bool isUpToDate;
  final DateTime? lastMaintenanceDate;
  
  DatabaseAnalytics({
    required this.health,
    required this.migrationHistory,
    required this.backupCount,
    required this.isUpToDate,
    this.lastMaintenanceDate,
  });
  
  bool get needsAttention => health.needsAttention || !isUpToDate;
  
  String get statusSummary {
    final issues = <String>[];
    
    if (!isUpToDate) {
      issues.add('Database version outdated');
    }
    
    if (health.needsAttention) {
      issues.add('Health issues detected');
    }
    
    if (backupCount == 0) {
      issues.add('No backups available');
    }
    
    if (issues.isEmpty) {
      return 'Database is healthy and up to date';
    }
    
    return 'Issues: ${issues.join(', ')}';
  }
}