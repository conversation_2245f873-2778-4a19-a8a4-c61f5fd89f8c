import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'local_reward_service.dart';
import 'storage_service.dart';
import '../constants/app_constants.dart';

final dailyLoginServiceProvider = Provider<DailyLoginService>((ref) {
  return DailyLoginService(
    rewardService: ref.read(localRewardServiceProvider),
    storageService: ref.read(storageServiceProvider),
  );
});

class DailyLoginService {
  final LocalRewardService _rewardService;
  final StorageService _storageService;

  DailyLoginService({
    required LocalRewardService rewardService,
    required StorageService storageService,
  }) : _rewardService = rewardService,
       _storageService = storageService;

  /// Check and award daily login bonus if eligible
  Future<bool> checkAndAwardDailyLoginBonus(String userId) async {
    try {
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      
      // Check if user already got login bonus today
      final lastLoginDate = await _storageService.getLastLoginDate(userId);
      if (lastLoginDate == todayString) {
        return false; // Already got bonus today
      }

      // Award daily login bonus
      final rewardPoint = await _rewardService.awardDailyLoginBonus(userId);
      if (rewardPoint != null) {
        // Save the login date
        await _storageService.setLastLoginDate(userId, todayString);
        
        // Update streak
        await _updateLoginStreak(userId, lastLoginDate, todayString);
        
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error checking daily login bonus: $e');
      return false;
    }
  }

  /// Get current login streak for user
  Future<int> getLoginStreak(String userId) async {
    try {
      return await _storageService.getLoginStreak(userId);
    } catch (e) {
      print('Error getting login streak: $e');
      return 0;
    }
  }

  /// Check if user is eligible for streak bonus
  Future<bool> isEligibleForStreakBonus(String userId) async {
    try {
      final streak = await getLoginStreak(userId);
      // Award streak bonus every 7 days
      return streak > 0 && streak % 7 == 0;
    } catch (e) {
      print('Error checking streak bonus eligibility: $e');
      return false;
    }
  }

  /// Award streak bonus if eligible
  Future<bool> awardStreakBonus(String userId) async {
    try {
      if (await isEligibleForStreakBonus(userId)) {
        final streak = await getLoginStreak(userId);
        final bonusAmount = (streak / 7 * 50).round(); // 50 points per week
        
        await _rewardService.awardPoints(
          userId: userId,
          sourceName: 'streak_bonus',
          context: {'streak': streak, 'bonus_amount': bonusAmount},
        );
        
        return true;
      }
      return false;
    } catch (e) {
      print('Error awarding streak bonus: $e');
      return false;
    }
  }

  Future<void> _updateLoginStreak(String userId, String? lastLoginDate, String todayString) async {
    try {
      if (lastLoginDate == null) {
        // First login
        await _storageService.setLoginStreak(userId, 1);
        return;
      }

      // Parse dates
      final lastDate = DateTime.tryParse(lastLoginDate.replaceAll('-', ''));
      final today = DateTime.tryParse(todayString.replaceAll('-', ''));
      
      if (lastDate == null || today == null) {
        await _storageService.setLoginStreak(userId, 1);
        return;
      }

      final daysDifference = today.difference(lastDate).inDays;
      
      if (daysDifference == 1) {
        // Consecutive day - increment streak
        final currentStreak = await _storageService.getLoginStreak(userId);
        await _storageService.setLoginStreak(userId, currentStreak + 1);
      } else {
        // Streak broken - reset to 1
        await _storageService.setLoginStreak(userId, 1);
      }
    } catch (e) {
      print('Error updating login streak: $e');
      await _storageService.setLoginStreak(userId, 1);
    }
  }
}