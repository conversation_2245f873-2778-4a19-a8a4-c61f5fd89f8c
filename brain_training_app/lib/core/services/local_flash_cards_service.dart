import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'local_database_service.dart';
import 'local_reward_service.dart';
import '../../shared/models/flash_card.dart';

final localFlashCardsServiceProvider = Provider<LocalFlashCardsService>((ref) {
  final rewardService = ref.read(localRewardServiceProvider);
  return LocalFlashCardsService(rewardService);
});

class LocalFlashCardsService {
  final LocalDatabaseService _localDb = LocalDatabaseService();
  final LocalRewardService _rewardService;
  final Uuid _uuid = const Uuid();

  LocalFlashCardsService(this._rewardService);

  /// Create a new card deck
  Future<CardDeck> createDeck({
    required String userId,
    required String name,
    required String description,
    String? category,
  }) async {
    try {
      final deck = CardDeck(
        id: _uuid.v4(),
        userId: userId,
        name: name,
        description: description,
        category: category,
        createdAt: DateTime.now(),
      );

      await _localDb.saveCardDeck(deck);
      return deck;
    } catch (e) {
      print('Error creating deck: $e');
      rethrow;
    }
  }

  /// Get user's card decks
  Future<List<CardDeck>> getUserDecks(String userId) async {
    try {
      return await _localDb.getCardDecks(userId);
    } catch (e) {
      print('Error getting user decks: $e');
      return [];
    }
  }

  /// Get deck by ID
  Future<CardDeck?> getDeck(String deckId) async {
    try {
      return await _localDb.getCardDeck(deckId);
    } catch (e) {
      print('Error getting deck: $e');
      return null;
    }
  }

  /// Delete a deck
  Future<void> deleteDeck(String deckId) async {
    try {
      await _localDb.deleteCardDeck(deckId);
    } catch (e) {
      print('Error deleting deck: $e');
      rethrow;
    }
  }

  /// Create a new flash card
  Future<FlashCard> createCard({
    required String deckId,
    required String front,
    required String back,
    String? hint,
    List<String> tags = const [],
  }) async {
    try {
      final card = FlashCard(
        id: _uuid.v4(),
        deckId: deckId,
        front: front,
        back: back,
        hint: hint,
        tags: tags,
        createdAt: DateTime.now(),
        review: const CardReview(),
      );

      await _localDb.saveFlashCard(card);
      return card;
    } catch (e) {
      print('Error creating card: $e');
      rethrow;
    }
  }

  /// Get cards in a deck
  Future<List<FlashCard>> getDeckCards(String deckId) async {
    try {
      return await _localDb.getFlashCards(deckId);
    } catch (e) {
      print('Error getting deck cards: $e');
      return [];
    }
  }

  /// Get all user's cards across all decks
  Future<List<FlashCard>> getAllUserCards(String userId) async {
    try {
      return await _localDb.getAllUserFlashCards(userId);
    } catch (e) {
      print('Error getting all user cards: $e');
      return [];
    }
  }

  /// Update a flash card
  Future<void> updateCard(FlashCard card) async {
    try {
      await _localDb.saveFlashCard(card);
    } catch (e) {
      print('Error updating card: $e');
      rethrow;
    }
  }

  /// Delete a flash card
  Future<void> deleteCard(String cardId) async {
    try {
      await _localDb.deleteFlashCard(cardId);
    } catch (e) {
      print('Error deleting card: $e');
      rethrow;
    }
  }

  /// Get cards due for review
  Future<List<FlashCard>> getCardsForReview(String userId, {int limit = 20}) async {
    try {
      final allCards = await getAllUserCards(userId);
      final now = DateTime.now();
      
      final dueCards = allCards.where((card) {
        final nextReview = card.review.nextReviewDate;
        return nextReview == null || nextReview.isBefore(now);
      }).toList();

      // Sort by priority (cards overdue first, then by review date)
      dueCards.sort((a, b) {
        final aNext = a.review.nextReviewDate;
        final bNext = b.review.nextReviewDate;
        
        if (aNext == null && bNext == null) return 0;
        if (aNext == null) return -1;
        if (bNext == null) return 1;
        
        return aNext.compareTo(bNext);
      });

      return dueCards.take(limit).toList();
    } catch (e) {
      print('Error getting cards for review: $e');
      return [];
    }
  }

  /// Review a card and update its review data
  Future<void> reviewCard(FlashCard card, CardQuality quality) async {
    try {
      final now = DateTime.now();
      
      // Calculate next review using spaced repetition
      final newReview = _calculateNextReview(card.review, quality, now);
      
      final updatedCard = card.copyWith(
        review: newReview,
        updatedAt: now,
      );

      await updateCard(updatedCard);
    } catch (e) {
      print('Error reviewing card: $e');
      rethrow;
    }
  }

  /// Calculate next review using spaced repetition algorithm
  CardReview _calculateNextReview(CardReview currentReview, CardQuality quality, DateTime reviewDate) {
    double easeFactor = currentReview.easeFactor;
    int interval = currentReview.interval;
    int repetitions = currentReview.repetitions;

    if (quality.index >= CardQuality.hard.index) {
      // Correct response
      if (repetitions == 0) {
        interval = 1;
      } else if (repetitions == 1) {
        interval = 6;
      } else {
        interval = (interval * easeFactor).round();
      }
      repetitions++;
    } else {
      // Incorrect response
      repetitions = 0;
      interval = 1;
    }

    // Update ease factor
    easeFactor = easeFactor + (0.1 - (5 - quality.index) * (0.08 + (5 - quality.index) * 0.02));
    easeFactor = easeFactor.clamp(1.3, 2.5);

    final nextReviewDate = reviewDate.add(Duration(days: interval));

    return CardReview(
      easeFactor: easeFactor,
      interval: interval,
      repetitions: repetitions,
      nextReviewDate: nextReviewDate,
      lastReviewedAt: reviewDate,
      lastQuality: quality,
    );
  }

  /// Get storage usage for user
  Future<StorageUsage> getStorageUsage(String userId) async {
    try {
      final allCards = await getAllUserCards(userId);
      final totalLimit = await _rewardService.getTotalStorageLimit(userId);
      
      final currentCount = allCards.length;
      final remainingCount = (totalLimit - currentCount).clamp(0, totalLimit);
      final usagePercentage = totalLimit > 0 ? (currentCount / totalLimit) : 0.0;

      return StorageUsage(
        currentCount: currentCount,
        limit: totalLimit,
        remainingCount: remainingCount,
        usagePercentage: usagePercentage,
      );
    } catch (e) {
      print('Error getting storage usage: $e');
      return const StorageUsage(
        currentCount: 0,
        limit: 100,
        remainingCount: 100,
        usagePercentage: 0.0,
      );
    }
  }

  /// Check if user can add more cards
  Future<bool> canAddCard(String userId) async {
    try {
      final usage = await getStorageUsage(userId);
      return usage.remainingCount > 0;
    } catch (e) {
      print('Error checking if user can add card: $e');
      return false;
    }
  }

  /// Get deck statistics
  Future<DeckStatistics> getDeckStatistics(String deckId) async {
    try {
      final cards = await getDeckCards(deckId);
      final now = DateTime.now();
      
      int newCards = 0;
      int dueCards = 0;
      int learnedCards = 0;
      double averageEaseFactor = 0.0;
      
      for (final card in cards) {
        if (card.review.repetitions == 0) {
          newCards++;
        } else {
          final nextReview = card.review.nextReviewDate;
          if (nextReview != null && nextReview.isBefore(now)) {
            dueCards++;
          } else {
            learnedCards++;
          }
        }
        averageEaseFactor += card.review.easeFactor;
      }
      
      if (cards.isNotEmpty) {
        averageEaseFactor /= cards.length;
      }

      return DeckStatistics(
        totalCards: cards.length,
        newCards: newCards,
        dueCards: dueCards,
        learnedCards: learnedCards,
        averageEaseFactor: averageEaseFactor,
      );
    } catch (e) {
      print('Error getting deck statistics: $e');
      return const DeckStatistics(
        totalCards: 0,
        newCards: 0,
        dueCards: 0,
        learnedCards: 0,
        averageEaseFactor: 2.5,
      );
    }
  }

  /// Start a study session
  Future<CardStudySession> startStudySession(String userId, String deckId) async {
    try {
      final session = CardStudySession(
        id: _uuid.v4(),
        userId: userId,
        deckId: deckId,
        startTime: DateTime.now(),
      );

      // Note: For now we just return the session object
      // In a full implementation, you might want to save this to the database
      return session;
    } catch (e) {
      print('Error starting study session: $e');
      rethrow;
    }
  }

  /// End a study session and award points
  Future<void> endStudySession(CardStudySession session) async {
    try {
      final endTime = DateTime.now();
      final duration = endTime.difference(session.startTime);
      
      // Award points based on cards reviewed
      if (session.cardsReviewed > 0) {
        final basePoints = session.cardsReviewed * 2; // 2 points per card
        final accuracyBonus = (session.correctAnswers / session.cardsReviewed * 10).round();
        final totalPoints = basePoints + accuracyBonus;
        
        await _rewardService.awardPoints(
          session.userId,
          totalPoints,
          '플래시카드 학습 완료 (${session.cardsReviewed}장 학습)',
        );
      }

      print('Study session ended: ${session.cardsReviewed} cards reviewed, ${duration.inMinutes} minutes');
    } catch (e) {
      print('Error ending study session: $e');
    }
  }
}

/// Deck statistics model
class DeckStatistics {
  final int totalCards;
  final int newCards;
  final int dueCards;
  final int learnedCards;
  final double averageEaseFactor;

  const DeckStatistics({
    required this.totalCards,
    required this.newCards,
    required this.dueCards,
    required this.learnedCards,
    required this.averageEaseFactor,
  });
}