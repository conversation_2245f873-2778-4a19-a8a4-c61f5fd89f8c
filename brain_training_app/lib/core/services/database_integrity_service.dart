import 'package:sqflite/sqflite.dart';

class DatabaseIntegrityService {
  
  Future<IntegrityCheckResult> performFullIntegrityCheck(Database db) async {
    final result = IntegrityCheckResult();
    
    try {
      // SQLite built-in integrity check
      await _checkSQLiteIntegrity(db, result);
      
      // Foreign key constraints check
      await _checkForeignKeyConstraints(db, result);
      
      // Data consistency checks
      await _checkDataConsistency(db, result);
      
      // Business logic validations
      await _checkBusinessRules(db, result);
      
      // Performance checks
      await _checkPerformanceMetrics(db, result);
      
      result.isValid = result.errors.isEmpty;
      
    } catch (e) {
      result.errors.add('Integrity check failed: $e');
      result.isValid = false;
    }
    
    return result;
  }

  Future<void> _checkSQLiteIntegrity(Database db, IntegrityCheckResult result) async {
    try {
      // Basic integrity check
      final integrityResult = await db.rawQuery('PRAGMA integrity_check');
      if (integrityResult.isEmpty || integrityResult.first.values.first != 'ok') {
        result.errors.add('SQLite integrity check failed');
        return;
      }
      
      // Quick check
      final quickResult = await db.rawQuery('PRAGMA quick_check');
      if (quickResult.isEmpty || quickResult.first.values.first != 'ok') {
        result.warnings.add('SQLite quick check reported issues');
      }
      
      result.checks.add('SQLite integrity: PASSED');
    } catch (e) {
      result.errors.add('SQLite integrity check error: $e');
    }
  }

  Future<void> _checkForeignKeyConstraints(Database db, IntegrityCheckResult result) async {
    try {
      final violations = await db.rawQuery('PRAGMA foreign_key_check');
      
      if (violations.isNotEmpty) {
        for (final violation in violations) {
          result.errors.add('Foreign key violation in table ${violation['table']}: ${violation['msg']}');
        }
      } else {
        result.checks.add('Foreign key constraints: PASSED');
      }
    } catch (e) {
      result.errors.add('Foreign key check error: $e');
    }
  }

  Future<void> _checkDataConsistency(Database db, IntegrityCheckResult result) async {
    try {
      // Check user data consistency
      await _checkUserDataConsistency(db, result);
      
      // Check game sessions consistency
      await _checkGameSessionsConsistency(db, result);
      
      // Check flash cards consistency
      await _checkFlashCardsConsistency(db, result);
      
      // Check reward system consistency
      await _checkRewardSystemConsistency(db, result);
      
    } catch (e) {
      result.errors.add('Data consistency check error: $e');
    }
  }

  Future<void> _checkUserDataConsistency(Database db, IntegrityCheckResult result) async {
    // Check for orphaned user data
    final orphanedSessions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM game_sessions gs 
      LEFT JOIN users u ON gs.user_id = u.id 
      WHERE u.id IS NULL
    ''');
    
    final orphanedCount = orphanedSessions.first['count'] as int;
    if (orphanedCount > 0) {
      result.errors.add('Found $orphanedCount orphaned game sessions');
    } else {
      result.checks.add('User data consistency: PASSED');
    }
    
    // Check for invalid user stats JSON
    final invalidStats = await db.rawQuery('''
      SELECT id FROM users WHERE stats IS NULL OR stats = ''
    ''');
    
    if (invalidStats.isNotEmpty) {
      result.warnings.add('Found ${invalidStats.length} users with invalid stats');
    }
  }

  Future<void> _checkGameSessionsConsistency(Database db, IntegrityCheckResult result) async {
    // Check for sessions with invalid durations
    final invalidDurations = await db.rawQuery('''
      SELECT COUNT(*) as count FROM game_sessions 
      WHERE duration <= 0 OR start_time >= end_time
    ''');
    
    final invalidCount = invalidDurations.first['count'] as int;
    if (invalidCount > 0) {
      result.warnings.add('Found $invalidCount game sessions with invalid durations');
    }
    
    // Check for impossible accuracy values
    final invalidAccuracy = await db.rawQuery('''
      SELECT COUNT(*) as count FROM game_sessions 
      WHERE accuracy < 0 OR accuracy > 1
    ''');
    
    final accuracyCount = invalidAccuracy.first['count'] as int;
    if (accuracyCount > 0) {
      result.errors.add('Found $accuracyCount game sessions with invalid accuracy values');
    } else {
      result.checks.add('Game sessions consistency: PASSED');
    }
  }

  Future<void> _checkFlashCardsConsistency(Database db, IntegrityCheckResult result) async {
    // Check for cards without decks
    final orphanedCards = await db.rawQuery('''
      SELECT COUNT(*) as count FROM flash_cards fc 
      LEFT JOIN card_decks cd ON fc.deck_id = cd.id 
      WHERE cd.id IS NULL
    ''');
    
    final orphanedCount = orphanedCards.first['count'] as int;
    if (orphanedCount > 0) {
      result.errors.add('Found $orphanedCount orphaned flash cards');
    }
    
    // Check for empty card content
    final emptyCards = await db.rawQuery('''
      SELECT COUNT(*) as count FROM flash_cards 
      WHERE front IS NULL OR front = '' OR back IS NULL OR back = ''
    ''');
    
    final emptyCount = emptyCards.first['count'] as int;
    if (emptyCount > 0) {
      result.warnings.add('Found $emptyCount flash cards with empty content');
    }
    
    if (orphanedCount == 0 && emptyCount == 0) {
      result.checks.add('Flash cards consistency: PASSED');
    }
  }

  Future<void> _checkRewardSystemConsistency(Database db, IntegrityCheckResult result) async {
    try {
      // Check if reward_balances table exists (for older database versions)
      final tableExists = await db.rawQuery('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='reward_balances'
      ''');
      
      if (tableExists.isEmpty) {
        result.warnings.add('Reward system tables not found (older database version)');
        return;
      }
      
      // Check reward balance consistency
      final balanceCheck = await db.rawQuery('''
        SELECT rb.user_id, rb.current_balance,
               COALESCE(SUM(CASE WHEN rt.is_earned = 1 THEN rt.amount ELSE 0 END), 0) as total_earned,
               COALESCE(SUM(CASE WHEN rt.is_earned = 0 THEN rt.amount ELSE 0 END), 0) as total_spent
        FROM reward_balances rb
        LEFT JOIN reward_transactions rt ON rb.user_id = rt.user_id
        GROUP BY rb.user_id, rb.current_balance
        HAVING rb.current_balance != (total_earned - total_spent)
      ''');
      
      if (balanceCheck.isNotEmpty) {
        result.errors.add('Found ${balanceCheck.length} users with inconsistent reward balances');
      } else {
        result.checks.add('Reward system consistency: PASSED');
      }
    } catch (e) {
      result.warnings.add('Could not check reward system consistency: $e');
    }
  }

  Future<void> _checkBusinessRules(Database db, IntegrityCheckResult result) async {
    // Check for users with impossible streak values
    final impossibleStreaks = await db.rawQuery('''
      SELECT COUNT(*) as count FROM users 
      WHERE json_extract(stats, '\$.currentStreak') > json_extract(stats, '\$.maxStreak')
    ''');
    
    final streakCount = impossibleStreaks.first['count'] as int;
    if (streakCount > 0) {
      result.errors.add('Found $streakCount users with current streak > max streak');
    }
    
    // Check for negative scores
    final negativeScores = await db.rawQuery('''
      SELECT COUNT(*) as count FROM game_sessions WHERE score < 0
    ''');
    
    final scoreCount = negativeScores.first['count'] as int;
    if (scoreCount > 0) {
      result.warnings.add('Found $scoreCount game sessions with negative scores');
    }
    
    if (streakCount == 0 && scoreCount == 0) {
      result.checks.add('Business rules validation: PASSED');
    }
  }

  Future<void> _checkPerformanceMetrics(Database db, IntegrityCheckResult result) async {
    try {
      // Check database size
      final sizeResult = await db.rawQuery('PRAGMA page_count');
      final pageCount = sizeResult.first['page_count'] as int;
      final pageSizeResult = await db.rawQuery('PRAGMA page_size');
      final pageSize = pageSizeResult.first['page_size'] as int;
      final dbSizeMB = (pageCount * pageSize) / (1024 * 1024);
      
      result.metrics['database_size_mb'] = dbSizeMB;
      
      // Check fragmentation
      final fragmentationResult = await db.rawQuery('PRAGMA freelist_count');
      final freePages = fragmentationResult.first['freelist_count'] as int;
      final fragmentationRatio = freePages / pageCount.toDouble();
      
      result.metrics['fragmentation_ratio'] = fragmentationRatio;
      
      if (fragmentationRatio > 0.2) {
        result.warnings.add('Database fragmentation is high (${(fragmentationRatio * 100).toStringAsFixed(1)}%)');
      }
      
      // Check index usage
      await _analyzeIndexUsage(db, result);
      
      result.checks.add('Performance metrics: COLLECTED');
    } catch (e) {
      result.warnings.add('Could not collect performance metrics: $e');
    }
  }

  Future<void> _analyzeIndexUsage(Database db, IntegrityCheckResult result) async {
    try {
      final indexes = await db.rawQuery('''
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      ''');
      
      result.metrics['custom_indexes_count'] = indexes.length.toDouble();
      
      // Check for tables without indexes (excluding small tables)
      final tablesWithoutIndexes = await db.rawQuery('''
        SELECT t.name, COUNT(i.name) as index_count
        FROM sqlite_master t
        LEFT JOIN sqlite_master i ON t.name = i.tbl_name AND i.type = 'index'
        WHERE t.type = 'table' AND t.name NOT LIKE 'sqlite_%'
        GROUP BY t.name
        HAVING index_count = 0
      ''');
      
      if (tablesWithoutIndexes.isNotEmpty) {
        final tableNames = tablesWithoutIndexes.map((t) => t['name']).join(', ');
        result.warnings.add('Tables without indexes: $tableNames');
      }
    } catch (e) {
      result.warnings.add('Could not analyze index usage: $e');
    }
  }

  Future<bool> repairDatabase(Database db, IntegrityCheckResult checkResult) async {
    try {
      print('[Repair] Starting database repair...');
      
      // Attempt to rebuild indexes if fragmentation is high
      if (checkResult.metrics['fragmentation_ratio'] != null && 
          checkResult.metrics['fragmentation_ratio']! > 0.2) {
        await db.execute('VACUUM');
        print('[Repair] Database vacuumed to reduce fragmentation');
      }
      
      // Reindex all indexes
      await db.execute('REINDEX');
      print('[Repair] All indexes rebuilt');
      
      // Analyze tables for query optimizer
      await db.execute('ANALYZE');
      print('[Repair] Table statistics updated');
      
      return true;
    } catch (e) {
      print('[Repair] Database repair failed: $e');
      return false;
    }
  }

  Future<DatabaseHealth> assessDatabaseHealth(Database db) async {
    final checkResult = await performFullIntegrityCheck(db);
    
    DatabaseHealthLevel healthLevel;
    
    if (checkResult.errors.isNotEmpty) {
      healthLevel = DatabaseHealthLevel.critical;
    } else if (checkResult.warnings.length > 5) {
      healthLevel = DatabaseHealthLevel.warning;
    } else if (checkResult.warnings.isNotEmpty) {
      healthLevel = DatabaseHealthLevel.caution;
    } else {
      healthLevel = DatabaseHealthLevel.healthy;
    }
    
    return DatabaseHealth(
      level: healthLevel,
      checkResult: checkResult,
      checkedAt: DateTime.now(),
    );
  }
}

class IntegrityCheckResult {
  bool isValid = true;
  final List<String> errors = [];
  final List<String> warnings = [];
  final List<String> checks = [];
  final Map<String, double> metrics = {};
  
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;
  
  String get summary {
    return 'Checks: ${checks.length}, Errors: ${errors.length}, Warnings: ${warnings.length}';
  }
  
  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'errors': errors,
      'warnings': warnings,
      'checks': checks,
      'metrics': metrics,
    };
  }
}

class DatabaseHealth {
  final DatabaseHealthLevel level;
  final IntegrityCheckResult checkResult;
  final DateTime checkedAt;
  
  DatabaseHealth({
    required this.level,
    required this.checkResult,
    required this.checkedAt,
  });
  
  String get description {
    switch (level) {
      case DatabaseHealthLevel.healthy:
        return 'Database is in excellent condition';
      case DatabaseHealthLevel.caution:
        return 'Database has minor issues that should be monitored';
      case DatabaseHealthLevel.warning:
        return 'Database has issues that should be addressed';
      case DatabaseHealthLevel.critical:
        return 'Database has critical issues requiring immediate attention';
    }
  }
  
  bool get needsAttention => level == DatabaseHealthLevel.warning || level == DatabaseHealthLevel.critical;
  bool get needsImmediateAction => level == DatabaseHealthLevel.critical;
}

enum DatabaseHealthLevel {
  healthy,
  caution,
  warning,
  critical,
}