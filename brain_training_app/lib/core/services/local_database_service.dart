import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:convert';
import '../../shared/models/game_result.dart';
import '../../shared/models/flash_card.dart';
import '../../shared/models/reward_point.dart';
import '../../shared/models/user.dart';
import '../models/app_user.dart';

class LocalDatabaseService {
  static Database? _database;
  static const String _databaseName = 'brain_booster.db';
  static const int _databaseVersion = 1;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        display_name TEXT,
        photo_url TEXT,
        created_at INTEGER NOT NULL,
        last_login_at INTEGER NOT NULL,
        is_guest INTEGER NOT NULL DEFAULT 0,
        stats TEXT NOT NULL,
        preferences TEXT NOT NULL
      )
    ''');

    // Game results table
    await db.execute('''
      CREATE TABLE game_results (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        game_type TEXT NOT NULL,
        level INTEGER NOT NULL,
        score INTEGER NOT NULL,
        accuracy REAL NOT NULL,
        duration INTEGER NOT NULL,
        timestamp INTEGER NOT NULL,
        game_specific_data TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Card decks table
    await db.execute('''
      CREATE TABLE card_decks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        is_public INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Flash cards table
    await db.execute('''
      CREATE TABLE flash_cards (
        id TEXT PRIMARY KEY,
        deck_id TEXT NOT NULL,
        front TEXT NOT NULL,
        back TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        last_reviewed_at INTEGER,
        next_review_at INTEGER,
        ease_factor REAL NOT NULL DEFAULT 2.5,
        interval_days INTEGER NOT NULL DEFAULT 1,
        repetitions INTEGER NOT NULL DEFAULT 0,
        quality INTEGER,
        FOREIGN KEY (deck_id) REFERENCES card_decks (id)
      )
    ''');

    // Reward points table
    await db.execute('''
      CREATE TABLE reward_points (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        points INTEGER NOT NULL,
        reason TEXT NOT NULL,
        earned_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Reward transactions table
    await db.execute('''
      CREATE TABLE reward_transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        amount INTEGER NOT NULL,
        description TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Reward balances table
    await db.execute('''
      CREATE TABLE reward_balances (
        user_id TEXT PRIMARY KEY,
        total_points INTEGER NOT NULL DEFAULT 0,
        spent_points INTEGER NOT NULL DEFAULT 0,
        available_points INTEGER NOT NULL DEFAULT 0,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // User unlocks table
    await db.execute('''
      CREATE TABLE user_unlocks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        unlock_type TEXT NOT NULL,
        unlock_data TEXT NOT NULL,
        unlocked_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_game_results_user_id ON game_results (user_id)');
    await db.execute('CREATE INDEX idx_game_results_timestamp ON game_results (timestamp)');
    await db.execute('CREATE INDEX idx_flash_cards_deck_id ON flash_cards (deck_id)');
    await db.execute('CREATE INDEX idx_flash_cards_next_review ON flash_cards (next_review_at)');
    await db.execute('CREATE INDEX idx_reward_points_user_id ON reward_points (user_id)');
    await db.execute('CREATE INDEX idx_reward_transactions_user_id ON reward_transactions (user_id)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades here
    // For now, just recreate tables
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  // User operations
  Future<void> saveUser(AppUser user) async {
    final db = await database;
    await db.insert(
      'users',
      {
        'id': user.id,
        'email': user.email,
        'display_name': user.displayName,
        'photo_url': user.photoURL,
        'created_at': user.createdAt.millisecondsSinceEpoch,
        'last_login_at': user.lastLoginAt.millisecondsSinceEpoch,
        'is_guest': user.isGuest ? 1 : 0,
        'stats': jsonEncode(user.stats.toJson()),
        'preferences': jsonEncode(user.preferences.toJson()),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<AppUser?> getUser(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    return AppUser(
      id: map['id'],
      email: map['email'],
      displayName: map['display_name'],
      photoURL: map['photo_url'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastLoginAt: DateTime.fromMillisecondsSinceEpoch(map['last_login_at']),
      isGuest: map['is_guest'] == 1,
      stats: UserStats.fromJson(jsonDecode(map['stats'])),
      preferences: UserPreferences.fromJson(jsonDecode(map['preferences'])),
    );
  }

  Future<void> updateUser(AppUser user) async {
    await saveUser(user); // Using replace conflict algorithm
  }

  // Game result operations
  Future<void> saveGameResult(GameResult result) async {
    final db = await database;
    await db.insert(
      'game_results',
      {
        'id': result.id,
        'user_id': result.userId,
        'game_type': result.gameType.toString(),
        'level': result.level,
        'score': result.score,
        'accuracy': result.accuracy,
        'duration': result.duration.inMilliseconds,
        'timestamp': result.timestamp.millisecondsSinceEpoch,
        'game_specific_data': jsonEncode(result.gameSpecificData),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<GameResult>> getGameResults(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'game_results',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'timestamp DESC',
    );

    return maps.map((map) => GameResult(
      id: map['id'],
      userId: map['user_id'],
      gameType: GameType.values.firstWhere(
        (e) => e.toString() == map['game_type'],
      ),
      level: map['level'],
      score: map['score'],
      accuracy: map['accuracy'],
      duration: Duration(milliseconds: map['duration']),
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      gameSpecificData: jsonDecode(map['game_specific_data']),
    )).toList();
  }

  // Flash card operations
  Future<void> saveCardDeck(CardDeck deck) async {
    final db = await database;
    await db.insert(
      'card_decks',
      {
        'id': deck.id,
        'user_id': deck.userId,
        'name': deck.name,
        'description': deck.description,
        'category': deck.category,
        'is_public': deck.isPublic ? 1 : 0,
        'created_at': deck.createdAt.millisecondsSinceEpoch,
        'updated_at': deck.updatedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<CardDeck?> getCardDeck(String deckId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_decks',
      where: 'id = ?',
      whereArgs: [deckId],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    return CardDeck(
      id: map['id'],
      userId: map['user_id'],
      name: map['name'],
      description: map['description'],
      category: map['category'],
      isPublic: map['is_public'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  Future<List<CardDeck>> getCardDecks(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_decks',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => CardDeck(
      id: map['id'],
      userId: map['user_id'],
      name: map['name'],
      description: map['description'],
      category: map['category'],
      isPublic: map['is_public'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    )).toList();
  }

  Future<void> deleteCardDeck(String deckId) async {
    final db = await database;
    await db.transaction((txn) async {
      // Delete all cards in the deck first
      await txn.delete(
        'flash_cards',
        where: 'deck_id = ?',
        whereArgs: [deckId],
      );
      
      // Delete the deck
      await txn.delete(
        'card_decks',
        where: 'id = ?',
        whereArgs: [deckId],
      );
    });
  }

  Future<void> saveFlashCard(FlashCard card) async {
    final db = await database;
    await db.insert(
      'flash_cards',
      {
        'id': card.id,
        'deck_id': card.deckId,
        'front': card.front,
        'back': card.back,
        'created_at': card.createdAt.millisecondsSinceEpoch,
        'last_reviewed_at': card.lastReviewedAt?.millisecondsSinceEpoch,
        'next_review_at': card.nextReviewAt?.millisecondsSinceEpoch,
        'ease_factor': card.easeFactor,
        'interval_days': card.intervalDays,
        'repetitions': card.repetitions,
        'quality': card.quality,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<FlashCard>> getFlashCards(String deckId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'flash_cards',
      where: 'deck_id = ?',
      whereArgs: [deckId],
      orderBy: 'created_at',
    );

    return maps.map((map) => FlashCard(
      id: map['id'],
      deckId: map['deck_id'],
      front: map['front'],
      back: map['back'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastReviewedAt: map['last_reviewed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_reviewed_at'])
          : null,
      nextReviewAt: map['next_review_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['next_review_at'])
          : null,
      easeFactor: map['ease_factor'],
      intervalDays: map['interval_days'],
      repetitions: map['repetitions'],
      quality: map['quality'],
    )).toList();
  }

  Future<void> deleteFlashCard(String cardId) async {
    final db = await database;
    await db.delete(
      'flash_cards',
      where: 'id = ?',
      whereArgs: [cardId],
    );
  }

  Future<List<FlashCard>> getAllUserFlashCards(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT fc.* FROM flash_cards fc
      INNER JOIN card_decks cd ON fc.deck_id = cd.id
      WHERE cd.user_id = ?
      ORDER BY fc.created_at
    ''', [userId]);

    return maps.map((map) => FlashCard(
      id: map['id'],
      deckId: map['deck_id'],
      front: map['front'],
      back: map['back'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastReviewedAt: map['last_reviewed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_reviewed_at'])
          : null,
      nextReviewAt: map['next_review_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['next_review_at'])
          : null,
      easeFactor: map['ease_factor'],
      intervalDays: map['interval_days'],
      repetitions: map['repetitions'],
      quality: map['quality'],
    )).toList();
  }

  // Reward operations
  Future<void> saveRewardPoint(RewardPoint rewardPoint) async {
    final db = await database;
    await db.insert(
      'reward_points',
      {
        'id': rewardPoint.id,
        'user_id': rewardPoint.userId,
        'points': rewardPoint.points,
        'reason': rewardPoint.reason,
        'earned_at': rewardPoint.earnedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<RewardPoint>> getRewardPoints(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reward_points',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'earned_at DESC',
    );

    return maps.map((map) => RewardPoint(
      id: map['id'],
      userId: map['user_id'],
      points: map['points'],
      reason: map['reason'],
      earnedAt: DateTime.fromMillisecondsSinceEpoch(map['earned_at']),
    )).toList();
  }

  Future<void> saveRewardTransaction(RewardTransaction transaction) async {
    final db = await database;
    await db.insert(
      'reward_transactions',
      {
        'id': transaction.id,
        'user_id': transaction.userId,
        'type': transaction.type.toString(),
        'amount': transaction.amount,
        'description': transaction.description,
        'timestamp': transaction.timestamp.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<RewardTransaction>> getRewardTransactions(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reward_transactions',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'timestamp DESC',
    );

    return maps.map((map) => RewardTransaction(
      id: map['id'],
      userId: map['user_id'],
      type: RewardTransactionType.values.firstWhere(
        (e) => e.toString() == map['type'],
      ),
      amount: map['amount'],
      description: map['description'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
    )).toList();
  }

  Future<void> saveRewardBalance(RewardBalance balance) async {
    final db = await database;
    await db.insert(
      'reward_balances',
      {
        'user_id': balance.userId,
        'total_points': balance.totalPoints,
        'spent_points': balance.spentPoints,
        'available_points': balance.availablePoints,
        'updated_at': balance.updatedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<RewardBalance?> getRewardBalance(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'reward_balances',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    return RewardBalance(
      userId: map['user_id'],
      totalPoints: map['total_points'],
      spentPoints: map['spent_points'],
      availablePoints: map['available_points'],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  Future<void> saveUserUnlock(UserUnlock unlock) async {
    final db = await database;
    await db.insert(
      'user_unlocks',
      {
        'id': unlock.id,
        'user_id': unlock.userId,
        'unlock_type': unlock.unlockType.toString(),
        'unlock_data': jsonEncode(unlock.unlockData),
        'unlocked_at': unlock.unlockedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<UserUnlock>> getUserUnlocks(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_unlocks',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'unlocked_at DESC',
    );

    return maps.map((map) => UserUnlock(
      id: map['id'],
      userId: map['user_id'],
      unlockType: UnlockType.values.firstWhere(
        (e) => e.toString() == map['unlock_type'],
      ),
      unlockData: jsonDecode(map['unlock_data']),
      unlockedAt: DateTime.fromMillisecondsSinceEpoch(map['unlocked_at']),
    )).toList();
  }

  // Cleanup and maintenance
  Future<void> clearAllData() async {
    final db = await database;
    await db.transaction((txn) async {
      await txn.delete('user_unlocks');
      await txn.delete('reward_balances');
      await txn.delete('reward_transactions');
      await txn.delete('reward_points');
      await txn.delete('flash_cards');
      await txn.delete('card_decks');
      await txn.delete('game_results');
      await txn.delete('users');
    });
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}