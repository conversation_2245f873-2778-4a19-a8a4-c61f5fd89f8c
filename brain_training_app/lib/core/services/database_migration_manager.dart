import 'package:sqflite/sqflite.dart';
import 'database_backup_service.dart';

typedef MigrationFunction = Future<void> Function(DatabaseExecutor db);

class DatabaseMigrationManager {
  final DatabaseBackupService _backupService = DatabaseBackupService();
  final Map<int, MigrationFunction> _migrations = {};
  final Map<int, DataValidationFunction> _validations = {};

  void registerMigration(int toVersion, MigrationFunction migration, {DataValidationFunction? validation}) {
    _migrations[toVersion] = migration;
    if (validation != null) {
      _validations[toVersion] = validation;
    }
  }

  Future<void> performMigration(Database db, int oldVersion, int newVersion) async {
    print('[Migration] Starting migration from version $oldVersion to $newVersion');
    
    String? backupPath;
    try {
      // Create backup before migration
      backupPath = await _backupService.createBackup(db, 'pre_migration_v${oldVersion}_to_v$newVersion');
      
      // Validate current database state
      await _validateDatabaseIntegrity(db, oldVersion);
      
      // Perform incremental migrations
      for (int version = oldVersion + 1; version <= newVersion; version++) {
        await _migrateToVersion(db, version);
      }
      
      // Final validation
      await _validateDatabaseIntegrity(db, newVersion);
      
      print('[Migration] Successfully migrated to version $newVersion');
    } catch (e) {
      print('[Migration] Error during migration: $e');
      
      // Attempt rollback if backup exists
      if (backupPath != null) {
        try {
          print('[Migration] Attempting rollback...');
          await db.close();
          await _backupService.restoreBackup(backupPath, db.path);
          print('[Migration] Rollback completed successfully');
        } catch (rollbackError) {
          print('[Migration] Rollback failed: $rollbackError');
        }
      }
      
      throw MigrationException('Migration failed: $e', oldVersion, newVersion);
    }
  }

  Future<void> _migrateToVersion(Database db, int version) async {
    print('[Migration] Migrating to version $version');
    
    final migration = _migrations[version];
    if (migration == null) {
      throw MigrationException('No migration defined for version $version', version - 1, version);
    }

    try {
      await db.transaction((txn) async {
        await migration(txn);
        
        // Update database version
        await txn.execute('PRAGMA user_version = $version');
        
        // Log migration in metadata table
        await _logMigration(txn, version);
      });
      
      // Validate migration result
      final validation = _validations[version];
      if (validation != null) {
        final isValid = await validation(db);
        if (!isValid) {
          throw MigrationException('Validation failed after migration to version $version', version - 1, version);
        }
      }
      
      print('[Migration] Successfully migrated to version $version');
    } catch (e) {
      print('[Migration] Error migrating to version $version: $e');
      rethrow;
    }
  }

  Future<void> _validateDatabaseIntegrity(Database db, int expectedVersion) async {
    try {
      // Check SQLite integrity
      final integrityResult = await db.rawQuery('PRAGMA integrity_check');
      if (integrityResult.isEmpty || integrityResult.first.values.first != 'ok') {
        throw MigrationException('Database integrity check failed', expectedVersion - 1, expectedVersion);
      }
      
      // Check foreign key constraints
      final foreignKeyResult = await db.rawQuery('PRAGMA foreign_key_check');
      if (foreignKeyResult.isNotEmpty) {
        throw MigrationException('Foreign key constraints violated', expectedVersion - 1, expectedVersion);
      }
      
      // Verify version
      final versionResult = await db.rawQuery('PRAGMA user_version');
      final currentVersion = versionResult.first['user_version'] as int;
      if (currentVersion != expectedVersion) {
        print('[Migration] Warning: Expected version $expectedVersion, but found $currentVersion');
      }
      
      print('[Migration] Database integrity validated for version $expectedVersion');
    } catch (e) {
      print('[Migration] Database integrity validation failed: $e');
      rethrow;
    }
  }

  Future<void> _logMigration(DatabaseExecutor db, int version) async {
    try {
      // Create migration log table if it doesn't exist
      await db.execute('''
        CREATE TABLE IF NOT EXISTS migration_log (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER NOT NULL,
          applied_at INTEGER NOT NULL,
          duration_ms INTEGER,
          status TEXT NOT NULL,
          error_message TEXT
        )
      ''');
      
      await db.insert('migration_log', {
        'version': version,
        'applied_at': DateTime.now().millisecondsSinceEpoch,
        'status': 'completed',
      });
    } catch (e) {
      print('[Migration] Failed to log migration: $e');
      // Don't fail the migration for logging errors
    }
  }

  Future<List<MigrationLogEntry>> getMigrationHistory(Database db) async {
    try {
      final results = await db.query(
        'migration_log',
        orderBy: 'applied_at DESC',
      );
      
      return results.map((row) => MigrationLogEntry(
        version: row['version'] as int,
        appliedAt: DateTime.fromMillisecondsSinceEpoch(row['applied_at'] as int),
        durationMs: row['duration_ms'] as int?,
        status: row['status'] as String,
        errorMessage: row['error_message'] as String?,
      )).toList();
    } catch (e) {
      print('[Migration] Error reading migration history: $e');
      return [];
    }
  }

  Future<bool> isDatabaseUpToDate(Database db, int expectedVersion) async {
    try {
      final versionResult = await db.rawQuery('PRAGMA user_version');
      final currentVersion = versionResult.first['user_version'] as int;
      return currentVersion == expectedVersion;
    } catch (e) {
      print('[Migration] Error checking database version: $e');
      return false;
    }
  }

  Future<void> createMigrationPlan(int fromVersion, int toVersion) async {
    final plan = <MigrationStep>[];
    
    for (int version = fromVersion + 1; version <= toVersion; version++) {
      final migration = _migrations[version];
      if (migration == null) {
        throw MigrationException('Missing migration for version $version', fromVersion, toVersion);
      }
      
      plan.add(MigrationStep(
        fromVersion: version - 1,
        toVersion: version,
        hasValidation: _validations.containsKey(version),
      ));
    }
    
    print('[Migration] Migration plan created: ${plan.length} steps');
    for (final step in plan) {
      print('[Migration] Step: v${step.fromVersion} -> v${step.toVersion} (validation: ${step.hasValidation})');
    }
  }
}

// Migration Registration Helper
class MigrationRegistry {
  static void registerAllMigrations(DatabaseMigrationManager manager) {
    // Version 2: Add reward system tables
    manager.registerMigration(2, _migrateToVersion2, validation: _validateVersion2);
    
    // Version 3: Add indexes for performance
    manager.registerMigration(3, _migrateToVersion3, validation: _validateVersion3);
    
    // Version 4: Add full-text search support
    manager.registerMigration(4, _migrateToVersion4, validation: _validateVersion4);
  }

  static Future<void> _migrateToVersion2(DatabaseExecutor db) async {
    print('[Migration] Adding reward system tables for version 2');
    
    await db.execute('''
      CREATE TABLE IF NOT EXISTS reward_points (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        amount INTEGER NOT NULL,
        source TEXT NOT NULL,
        earned_at INTEGER NOT NULL,
        description TEXT,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS reward_transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        amount INTEGER NOT NULL,
        description TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        is_earned INTEGER NOT NULL,
        source TEXT,
        unlockable_reward TEXT,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS reward_balances (
        user_id TEXT PRIMARY KEY,
        total_earned INTEGER NOT NULL,
        total_spent INTEGER NOT NULL,
        current_balance INTEGER NOT NULL,
        last_updated INTEGER NOT NULL,
        earned_by_type TEXT DEFAULT '{}',
        spent_on_rewards TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS user_unlocks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        reward TEXT NOT NULL,
        points_spent INTEGER NOT NULL,
        unlocked_at INTEGER NOT NULL,
        transaction_id TEXT,
        quantity INTEGER DEFAULT 1,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');
  }

  static Future<bool> _validateVersion2(Database db) async {
    final tables = ['reward_points', 'reward_transactions', 'reward_balances', 'user_unlocks'];
    
    for (final table in tables) {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [table]
      );
      if (result.isEmpty) {
        print('[Migration] Validation failed: Table $table not found');
        return false;
      }
    }
    
    return true;
  }

  static Future<void> _migrateToVersion3(DatabaseExecutor db) async {
    print('[Migration] Adding performance indexes for version 3');
    
    await db.execute('CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_game_sessions_timestamp ON game_sessions (start_time)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_flash_cards_deck_id ON flash_cards (deck_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_reward_points_user_id ON reward_points (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_reward_transactions_user_id ON reward_transactions (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements (user_id)');
  }

  static Future<bool> _validateVersion3(Database db) async {
    final indexes = [
      'idx_game_sessions_user_id',
      'idx_game_sessions_timestamp', 
      'idx_flash_cards_deck_id',
      'idx_reward_points_user_id',
      'idx_reward_transactions_user_id',
      'idx_user_achievements_user_id'
    ];
    
    for (final index in indexes) {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND name=?",
        [index]
      );
      if (result.isEmpty) {
        print('[Migration] Validation failed: Index $index not found');
        return false;
      }
    }
    
    return true;
  }

  static Future<void> _migrateToVersion4(DatabaseExecutor db) async {
    print('[Migration] Adding full-text search support for version 4');
    
    // Add FTS virtual table for flash cards
    await db.execute('''
      CREATE VIRTUAL TABLE IF NOT EXISTS flash_cards_fts USING fts5(
        card_id,
        front,
        back,
        hint,
        content='flash_cards',
        content_rowid='rowid'
      )
    ''');
    
    // Populate FTS table with existing data
    await db.execute('''
      INSERT INTO flash_cards_fts(card_id, front, back, hint)
      SELECT id, front, back, hint FROM flash_cards
    ''');
    
    // Add search functionality triggers
    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS flash_cards_fts_insert AFTER INSERT ON flash_cards BEGIN
        INSERT INTO flash_cards_fts(card_id, front, back, hint)
        VALUES (new.id, new.front, new.back, new.hint);
      END
    ''');
    
    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS flash_cards_fts_update AFTER UPDATE ON flash_cards BEGIN
        UPDATE flash_cards_fts SET front=new.front, back=new.back, hint=new.hint
        WHERE card_id=new.id;
      END
    ''');
    
    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS flash_cards_fts_delete AFTER DELETE ON flash_cards BEGIN
        DELETE FROM flash_cards_fts WHERE card_id=old.id;
      END
    ''');
  }

  static Future<bool> _validateVersion4(Database db) async {
    final result = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='flash_cards_fts'"
    );
    return result.isNotEmpty;
  }
}

typedef DataValidationFunction = Future<bool> Function(Database db);

class MigrationLogEntry {
  final int version;
  final DateTime appliedAt;
  final int? durationMs;
  final String status;
  final String? errorMessage;

  MigrationLogEntry({
    required this.version,
    required this.appliedAt,
    this.durationMs,
    required this.status,
    this.errorMessage,
  });
}

class MigrationStep {
  final int fromVersion;
  final int toVersion;
  final bool hasValidation;

  MigrationStep({
    required this.fromVersion,
    required this.toVersion,
    required this.hasValidation,
  });
}

class MigrationException implements Exception {
  final String message;
  final int fromVersion;
  final int toVersion;

  MigrationException(this.message, this.fromVersion, this.toVersion);

  @override
  String toString() => 'MigrationException: $message (v$fromVersion -> v$toVersion)';
}