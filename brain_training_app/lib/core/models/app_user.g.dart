// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppUserImpl _$$AppUserImplFromJson(Map<String, dynamic> json) =>
    _$AppUserImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      birthYear: (json['birthYear'] as num?)?.toInt(),
      gender: json['gender'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      stats: UserStats.fromJson(json['stats'] as Map<String, dynamic>),
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      isGuest: json['isGuest'] as bool? ?? false,
    );

Map<String, dynamic> _$$AppUserImplToJson(_$AppUserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoURL': instance.photoURL,
      'birthYear': instance.birthYear,
      'gender': instance.gender,
      'createdAt': instance.createdAt.toIso8601String(),
      'stats': instance.stats,
      'preferences': instance.preferences,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'isGuest': instance.isGuest,
    };
