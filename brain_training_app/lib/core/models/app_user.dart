import 'package:freezed_annotation/freezed_annotation.dart';
import '../../shared/models/user.dart';

part 'app_user.freezed.dart';
part 'app_user.g.dart';

@freezed
class AppUser with _$AppUser {
  const factory AppUser({
    required String id,
    required String email,
    String? displayName,
    String? photoURL,
    int? birthYear,
    String? gender,
    required DateTime createdAt,
    required UserStats stats,
    required UserPreferences preferences,
    DateTime? lastLoginAt,
    @Default(false) bool isGuest,
  }) = _AppUser;

  factory AppUser.fromJson(Map<String, dynamic> json) => _$AppUserFromJson(json);
}
