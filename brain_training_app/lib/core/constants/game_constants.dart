enum GameType {
  dualNBack,
  flashCards,
  workingMemory,
  attentionTraining,
}

enum AchievementType {
  streak,
  gameSpecific,
  level,
  total,
}

enum LeaderboardFilter {
  daily,
  weekly,
  monthly,
  allTime,
}

class GameConstants {
  static const Map<GameType, String> gameNames = {
    GameType.dualNBack: 'Dual N-Back',
    GameType.flashCards: 'Flash Cards',
    GameType.workingMemory: 'Working Memory',
    GameType.attentionTraining: 'Attention Training',
  };
  
  static const Map<GameType, String> gameDescriptions = {
    GameType.dualNBack: '시각적/청각적 자극의 N번째 전 패턴을 기억하세요',
    GameType.flashCards: '간격 반복을 통해 효율적으로 학습하세요',
    GameType.workingMemory: '숫자와 패턴을 기억하고 처리하세요',
    GameType.attentionTraining: '집중력과 선택적 주의력을 향상시키세요',
  };
  
  static const Map<GameType, int> baseXpRewards = {
    GameType.dualNBack: 100,
    GameType.flashCards: 50,
    GameType.workingMemory: 75,
    GameType.attentionTraining: 80,
  };
  
  static const Map<int, int> levelXpRequirements = {
    1: 0,
    2: 1000,
    3: 2500,
    4: 5000,
    5: 8500,
    6: 13000,
    7: 18500,
    8: 25000,
    9: 32500,
    10: 41000,
  };
  
  static const List<String> dualNBackPositions = [
    'top-left', 'top-center', 'top-right',
    'middle-left', 'middle-center', 'middle-right',
    'bottom-left', 'bottom-center', 'bottom-right',
  ];
  
  static const List<String> dualNBackSounds = [
    'a', 'e', 'i', 'o', 'u', 'k', 'h', 'l', 'q',
  ];
}