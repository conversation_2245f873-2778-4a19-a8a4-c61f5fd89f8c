class AppConstants {
  static const String appName = 'Brain Booster';
  static const String appVersion = '1.0.0';
  
  static const int maxStreakDays = 365;
  static const int dailyXpBonus = 50;
  static const int streakXpMultiplier = 2;
  
  static const Duration gameSessionTimeout = Duration(minutes: 30);
  static const Duration networkTimeout = Duration(seconds: 30);
  
  static const int minNBackLevel = 1;
  static const int maxNBackLevel = 8;
  static const double nBackAccuracyThreshold = 0.8;
  
  static const int flashCardDefaultDailyGoal = 20;
  static const int flashCardMaxDeckSize = 1000;
  
  static const Duration stimulusDuration = Duration(milliseconds: 1000);
  static const Duration interstimulusInterval = Duration(milliseconds: 4000);
  
  static const List<String> supportedLanguages = ['ko', 'en', 'ja', 'zh'];
  
  static const String privacyPolicyUrl = 'https://windroamer.com/brainbooster/privacy';
  static const String privacyPolicyUrlKo = 'https://windroamer.com/brainbooster/privacy-ko';
  static const String termsOfServiceUrl = 'https://windroamer.com/brainbooster/terms';
  static const String supportEmail = '<EMAIL>';
  
  // Feature Toggles - 게임별 결과 저장 기능 제어
  static const bool enableDualNBackResultSaving = true;
  static const bool enableFlashCardsResultSaving = true;
  static const bool enableWorkingMemoryResultSaving = true;
  static const bool enableAttentionTrainingResultSaving = true;
  
  // Feature Toggles - 수익화 및 외부 서비스
  static const bool enableBuzzvil = false;
}