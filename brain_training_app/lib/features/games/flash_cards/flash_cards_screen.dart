import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/extensions.dart';
import '../../../shared/models/flash_card.dart';
import 'flash_cards_provider.dart';
import 'spaced_repetition.dart';
import 'deck_creation_screen.dart';
import 'study_session_screen.dart';

class FlashCardsScreen extends ConsumerWidget {
  const FlashCardsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userDecks = ref.watch(userDecksProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Flash Cards'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreateDeck(context),
          ),
        ],
      ),
      body: userDecks.when(
        data: (decks) => _buildDecksView(context, ref, decks),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorView(context, error),
      ),
    );
  }

  Widget _buildDecksView(BuildContext context, WidgetRef ref, List<CardDeck> decks) {
    if (decks.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () => ref.refresh(userDecksProvider.future),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: decks.length,
        itemBuilder: (context, index) {
          final deck = decks[index];
          return _buildDeckCard(context, ref, deck);
        },
      ),
    );
  }

  Widget _buildDeckCard(BuildContext context, WidgetRef ref, CardDeck deck) {
    final deckStats = ref.watch(deckStatisticsProvider(deck.id));

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToStudySession(context, deck),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          deck.name,
                          style: context.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (deck.description.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            deck.description,
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.colors.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  deckStats.when(
                    data: (stats) => _buildPriorityIndicator(stats.priorityLevel),
                    loading: () => const SizedBox(width: 24, height: 24),
                    error: (_, __) => const SizedBox(),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              deckStats.when(
                data: (stats) => _buildDeckStatistics(context, stats),
                loading: () => const LinearProgressIndicator(),
                error: (_, __) => Text(
                  '통계를 불러올 수 없습니다',
                  style: context.textTheme.bodySmall?.copyWith(color: context.colors.error),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _navigateToStudySession(context, deck),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('학습하기'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _showDeckOptions(context, ref, deck),
                    icon: const Icon(Icons.more_vert),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeckStatistics(BuildContext context, DeckStatistics stats) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(context, '총 카드', stats.totalCards.toString()),
            _buildStatItem(context, '새 카드', stats.newCards.toString()),
            _buildStatItem(context, '복습 대기', stats.dueCards.toString()),
            _buildStatItem(context, '정확도', '${(stats.retentionRate * 100).toInt()}%'),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: stats.totalCards > 0 ? (stats.totalCards - stats.newCards) / stats.totalCards : 0,
          backgroundColor: context.colors.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(context.colors.primary),
        ),
        const SizedBox(height: 4),
        Text(
          stats.statusDescription,
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colors.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: context.textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildPriorityIndicator(int priorityLevel) {
    Color color;
    IconData icon;
    
    switch (priorityLevel) {
      case 2:
        color = Colors.red;
        icon = Icons.priority_high;
        break;
      case 1:
        color = Colors.orange;
        icon = Icons.schedule;
        break;
      default:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.style,
              size: 100,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 24),
            Text(
              'Flash Cards',
              style: context.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '아직 생성된 카드 덱이 없습니다.\n첫 번째 덱을 만들어보세요!',
              textAlign: TextAlign.center,
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colors.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _navigateToCreateDeck(context),
              icon: const Icon(Icons.add),
              label: const Text('첫 덱 만들기'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '오류가 발생했습니다',
              style: context.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => {}, // Refresh action
              child: const Text('다시 시도'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCreateDeck(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DeckCreationScreen(),
      ),
    );
  }

  void _navigateToStudySession(BuildContext context, CardDeck deck) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StudySessionScreen(deck: deck),
      ),
    );
  }

  void _showDeckOptions(BuildContext context, WidgetRef ref, CardDeck deck) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('덱 편집'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to edit deck screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.add),
            title: const Text('카드 추가'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to add card screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('통계 보기'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to deck statistics screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('덱 삭제', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _confirmDeleteDeck(context, ref, deck);
            },
          ),
        ],
      ),
    );
  }

  void _confirmDeleteDeck(BuildContext context, WidgetRef ref, CardDeck deck) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('덱 삭제'),
        content: Text('정말로 "${deck.name}" 덱을 삭제하시겠습니까?\n모든 카드와 학습 기록이 삭제됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                final service = ref.read(flashCardsServiceProvider);
                await service.deleteDeck(deck.id);
                ref.invalidate(userDecksProvider);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('덱이 삭제되었습니다')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('삭제 실패: $e')),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }
}