
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/extensions.dart';
import '../../../shared/models/flash_card.dart';
import 'flash_cards_provider.dart';

class StudySessionScreen extends ConsumerStatefulWidget {
  final CardDeck deck;

  const StudySessionScreen({super.key, required this.deck});

  @override
  ConsumerState<StudySessionScreen> createState() => _StudySessionScreenState();
}

class _StudySessionScreenState extends ConsumerState<StudySessionScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;
  
  List<FlashCard> _cards = [];
  int _currentIndex = 0;
  bool _isShowingBack = false;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));
    _loadCards();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _flipController.dispose();
    super.dispose();
  }

  Future<void> _loadCards() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final service = ref.read(flashCardsServiceProvider);
      final cards = await service.getDueCards(widget.deck.id);
      
      setState(() {
        _cards = cards;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('학습: ${widget.deck.name}'),
        actions: [
          if (_cards.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Center(
                child: Text(
                  '${_currentIndex + 1}/${_cards.length}',
                  style: context.textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return _buildErrorView();
    }

    if (_cards.isEmpty) {
      return _buildEmptyView();
    }

    return Column(
      children: [
        _buildProgressIndicator(),
        Expanded(
          child: _buildCardView(),
        ),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return LinearProgressIndicator(
      value: _cards.isNotEmpty ? (_currentIndex + 1) / _cards.length : 0,
      backgroundColor: context.colors.outline.withValues(alpha: 0.2),
      valueColor: AlwaysStoppedAnimation<Color>(context.colors.primary),
    );
  }

  Widget _buildCardView() {
    return PageView.builder(
      controller: _pageController,
      itemCount: _cards.length,
      onPageChanged: (index) {
        setState(() {
          _currentIndex = index;
          _isShowingBack = false;
        });
        _flipController.reset();
      },
      itemBuilder: (context, index) {
        final card = _cards[index];
        return Padding(
          padding: const EdgeInsets.all(16),
          child: GestureDetector(
            onTap: _flipCard,
            child: AnimatedBuilder(
              animation: _flipAnimation,
              builder: (context, child) {
                final isShowingFront = _flipAnimation.value < 0.5;
                return Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_flipAnimation.value * 3.14159),
                  child: Card(
                    elevation: 8,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: isShowingFront
                              ? [
                                  AppTheme.primaryColor.withValues(alpha: 0.1),
                                  AppTheme.primaryColor.withValues(alpha: 0.05),
                                ]
                              : [
                                  AppTheme.accentColor.withValues(alpha: 0.1),
                                  AppTheme.accentColor.withValues(alpha: 0.05),
                                ],
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            isShowingFront ? Icons.help_outline : Icons.lightbulb_outline,
                            size: 48,
                            color: isShowingFront 
                                ? AppTheme.primaryColor 
                                : AppTheme.accentColor,
                          ),
                          const SizedBox(height: 24),
                          Transform(
                            alignment: Alignment.center,
                            transform: Matrix4.identity()
                              ..rotateY(isShowingFront ? 0 : 3.14159),
                            child: Text(
                              isShowingFront ? card.front : card.back,
                              style: context.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(height: 32),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: (isShowingFront 
                                  ? AppTheme.primaryColor 
                                  : AppTheme.accentColor).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              isShowingFront ? '앞면' : '뒷면',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: isShowingFront 
                                    ? AppTheme.primaryColor 
                                    : AppTheme.accentColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (!_isShowingBack) ...[
                            const SizedBox(height: 24),
                            Text(
                              '카드를 탭해서 답을 확인하세요',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.colors.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButtons() {
    if (!_isShowingBack) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _flipCard,
            icon: const Icon(Icons.flip_to_back),
            label: const Text('답 보기'),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            '이 카드를 얼마나 잘 기억했나요?',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQualityButton(
                  label: '어려움',
                  color: Colors.red,
                  quality: CardQuality.incorrect,
                  description: '다시',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQualityButton(
                  label: '보통',
                  color: Colors.orange,
                  quality: CardQuality.hard,
                  description: '어려움',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQualityButton(
                  label: '좋음',
                  color: Colors.blue,
                  quality: CardQuality.good,
                  description: '좋음',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQualityButton(
                  label: '쉬움',
                  color: Colors.green,
                  quality: CardQuality.easy,
                  description: '쉬움',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQualityButton({
    required String label,
    required Color color,
    required CardQuality quality,
    required String description,
  }) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () => _rateCard(quality),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colors.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle_outline,
              size: 100,
              color: Colors.green,
            ),
            const SizedBox(height: 24),
            Text(
              '축하합니다!',
              style: context.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '오늘 학습할 카드가 모두 완료되었습니다.\n내일 다시 학습하러 오세요!',
              textAlign: TextAlign.center,
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colors.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('완료'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '카드를 불러올 수 없습니다',
              style: context.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadCards,
              child: const Text('다시 시도'),
            ),
          ],
        ),
      ),
    );
  }

  void _flipCard() {
    if (_isShowingBack) return;
    
    setState(() {
      _isShowingBack = true;
    });
    _flipController.forward();
  }

  Future<void> _rateCard(CardQuality quality) async {
    if (_currentIndex >= _cards.length) return;

    try {
      final card = _cards[_currentIndex];
      final service = ref.read(flashCardsServiceProvider);
      
      // Update card review
      await service.reviewCard(card.id, quality);
      
      // Move to next card or finish session
      if (_currentIndex < _cards.length - 1) {
        _moveToNextCard();
      } else {
        _finishSession();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('평가 저장 실패: $e')),
        );
      }
    }
  }

  void _moveToNextCard() {
    setState(() {
      _currentIndex++;
      _isShowingBack = false;
    });
    _flipController.reset();
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _finishSession() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('학습 완료'),
        content: Text('${widget.deck.name} 덱의 학습을 완료했습니다!\n오늘도 수고하셨습니다.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close study session
            },
            child: const Text('완료'),
          ),
        ],
      ),
    );
  }
}
