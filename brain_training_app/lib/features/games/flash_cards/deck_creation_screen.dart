
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/extensions.dart';
import '../../../shared/models/flash_card.dart';
import '../../../features/auth/auth_provider.dart';
import 'flash_cards_provider.dart';

class DeckCreationScreen extends ConsumerStatefulWidget {
  final CardDeck? editingDeck;
  
  const DeckCreationScreen({super.key, this.editingDeck});

  @override
  ConsumerState<DeckCreationScreen> createState() => _DeckCreationScreenState();
}

class _DeckCreationScreenState extends ConsumerState<DeckCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _cards = <FlashCard>[];
  
  bool get isEditing => widget.editingDeck != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.editingDeck!.name;
      _descriptionController.text = widget.editingDeck!.description;
      // Load existing cards
      _loadExistingCards();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingCards() async {
    if (widget.editingDeck != null) {
      try {
        final service = ref.read(flashCardsServiceProvider);
        final cards = await service.getCardsInDeck(widget.editingDeck!.id);
        setState(() {
          _cards.clear();
          _cards.addAll(cards);
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('카드를 불러오는데 실패했습니다: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? '덱 편집' : '새 덱 만들기'),
        actions: [
          TextButton(
            onPressed: _saveDeck,
            child: Text(
              isEditing ? '저장' : '완료',
              style: TextStyle(color: context.colors.primary),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildDeckInfoSection(),
                  const SizedBox(height: 24),
                  _buildCardsSection(),
                ],
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeckInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '덱 정보',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '덱 이름',
                hintText: '예: 영단어 기초',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '덱 이름을 입력해주세요';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: '설명 (선택사항)',
                hintText: '이 덱에 대한 간단한 설명을 입력하세요',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '카드 (${_cards.length}개)',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: _canAddCard() ? _addNewCard : null,
                  icon: const Icon(Icons.add),
                  label: const Text('카드 추가'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildStorageUsageIndicator(),
            const SizedBox(height: 16),
            if (_cards.isEmpty)
              _buildEmptyCardsState()
            else
              ..._cards.asMap().entries.map((entry) {
                final index = entry.key;
                final card = entry.value;
                return _buildCardItem(index, card);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyCardsState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        border: Border.all(
          color: context.colors.outline.withValues(alpha: 0.3),
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            Icons.style_outlined,
            size: 48,
            color: context.colors.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '아직 카드가 없습니다',
            style: context.textTheme.titleMedium?.copyWith(
              color: context.colors.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '첫 번째 카드를 추가해보세요',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colors.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardItem(int index, FlashCard card) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
          child: Text(
            '${index + 1}',
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          card.front,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          card.back,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: context.textTheme.bodyMedium?.copyWith(
            color: context.colors.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _editCard(index),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _deleteCard(index),
            ),
          ],
        ),
        onTap: () => _editCard(index),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.colors.surface,
        boxShadow: [
          BoxShadow(
            color: context.colors.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _addNewCard,
              icon: const Icon(Icons.add),
              label: const Text('카드 추가'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _cards.isNotEmpty ? _saveDeck : null,
              child: Text(isEditing ? '저장' : '덱 생성'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageUsageIndicator() {
    final storageUsage = ref.watch(storageUsageProvider);
    
    return storageUsage.when(
      data: (usage) {
        if (usage == null) return const SizedBox.shrink();
        
        final usagePercentage = usage.usagePercentage;
        final color = usagePercentage > 0.9 
            ? Colors.red
            : usagePercentage > 0.7 
                ? Colors.orange
                : Colors.green;
        
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withAlpha(100)),
          ),
          child: Row(
            children: [
              Icon(Icons.storage, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '저장 공간: ${usage.currentCount + _cards.length}/${usage.limit}',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: (usage.currentCount + _cards.length) / usage.limit,
                      backgroundColor: color.withAlpha(50),
                      valueColor: AlwaysStoppedAnimation<Color>(color),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  bool _canAddCard() {
    final storageUsage = ref.read(storageUsageProvider).value;
    if (storageUsage == null) return true;
    
    final totalCards = storageUsage.currentCount + _cards.length;
    return totalCards < storageUsage.limit;
  }

  void _addNewCard() {
    if (!_canAddCard()) {
      _showStorageLimitDialog();
      return;
    }
    _showCardDialog();
  }

  void _showStorageLimitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('저장 공간 부족'),
        content: const Text('카드 저장 공간이 부족합니다. 기존 카드를 삭제하거나 프리미엄 구독을 통해 저장 공간을 늘려보세요.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('확인'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Navigate to subscription screen
            },
            child: const Text('프리미엄 구독'),
          ),
        ],
      ),
    );
  }

  void _editCard(int index) {
    _showCardDialog(editingCard: _cards[index], editingIndex: index);
  }

  void _deleteCard(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('카드 삭제'),
        content: const Text('이 카드를 삭제하시겠습니까?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _cards.removeAt(index);
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }

  void _showCardDialog({FlashCard? editingCard, int? editingIndex}) {
    final frontController = TextEditingController(text: editingCard?.front ?? '');
    final backController = TextEditingController(text: editingCard?.back ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(editingCard == null ? '새 카드 추가' : '카드 편집'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: frontController,
                decoration: const InputDecoration(
                  labelText: '앞면',
                  hintText: '질문이나 단어를 입력하세요',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '앞면 내용을 입력해주세요';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: backController,
                decoration: const InputDecoration(
                  labelText: '뒷면',
                  hintText: '답변이나 설명을 입력하세요',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '뒷면 내용을 입력해주세요';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                final newCard = FlashCard(
                  id: editingCard?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
                  deckId: widget.editingDeck?.id ?? '',
                  front: frontController.text.trim(),
                  back: backController.text.trim(),
                  createdAt: editingCard?.createdAt ?? DateTime.now(),
                  updatedAt: DateTime.now(),
                  review: editingCard?.review ?? const CardReview(),
                );

                setState(() {
                  if (editingIndex != null) {
                    _cards[editingIndex] = newCard;
                  } else {
                    _cards.add(newCard);
                  }
                });

                Navigator.pop(context);
              }
            },
            child: Text(editingCard == null ? '추가' : '저장'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveDeck() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_cards.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('최소 하나의 카드를 추가해주세요')),
      );
      return;
    }

    try {
      final service = ref.read(flashCardsServiceProvider);
      final currentUser = ref.read(currentUserProvider);
      
      if (currentUser == null) {
        throw Exception('로그인이 필요합니다');
      }
      
      if (isEditing) {
        // Update existing deck
        final updatedDeck = widget.editingDeck!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          updatedAt: DateTime.now(),
        );
        
        await service.updateDeck(updatedDeck);
        await service.updateCardsInDeck(updatedDeck.id, _cards);
      } else {
        // Create new deck
        final newDeck = CardDeck(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          userId: currentUser.id,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await service.createDeck(newDeck, _cards);
      }

      // Refresh decks list and storage usage
      ref.invalidate(userDecksProvider);
      ref.invalidate(storageUsageProvider);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing ? '덱이 수정되었습니다' : '덱이 생성되었습니다'),
          ),
        );
      }
    } on StorageLimitExceededException catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('저장 공간 부족'),
            content: Text(e.message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('확인'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Navigate to subscription screen
                },
                child: const Text('프리미엄 구독'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('저장 실패: $e')),
        );
      }
    }
  }
}
