import 'dart:math';
import '../../../shared/models/flash_card.dart';

class SpacedRepetitionAlgorithm {
  /// Implements the SM-2 (SuperMemo 2) algorithm for spaced repetition
  /// Quality scale: 0-5 (0=blackout, 1=incorrect, 2=hard, 3=good, 4=easy, 5=perfect)
  static CardReview calculateNextReview(CardReview currentReview, CardQuality quality) {
    double easeFactor = currentReview.easeFactor;
    int interval = currentReview.interval;
    int repetitions = currentReview.repetitions;
    
    // Convert quality enum to numeric value
    final qualityValue = _qualityToNumber(quality);
    
    if (qualityValue >= 3) {
      // Correct response
      if (repetitions == 0) {
        interval = 1;
      } else if (repetitions == 1) {
        interval = 6;
      } else {
        interval = (interval * easeFactor).round();
      }
      repetitions++;
    } else {
      // Incorrect response - reset repetitions
      repetitions = 0;
      interval = 1;
    }
    
    // Update ease factor
    easeFactor = easeFactor + (0.1 - (5 - qualityValue) * (0.08 + (5 - qualityValue) * 0.02));
    easeFactor = max(1.3, easeFactor); // Minimum ease factor
    
    // Calculate next review date
    final nextReviewDate = DateTime.now().add(Duration(days: interval));
    
    return CardReview(
      easeFactor: easeFactor,
      interval: interval,
      repetitions: repetitions,
      nextReviewDate: nextReviewDate,
      lastReviewedAt: DateTime.now(),
      lastQuality: quality,
    );
  }
  
  /// Get cards that are due for review
  static List<FlashCard> getDueCards(List<FlashCard> allCards) {
    final now = DateTime.now();
    
    return allCards.where((card) {
      final nextReview = card.review.nextReviewDate;
      return nextReview == null || nextReview.isBefore(now) || nextReview.isAtSameMomentAs(now);
    }).toList();
  }
  
  /// Sort cards by priority (overdue cards first, then by ease factor)
  static List<FlashCard> sortCardsByPriority(List<FlashCard> cards) {
    final now = DateTime.now();
    
    cards.sort((a, b) {
      final aNextReview = a.review.nextReviewDate;
      final bNextReview = b.review.nextReviewDate;
      
      // Cards without review date go first
      if (aNextReview == null && bNextReview == null) {
        return a.review.easeFactor.compareTo(b.review.easeFactor);
      }
      if (aNextReview == null) return -1;
      if (bNextReview == null) return 1;
      
      // Overdue cards first
      final aOverdue = aNextReview.isBefore(now);
      final bOverdue = bNextReview.isBefore(now);
      
      if (aOverdue && !bOverdue) return -1;
      if (!aOverdue && bOverdue) return 1;
      
      if (aOverdue && bOverdue) {
        // Both overdue - prioritize by how overdue they are
        final aDaysOverdue = now.difference(aNextReview).inDays;
        final bDaysOverdue = now.difference(bNextReview).inDays;
        final comparison = bDaysOverdue.compareTo(aDaysOverdue);
        
        if (comparison != 0) return comparison;
        
        // If equally overdue, prioritize by ease factor (harder cards first)
        return a.review.easeFactor.compareTo(b.review.easeFactor);
      }
      
      // Both due today or future - prioritize by ease factor
      return a.review.easeFactor.compareTo(b.review.easeFactor);
    });
    
    return cards;
  }
  
  /// Calculate retention rate for a deck
  static double calculateRetentionRate(List<FlashCard> cards) {
    if (cards.isEmpty) return 0.0;
    
    final reviewedCards = cards.where((card) => 
      card.review.lastReviewedAt != null && 
      card.review.repetitions > 0
    ).toList();
    
    if (reviewedCards.isEmpty) return 0.0;
    
    final correctCards = reviewedCards.where((card) {
      final quality = card.review.lastQuality;
      return quality == CardQuality.good || 
             quality == CardQuality.easy;
    }).length;
    
    return correctCards / reviewedCards.length;
  }
  
  /// Get statistics for a deck
  static DeckStatistics calculateDeckStatistics(List<FlashCard> cards) {
    final now = DateTime.now();
    final total = cards.length;
    
    if (total == 0) {
      return const DeckStatistics(
        totalCards: 0,
        newCards: 0,
        dueCards: 0,
        overdueCards: 0,
        masteredCards: 0,
        retentionRate: 0.0,
        averageEaseFactor: 2.5,
      );
    }
    
    final newCards = cards.where((card) => card.review.repetitions == 0).length;
    final dueCards = getDueCards(cards).length;
    
    final overdueCards = cards.where((card) {
      final nextReview = card.review.nextReviewDate;
      return nextReview != null && nextReview.isBefore(now);
    }).length;
    
    final masteredCards = cards.where((card) => 
      card.review.repetitions >= 5 && 
      card.review.easeFactor >= 2.5
    ).length;
    
    final retentionRate = calculateRetentionRate(cards);
    
    final averageEaseFactor = cards.isNotEmpty 
      ? cards.map((card) => card.review.easeFactor).reduce((a, b) => a + b) / cards.length
      : 2.5;
    
    return DeckStatistics(
      totalCards: total,
      newCards: newCards,
      dueCards: dueCards,
      overdueCards: overdueCards,
      masteredCards: masteredCards,
      retentionRate: retentionRate,
      averageEaseFactor: averageEaseFactor,
    );
  }
  
  /// Predict next session size based on current progress
  static int predictNextSessionSize(List<FlashCard> cards, {int targetSessionSize = 20}) {
    final dueCards = getDueCards(cards);
    final sortedCards = sortCardsByPriority(dueCards);
    
    return min(sortedCards.length, targetSessionSize);
  }
  
  static int _qualityToNumber(CardQuality quality) {
    switch (quality) {
      case CardQuality.none:
      case CardQuality.blackout:
        return 0;
      case CardQuality.incorrect:
        return 1;
      case CardQuality.hard:
        return 2;
      case CardQuality.good:
        return 3;
      case CardQuality.easy:
        return 4;
    }
  }
}

class DeckStatistics {
  final int totalCards;
  final int newCards;
  final int dueCards;
  final int overdueCards;
  final int masteredCards;
  final double retentionRate;
  final double averageEaseFactor;
  
  const DeckStatistics({
    required this.totalCards,
    required this.newCards,
    required this.dueCards,
    required this.overdueCards,
    required this.masteredCards,
    required this.retentionRate,
    required this.averageEaseFactor,
  });
  
  /// Get a description of the deck's current state
  String get statusDescription {
    if (totalCards == 0) return '카드가 없습니다';
    if (newCards == totalCards) return '모든 카드가 새 카드입니다';
    if (dueCards == 0) return '복습할 카드가 없습니다';
    if (overdueCards > 0) return '$overdueCards개의 미뤄진 카드가 있습니다';
    return '$dueCards개의 카드가 복습 대기 중입니다';
  }
  
  /// Get priority level for the deck (0=low, 1=medium, 2=high)
  int get priorityLevel {
    if (overdueCards > 5) return 2; // High priority
    if (dueCards > 10 || overdueCards > 0) return 1; // Medium priority
    return 0; // Low priority
  }
}