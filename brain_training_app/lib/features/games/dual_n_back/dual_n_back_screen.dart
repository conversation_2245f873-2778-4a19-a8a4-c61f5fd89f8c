import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/extensions.dart';
import 'dual_n_back_provider.dart';

class DualNBackScreen extends ConsumerStatefulWidget {
  const DualNBackScreen({super.key});

  @override
  ConsumerState<DualNBackScreen> createState() => _DualNBackScreenState();
}

class _DualNBackScreenState extends ConsumerState<DualNBackScreen> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Reset game state when screen is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_hasInitialized) {
        ref.read(dualNBackProvider.notifier).resetGame();
        _hasInitialized = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(dualNBackProvider);

    return PopScope(
      canPop: _canPop(gameState.phase),
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackButton(context, ref, gameState.phase);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Dual N-Back'),
          actions: [
            if (gameState.phase == DualNBackPhase.playing)
              IconButton(
                icon: const Icon(Icons.pause),
                onPressed: () => ref.read(dualNBackProvider.notifier).pauseGame(),
              ),
          ],
        ),
        body: _buildGameBody(context, ref, gameState),
      ),
    );
  }

  bool _canPop(DualNBackPhase phase) {
    // Allow immediate pop on setup, completed, and results screens
    return phase == DualNBackPhase.setup || 
           phase == DualNBackPhase.completed || 
           phase == DualNBackPhase.results;
  }

  void _handleBackButton(BuildContext context, WidgetRef ref, DualNBackPhase phase) {
    // For active game phases, show confirmation dialog
    if (phase == DualNBackPhase.playing ||
        phase == DualNBackPhase.instructions ||
        phase == DualNBackPhase.stimulusDisplay ||
        phase == DualNBackPhase.waitingForResponse ||
        phase == DualNBackPhase.betweenTrials) {
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('게임 종료'),
          content: const Text('게임을 종료하시겠습니까?\n현재 진행 중인 게임이 저장되지 않습니다.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('계속하기'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
                ref.read(dualNBackProvider.notifier).stopGame();
                Navigator.pop(context); // Exit game screen
              },
              child: const Text('종료'),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildGameBody(BuildContext context, WidgetRef ref, DualNBackState state) {
    switch (state.phase) {
      case DualNBackPhase.setup:
        return _buildSetupScreen(context, ref);
      case DualNBackPhase.instructions:
        return _buildInstructionsScreen(context, ref, state);
      case DualNBackPhase.playing:
      case DualNBackPhase.stimulusDisplay:
      case DualNBackPhase.waitingForResponse:
      case DualNBackPhase.betweenTrials:
        return _buildGameScreen(context, ref, state);
      case DualNBackPhase.completed:
        return _buildCompletedScreen(context, ref);
      case DualNBackPhase.results:
        return _buildResultsScreen(context, ref, state);
    }
  }

  Widget _buildSetupScreen(BuildContext context, WidgetRef ref) {
    final orientation = MediaQuery.of(context).orientation;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = MediaQuery.of(context).size.shortestSide >= 600;
    final padding = MediaQuery.of(context).padding;
    
    // Adjust spacing and icon size based on orientation and screen size
    final iconSize = (orientation == Orientation.landscape) ? 60.0 : 100.0;
    final spacing = (orientation == Orientation.landscape) ? 12.0 : 24.0;
    final buttonSpacing = (orientation == Orientation.landscape) ? 8.0 : 16.0;
    final horizontalPadding = (orientation == Orientation.landscape && isTablet) ? 64.0 : 32.0;
    
    return LayoutBuilder(
      builder: (context, constraints) {
        return SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Container(
                constraints: BoxConstraints(
                  minHeight: orientation == Orientation.landscape 
                      ? constraints.maxHeight - 32
                      : screenHeight - padding.top - padding.bottom - 32,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.psychology,
                      size: iconSize,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(height: spacing),
                    Text(
                      'Dual N-Back',
                      style: context.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: spacing * 0.7),
                    Container(
                      width: orientation == Orientation.landscape 
                          ? screenWidth * 0.6 
                          : double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: Text(
                        '시각적 위치와 청각적 자극을 동시에 기억하는 작업기억 훈련 게임입니다.',
                        textAlign: TextAlign.center,
                        style: context.textTheme.bodyLarge,
                      ),
                    ),
                    SizedBox(height: spacing * 2),
                    SizedBox(
                      width: orientation == Orientation.landscape ? 300 : double.infinity,
                      child: ElevatedButton(
                        onPressed: () => ref.read(dualNBackProvider.notifier).startGame(),
                        child: const Text('게임 시작'),
                      ),
                    ),
                    SizedBox(height: buttonSpacing),
                    SizedBox(
                      width: orientation == Orientation.landscape ? 300 : double.infinity,
                      child: OutlinedButton(
                        onPressed: () => _showDifficultyDialog(context, ref),
                        child: const Text('난이도 선택'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInstructionsScreen(BuildContext context, WidgetRef ref, DualNBackState state) {
    final orientation = MediaQuery.of(context).orientation;
    final screenHeight = MediaQuery.of(context).size.height;
    final padding = orientation == Orientation.landscape ? 16.0 : 24.0;
    final spacing = orientation == Orientation.landscape ? 16.0 : 32.0;
    final cardSpacing = orientation == Orientation.landscape ? 12.0 : 16.0;
    
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(padding),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: screenHeight - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom - (padding * 2),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${state.nLevel}-Back 게임',
                style: context.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: spacing),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.visibility,
                        size: 40,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '시각적 자극',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '격자에서 파란색 사각형이 나타납니다.\n${state.nLevel}번째 전과 같은 위치면 "시각" 버튼을 누르세요.',
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: cardSpacing),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.hearing,
                        size: 40,
                        color: AppTheme.secondaryColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '청각적 자극',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '소리가 재생됩니다.\n${state.nLevel}번째 전과 같은 소리면 "청각" 버튼을 누르세요.',
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: spacing),
              SizedBox(
                width: orientation == Orientation.landscape ? 300 : double.infinity,
                child: ElevatedButton(
                  onPressed: () => ref.read(dualNBackProvider.notifier).skipInstructions(),
                  child: const Text('게임 시작하기'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameScreen(BuildContext context, WidgetRef ref, DualNBackState state) {
    final orientation = MediaQuery.of(context).orientation;
    final isTablet = MediaQuery.of(context).size.shortestSide >= 600;
    
    if (orientation == Orientation.landscape && isTablet) {
      return _buildLandscapeLayout(context, ref, state);
    } else {
      return _buildPortraitLayout(context, ref, state);
    }
  }

  Widget _buildPortraitLayout(BuildContext context, WidgetRef ref, DualNBackState state) {
    return Column(
      children: [
        // Progress and stats
        _buildGameHeader(context, state),
        
        // Game grid
        Expanded(
          flex: 3,
          child: _buildGameGrid(context, state),
        ),
        
        // Control buttons
        Expanded(
          flex: 1,
          child: _buildControlButtons(context, ref, state),
        ),
      ],
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, WidgetRef ref, DualNBackState state) {
    return Column(
      children: [
        // Progress and stats - more compact in landscape
        _buildGameHeader(context, state),
        
        // Main content area
        Expanded(
          child: Row(
            children: [
              // Game grid - centered and properly sized
              Expanded(
                flex: 2,
                child: Center(
                  child: _buildGameGrid(context, state),
                ),
              ),
              
              // Control buttons - right side
              Expanded(
                flex: 1,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: _buildControlButtons(context, ref, state),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGameHeader(BuildContext context, DualNBackState state) {
    final progress = state.totalTrials > 0 ? state.currentTrial / state.totalTrials : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${state.nLevel}-Back',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${state.currentTrial}/${state.totalTrials}',
                style: context.textTheme.titleMedium,
              ),
              Text(
                state.elapsed.formatted,
                style: context.textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: context.colors.outline.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(context.colors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildGameGrid(BuildContext context, DualNBackState state) {
    final orientation = MediaQuery.of(context).orientation;
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.shortestSide >= 600;
    
    // Calculate optimal grid size based on orientation and device type
    double gridSize;
    double margin;
    
    if (orientation == Orientation.landscape && isTablet) {
      // In landscape on tablets, use a smaller grid that fits well with the layout
      gridSize = (screenSize.height - 200).clamp(200.0, 400.0); // Leave space for header
      margin = 16.0;
    } else {
      // Original behavior for portrait or phones
      gridSize = (screenSize.width - 64).clamp(200.0, 400.0);
      margin = 32.0;
    }
    
    return Center(
      child: SizedBox(
        width: gridSize,
        height: gridSize,
        child: Container(
          margin: EdgeInsets.all(margin),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 9,
            itemBuilder: (context, index) {
              final isActive = state.isVisualStimulusActive && 
                              state.currentPosition?.index == index;
              
              return Container(
                decoration: BoxDecoration(
                  color: isActive 
                      ? AppTheme.primaryColor 
                      : context.colors.outline.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: context.colors.outline.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context, WidgetRef ref, DualNBackState state) {
    final canRespond = state.phase == DualNBackPhase.waitingForResponse;
    final orientation = MediaQuery.of(context).orientation;
    final isTablet = MediaQuery.of(context).size.shortestSide >= 600;
    
    final padding = (orientation == Orientation.landscape && isTablet) 
        ? const EdgeInsets.all(16)
        : const EdgeInsets.all(24);
    
    return Padding(
      padding: padding,
      child: _DualNBackControls(
        canRespond: canRespond,
        onSubmit: (visual, audio) => _submitResponse(ref, visual: visual, audio: audio),
        isLandscape: orientation == Orientation.landscape && isTablet,
      ),
    );
  }

  Widget _buildCompletedScreen(BuildContext context, WidgetRef ref) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 24),
          Text('게임 결과를 저장하는 중...'),
        ],
      ),
    );
  }

  Widget _buildResultsScreen(BuildContext context, WidgetRef ref, DualNBackState state) {
    final results = state.results!;
    final orientation = MediaQuery.of(context).orientation;
    final screenHeight = MediaQuery.of(context).size.height;
    final padding = orientation == Orientation.landscape ? 16.0 : 24.0;
    final spacing = orientation == Orientation.landscape ? 16.0 : 24.0;
    final cardSpacing = orientation == Orientation.landscape ? 12.0 : 24.0;
    
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(padding),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: screenHeight - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom - (padding * 2),
          ),
          child: Column(
            children: [
              Text(
                '게임 완료!',
                style: context.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: spacing),
              
              // Score card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        '점수: ${results.score}',
                        style: context.textTheme.headlineSmall?.copyWith(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      orientation == Orientation.landscape
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                _buildStatColumn('전체 정확도', '${(results.overallAccuracy * 100).toInt()}%'),
                                _buildStatColumn('시각 정확도', '${(results.visualAccuracy * 100).toInt()}%'),
                                _buildStatColumn('청각 정확도', '${(results.audioAccuracy * 100).toInt()}%'),
                              ],
                            )
                          : Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    _buildStatColumn('전체 정확도', '${(results.overallAccuracy * 100).toInt()}%'),
                                    _buildStatColumn('시각 정확도', '${(results.visualAccuracy * 100).toInt()}%'),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                _buildStatColumn('청각 정확도', '${(results.audioAccuracy * 100).toInt()}%'),
                              ],
                            ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: cardSpacing),
              
              // Detailed stats
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '상세 결과',
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildResultRow('N-Level', '${results.nLevel}'),
                      _buildResultRow('총 시행', '${results.totalTrials}'),
                      _buildResultRow('준비 시행', '${results.nLevel}'),
                      _buildResultRow('응답 시행', '${results.totalTrials - results.nLevel}'),
                      _buildResultRow('시각 정답', '${results.correctVisual}'),
                      _buildResultRow('청각 정답', '${results.correctAudio}'),
                      _buildResultRow('평균 반응시간', results.averageResponseTime.formatted),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: spacing),
              
              // Action buttons
              orientation == Orientation.landscape
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 150,
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('메인으로'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        SizedBox(
                          width: 150,
                          child: ElevatedButton(
                            onPressed: () {
                              ref.read(dualNBackProvider.notifier).resetGame();
                            },
                            child: const Text('다시 하기'),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('메인으로'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              ref.read(dualNBackProvider.notifier).resetGame();
                            },
                            child: const Text('다시 하기'),
                          ),
                        ),
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildResultRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _submitResponse(WidgetRef ref, {required bool visual, required bool audio}) {
    ref.read(dualNBackProvider.notifier).submitResponse(
      visualMatch: visual,
      audioMatch: audio,
    );
  }

  void _showDifficultyDialog(BuildContext context, WidgetRef ref) {
    final orientation = MediaQuery.of(context).orientation;
    final screenHeight = MediaQuery.of(context).size.height;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('난이도 선택'),
        content: SizedBox(
          width: orientation == Orientation.landscape ? 400 : double.maxFinite,
          height: orientation == Orientation.landscape 
              ? (screenHeight * 0.4).clamp(200.0, 300.0)
              : null,
          child: orientation == Orientation.landscape
              ? SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      for (int level = 1; level <= 4; level++)
                        _buildDifficultyTile(context, ref, level),
                    ],
                  ),
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    for (int level = 1; level <= 4; level++)
                      _buildDifficultyTile(context, ref, level),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildDifficultyTile(BuildContext context, WidgetRef ref, int level) {
    return ListTile(
      title: Text('$level-Back'),
      subtitle: Text(_getDifficultyDescription(level)),
      onTap: () {
        Navigator.pop(context);
        ref.read(dualNBackProvider.notifier).startGame(customNLevel: level);
      },
    );
  }

  String _getDifficultyDescription(int level) {
    switch (level) {
      case 1: return '초급 - 1번째 전 자극과 비교';
      case 2: return '중급 - 2번째 전 자극과 비교';
      case 3: return '고급 - 3번째 전 자극과 비교';
      case 4: return '전문가 - 4번째 전 자극과 비교';
      default: return '';
    }
  }
}

class _DualNBackControls extends StatelessWidget {
  final bool canRespond;
  final Function(bool visual, bool audio) onSubmit;
  final bool isLandscape;

  const _DualNBackControls({
    required this.canRespond,
    required this.onSubmit,
    this.isLandscape = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLandscape) {
      return _buildLandscapeControls();
    } else {
      return _buildPortraitControls();
    }
  }

  Widget _buildPortraitControls() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: canRespond ? () => onSubmit(true, false) : null,
                icon: const Icon(Icons.visibility),
                label: const Text('시각 매치'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: canRespond ? () => onSubmit(false, true) : null,
                icon: const Icon(Icons.hearing),
                label: const Text('청각 매치'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: canRespond ? () => onSubmit(true, true) : null,
                icon: const Icon(Icons.done_all),
                label: const Text('둘 다'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: canRespond ? () => onSubmit(false, false) : null,
                icon: const Icon(Icons.close),
                label: const Text('매치 없음'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLandscapeControls() {
    // In landscape mode, stack buttons vertically with better spacing
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: canRespond ? () => onSubmit(true, false) : null,
            icon: const Icon(Icons.visibility),
            label: const Text('시각 매치'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: canRespond ? () => onSubmit(false, true) : null,
            icon: const Icon(Icons.hearing),
            label: const Text('청각 매치'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: canRespond ? () => onSubmit(true, true) : null,
            icon: const Icon(Icons.done_all),
            label: const Text('둘 다'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: canRespond ? () => onSubmit(false, false) : null,
            icon: const Icon(Icons.close),
            label: const Text('매치 없음'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }
}