import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:uuid/uuid.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/extensions.dart';
import '../../../shared/models/game_result.dart';
import '../../../shared/providers/game_providers.dart';
import '../../../shared/providers/user_providers.dart';
import '../../../shared/providers/achievement_providers.dart';
import '../../../core/services/notification_service.dart';
import '../../../features/auth/auth_provider.dart';
import '../../../shared/models/game_session.dart';
import '../../../shared/models/user.dart';
import '../../../core/constants/game_constants.dart';
import '../game_repository.dart';
import 'dual_n_back_logic.dart';

final dualNBackProvider =
    StateNotifierProvider<DualNBackNotifier, DualNBackState>((ref) {
  return DualNBackNotifier(ref);
});

enum DualNBackPhase {
  setup,
  instructions,
  playing,
  stimulusDisplay,
  waitingForResponse,
  betweenTrials,
  completed,
  results,
}

class DualNBackState {
  final DualNBackPhase phase;
  final int nLevel;
  final int currentTrial;
  final int totalTrials;
  final DualNBackLogic? gameLogic;
  final Duration elapsed;
  final bool isVisualStimulusActive;
  final Position? currentPosition;
  final AudioStimulus? currentAudio;
  final bool isAudioPlaying;
  final DualNBackResults? results;
  final String? error;
  final DateTime? trialStartTime;

  const DualNBackState({
    this.phase = DualNBackPhase.setup,
    this.nLevel = 1,
    this.currentTrial = 0,
    this.totalTrials = 20,
    this.gameLogic,
    this.elapsed = Duration.zero,
    this.isVisualStimulusActive = false,
    this.currentPosition,
    this.currentAudio,
    this.isAudioPlaying = false,
    this.results,
    this.error,
    this.trialStartTime,
  });

  DualNBackState copyWith({
    DualNBackPhase? phase,
    int? nLevel,
    int? currentTrial,
    int? totalTrials,
    DualNBackLogic? gameLogic,
    Duration? elapsed,
    bool? isVisualStimulusActive,
    Position? currentPosition,
    AudioStimulus? currentAudio,
    bool? isAudioPlaying,
    DualNBackResults? results,
    String? error,
    DateTime? trialStartTime,
  }) {
    return DualNBackState(
      phase: phase ?? this.phase,
      nLevel: nLevel ?? this.nLevel,
      currentTrial: currentTrial ?? this.currentTrial,
      totalTrials: totalTrials ?? this.totalTrials,
      gameLogic: gameLogic ?? this.gameLogic,
      elapsed: elapsed ?? this.elapsed,
      isVisualStimulusActive: isVisualStimulusActive ?? this.isVisualStimulusActive,
      currentPosition: currentPosition ?? this.currentPosition,
      currentAudio: currentAudio ?? this.currentAudio,
      isAudioPlaying: isAudioPlaying ?? this.isAudioPlaying,
      results: results ?? this.results,
      error: error ?? this.error,
      trialStartTime: trialStartTime ?? this.trialStartTime,
    );
  }
}

class DualNBackNotifier extends StateNotifier<DualNBackState> {
  final Ref _ref;
  Timer? _gameTimer;
  Timer? _stimulusTimer;
  late AudioPlayer _audioPlayer;
  DateTime? _sessionStartTime;

  DualNBackNotifier(this._ref) : super(const DualNBackState()) {
    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _stimulusTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  void startGame({int? customNLevel}) {
    final nLevel = customNLevel ?? _calculateRecommendedNLevel();
    final gameLogic = DualNBackLogic(
      nLevel: nLevel,
      totalTrials: 20,
      stimulusDuration: AppConstants.stimulusDuration,
      interstimulusInterval: AppConstants.interstimulusInterval,
    );

    _sessionStartTime = DateTime.now();

    state = state.copyWith(
      phase: DualNBackPhase.instructions,
      nLevel: nLevel,
      gameLogic: gameLogic,
      currentTrial: 0,
      totalTrials: gameLogic.actualTotalTrials,
      elapsed: Duration.zero,
      error: null,
      results: null,
    );
  }

  void skipInstructions() {
    if (state.phase == DualNBackPhase.instructions) {
      _startFirstTrial();
    }
  }

  void _startFirstTrial() {
    state = state.copyWith(phase: DualNBackPhase.playing);
    _startGameTimer();
    _nextTrial();
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      state = state.copyWith(
        elapsed: DateTime.now().difference(_sessionStartTime!),
      );
    });
  }

  void _nextTrial() {
    if (state.gameLogic?.isComplete == true) {
      _completeGame();
      return;
    }

    final logic = state.gameLogic!;
    final currentPos = logic.currentVisualStimulus;
    final currentAudio = logic.currentAudioStimulus;

    state = state.copyWith(
      currentTrial: logic.currentTrialIndex + 1,
      totalTrials: logic.actualTotalTrials,
      currentPosition: currentPos,
      currentAudio: currentAudio,
      phase: DualNBackPhase.stimulusDisplay,
      trialStartTime: DateTime.now(),
    );

    _showStimulus();
  }

  void _showStimulus() {
    // Show visual stimulus
    state = state.copyWith(isVisualStimulusActive: true);

    // Play audio stimulus
    _playAudioStimulus(state.currentAudio!);

    // Hide stimulus after duration
    Timer(AppConstants.stimulusDuration, () {
      state = state.copyWith(
        isVisualStimulusActive: false,
        phase: _isEarlyTrial() ? DualNBackPhase.betweenTrials : DualNBackPhase.waitingForResponse,
      );
    });

    // Wait for full interstimulus interval from trial start before next trial
    _stimulusTimer = Timer(AppConstants.interstimulusInterval, () {
      if (_isEarlyTrial()) {
        // Early trials - no user response needed, just advance trial counter and continue
        state.gameLogic?.submitResponse(
          visualResponse: false,
          audioResponse: false,
          responseTime: Duration.zero,
        );
        _nextTrial();
      } else if (state.phase == DualNBackPhase.waitingForResponse) {
        // Timeout - submit no response and advance to next trial
        final responseTime = DateTime.now().difference(state.trialStartTime!);
        state.gameLogic?.submitResponse(
          visualResponse: false,
          audioResponse: false,
          responseTime: responseTime,
        );
        _nextTrial();
      } else {
        // User already responded, just continue to next trial
        _nextTrial();
      }
    });
  }

  bool _isEarlyTrial() {
    return state.currentTrial <= state.nLevel;
  }

  void _playAudioStimulus(AudioStimulus audio) async {
    try {
      state = state.copyWith(isAudioPlaying: true);
      
      // Play the actual audio file
      await _audioPlayer.play(AssetSource('sounds/${audio.sound}.wav'));
      
      // Audio will stop automatically when the file finishes playing
      // The isAudioPlaying flag will be reset when the audio completes
      _audioPlayer.onPlayerComplete.listen((_) {
        state = state.copyWith(isAudioPlaying: false);
      });
      
    } catch (e) {
      state = state.copyWith(
        isAudioPlaying: false,
        error: 'Audio playback failed: $e',
      );
    }
  }

  void submitResponse({required bool visualMatch, required bool audioMatch}) {
    // Don't accept responses during early trials or if not in waiting phase
    if (_isEarlyTrial() || state.phase != DualNBackPhase.waitingForResponse) return;

    final responseTime = DateTime.now().difference(state.trialStartTime!);
    
    state.gameLogic?.submitResponse(
      visualResponse: visualMatch,
      audioResponse: audioMatch,
      responseTime: responseTime,
    );

    state = state.copyWith(phase: DualNBackPhase.betweenTrials);
    
    // No additional delay - let the main timer handle the timing
  }

  void _completeGame() async {
    _gameTimer?.cancel();
    _stimulusTimer?.cancel();

    final results = state.gameLogic?.calculateResults();
    
    state = state.copyWith(
      phase: DualNBackPhase.completed,
      results: results,
    );

    await _saveGameSession();
  }

  Future<void> _saveGameSession() async {
    try {
      if (state.results == null || _sessionStartTime == null) {
        state = state.copyWith(phase: DualNBackPhase.results);
        return;
      }

      final results = state.results!;
      final user = _ref.read(authStateProvider).value;
      final userId = user?.id ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';
      final endTime = DateTime.now();
      
      final sessionId = '${userId}_dualNBack_${endTime.millisecondsSinceEpoch}';

      final gameSession = GameSession(
        id: sessionId,
        userId: userId,
        gameType: GameType.dualNBack,
        level: results.nLevel,
        accuracy: results.overallAccuracy,
        duration: state.elapsed,
        score: results.score,
        startTime: _sessionStartTime!,
        endTime: endTime,
        gameSpecificData: {
          'totalTrials': results.totalTrials,
          'correctVisual': results.correctVisual,
          'correctAudio': results.correctAudio,
          'falsePositivesVisual': results.falsePositivesVisual,
          'falsePositivesAudio': results.falsePositivesAudio,
          'missesVisual': results.missesVisual,
          'missesAudio': results.missesAudio,
          'visualAccuracy': results.visualAccuracy,
          'audioAccuracy': results.audioAccuracy,
          'averageResponseTime': results.averageResponseTime.inMilliseconds,
          'trials': results.trials?.map((t) => {
            'index': t.index,
            'visualPosition': t.visualPosition?.index,
            'audioStimulus': t.audioStimulus?.sound,
            'hasVisualMatch': t.hasVisualMatch,
            'hasAudioMatch': t.hasAudioMatch,
            'userVisualResponse': t.userVisualResponse,
            'userAudioResponse': t.userAudioResponse,
            'responseTime': t.responseTime?.inMilliseconds,
            'isCorrectVisual': t.isCorrectVisual,
            'isCorrectAudio': t.isCorrectAudio,
          }).toList(),
        },
        xpEarned: _calculateXP(),
        isCompleted: true,
        events: [],
      );

      await _ref.read(gameServiceProvider).saveGameSession(gameSession);

      state = state.copyWith(phase: DualNBackPhase.results);
    } catch (e) {
      state = state.copyWith(
        phase: DualNBackPhase.results,
        error: 'Failed to save game: $e',
      );
    }
  }

  int _calculateXP() {
    final baseXP = GameConstants.baseXpRewards[GameType.dualNBack] ?? 100;
    final performanceMultiplier = state.results!.overallAccuracy;
    final levelBonus = state.nLevel * 10;
    
    // Only award XP based on actual performance
    if (performanceMultiplier < 0.1) {
      // Very poor performance gets minimal XP
      return (baseXP * 0.1).round();
    }
    
    return ((baseXP + levelBonus) * performanceMultiplier).round();
  }

  bool _isNewStreak(UserStats stats) {
    final lastPlayed = stats.lastGamePlayedAt;
    if (lastPlayed == null) return true;
    
    final now = DateTime.now();
    final daysDiff = now.difference(lastPlayed).inDays;
    
    return daysDiff == 1 && !lastPlayed.isToday;
  }

  int _calculateRecommendedNLevel() {
    // Start with 1-back for new users
    // TODO: Implement adaptive difficulty based on user's performance history
    return 1;
  }

  void resetGame() {
    _gameTimer?.cancel();
    _stimulusTimer?.cancel();
    _audioPlayer.stop();
    _sessionStartTime = null;
    
    state = const DualNBackState();
  }

  void pauseGame() {
    _gameTimer?.cancel();
    _stimulusTimer?.cancel();
  }

  void resumeGame() {
    if (state.phase == DualNBackPhase.playing) {
      _startGameTimer();
    }
  }

  void stopGame() {
    _gameTimer?.cancel();
    _stimulusTimer?.cancel();
    _audioPlayer.stop();
    _sessionStartTime = null;
    
    // Reset to initial state
    state = const DualNBackState();
  }
}
