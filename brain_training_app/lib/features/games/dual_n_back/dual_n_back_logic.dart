import 'dart:math';
import '../../../core/constants/game_constants.dart';
import '../../../shared/models/game_session.dart';

class DualNBackLogic {
  final int nLevel;
  final int totalTrials;
  final Duration stimulusDuration;
  final Duration interstimulusInterval;
  
  late List<Position> _visualSequence;
  late List<AudioStimulus> _audioSequence;
  late List<bool> _visualMatches;
  late List<bool> _audioMatches;
  
  int _currentTrial = 0;
  final List<bool> _userVisualResponses = [];
  final List<bool> _userAudioResponses = [];
  final List<Duration> _responseTimes = [];

  DualNBackLogic({
    required this.nLevel,
    this.totalTrials = 20, // This represents the number of response trials
    this.stimulusDuration = const Duration(milliseconds: 1000),
    this.interstimulusInterval = const Duration(milliseconds: 4000),
  }) {
    _initializeGame();
  }
  
  // Total trials including warm-up trials
  int get actualTotalTrials => totalTrials + nLevel;

  void _initializeGame() {
    _currentTrial = 0;
    _userVisualResponses.clear();
    _userAudioResponses.clear();
    _responseTimes.clear();
    _generateSequences();
  }

  void reset() {
    _initializeGame();
  }

  void _generateSequences() {
    final random = Random();
    _visualSequence = [];
    _audioSequence = [];
    _visualMatches = [];
    _audioMatches = [];

    // Generate random sequences for warm-up + response trials
    for (int i = 0; i < actualTotalTrials; i++) {
      // Visual position (0-8 for 3x3 grid)
      int visualPos = random.nextInt(9);
      
      // Audio stimulus (0-8 for different sounds)
      int audioPos = random.nextInt(GameConstants.dualNBackSounds.length);
      
      _visualSequence.add(Position.fromIndex(visualPos));
      _audioSequence.add(AudioStimulus.fromIndex(audioPos));
      
      // Determine if this trial should match N-back
      bool visualMatch = false;
      bool audioMatch = false;
      
      if (i >= nLevel) {
        // 30% chance of match for balanced difficulty
        if (random.nextDouble() < 0.3) {
          visualMatch = true;
          _visualSequence[i] = _visualSequence[i - nLevel];
        }
        
        if (random.nextDouble() < 0.3) {
          audioMatch = true;
          _audioSequence[i] = _audioSequence[i - nLevel];
        }
      }
      
      _visualMatches.add(visualMatch);
      _audioMatches.add(audioMatch);
    }
  }

  bool get isComplete => _currentTrial >= actualTotalTrials;
  
  int get currentTrialIndex => _currentTrial;
  
  Position get currentVisualStimulus {
    if (_currentTrial >= _visualSequence.length) {
      return Position.fromIndex(0); // Return default position if out of bounds
    }
    return _visualSequence[_currentTrial];
  }
  
  AudioStimulus get currentAudioStimulus {
    if (_currentTrial >= _audioSequence.length) {
      return AudioStimulus.fromIndex(0); // Return default audio if out of bounds
    }
    return _audioSequence[_currentTrial];
  }
  
  bool get shouldMatchVisual => _currentTrial >= nLevel && 
      _currentTrial < _visualMatches.length && _visualMatches[_currentTrial];
  
  bool get shouldMatchAudio => _currentTrial >= nLevel && 
      _currentTrial < _audioMatches.length && _audioMatches[_currentTrial];

  void submitResponse({required bool visualResponse, required bool audioResponse, required Duration responseTime}) {
    if (isComplete) return;
    
    _userVisualResponses.add(visualResponse);
    _userAudioResponses.add(audioResponse);
    _responseTimes.add(responseTime);
    
    _currentTrial++;
  }

  DualNBackResults calculateResults() {
    int correctVisual = 0;
    int correctAudio = 0;
    int falsePositivesVisual = 0;
    int falsePositivesAudio = 0;
    int missesVisual = 0;
    int missesAudio = 0;

    for (int i = 0; i < _userVisualResponses.length; i++) {
      final shouldMatchVis = i >= nLevel && _visualMatches[i];
      final shouldMatchAud = i >= nLevel && _audioMatches[i];
      final userVis = _userVisualResponses[i];
      final userAud = _userAudioResponses[i];

      // Visual scoring
      if (shouldMatchVis && userVis) {
        correctVisual++;
      } else if (shouldMatchVis && !userVis) {
        missesVisual++;
      } else if (!shouldMatchVis && userVis) {
        falsePositivesVisual++;
      }

      // Audio scoring
      if (shouldMatchAud && userAud) {
        correctAudio++;
      } else if (shouldMatchAud && !userAud) {
        missesAudio++;
      } else if (!shouldMatchAud && userAud) {
        falsePositivesAudio++;
      }
    }

    final actualVisualTargets = _visualMatches.where((match) => match).length;
    final actualAudioTargets = _audioMatches.where((match) => match).length;

    final visualAccuracy = actualVisualTargets > 0 ? correctVisual / actualVisualTargets : 1.0;
    final audioAccuracy = actualAudioTargets > 0 ? correctAudio / actualAudioTargets : 1.0;
    final overallAccuracy = (visualAccuracy + audioAccuracy) / 2;

    final averageResponseTime = _responseTimes.isNotEmpty 
        ? _responseTimes.reduce((a, b) => a + b) ~/ _responseTimes.length
        : Duration.zero;

    return DualNBackResults(
      nLevel: nLevel,
      totalTrials: actualTotalTrials,
      correctVisual: correctVisual,
      correctAudio: correctAudio,
      falsePositivesVisual: falsePositivesVisual,
      falsePositivesAudio: falsePositivesAudio,
      missesVisual: missesVisual,
      missesAudio: missesAudio,
      visualAccuracy: visualAccuracy,
      audioAccuracy: audioAccuracy,
      overallAccuracy: overallAccuracy,
      averageResponseTime: averageResponseTime,
      trials: _generateTrialResults(),
    );
  }

  List<DualNBackTrial> _generateTrialResults() {
    final trials = <DualNBackTrial>[];
    
    for (int i = 0; i < _userVisualResponses.length; i++) {
      trials.add(DualNBackTrial(
        trialNumber: i + 1,
        visualPosition: _visualSequence[i].toString(),
        audioStimulus: _audioSequence[i].sound,
        shouldMatchVisual: i >= nLevel && _visualMatches[i],
        shouldMatchAudio: i >= nLevel && _audioMatches[i],
        userResponseVisual: _userVisualResponses[i],
        userResponseAudio: _userAudioResponses[i],
        responseTime: _responseTimes[i],
      ));
    }
    
    return trials;
  }
}

class Position {
  final int row;
  final int col;

  const Position(this.row, this.col);

  factory Position.fromIndex(int index) {
    return Position(index ~/ 3, index % 3);
  }

  int get index => row * 3 + col;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Position && runtimeType == other.runtimeType && row == other.row && col == other.col;

  @override
  int get hashCode => row.hashCode ^ col.hashCode;

  @override
  String toString() => GameConstants.dualNBackPositions[index];
}

class AudioStimulus {
  final int index;
  final String sound;

  const AudioStimulus(this.index, this.sound);

  factory AudioStimulus.fromIndex(int index) {
    return AudioStimulus(index, GameConstants.dualNBackSounds[index]);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioStimulus && runtimeType == other.runtimeType && index == other.index;

  @override
  int get hashCode => index.hashCode;
}

class DualNBackResults {
  final int nLevel;
  final int totalTrials;
  final int correctVisual;
  final int correctAudio;
  final int falsePositivesVisual;
  final int falsePositivesAudio;
  final int missesVisual;
  final int missesAudio;
  final double visualAccuracy;
  final double audioAccuracy;
  final double overallAccuracy;
  final Duration averageResponseTime;
  final List<DualNBackTrial> trials;

  const DualNBackResults({
    required this.nLevel,
    required this.totalTrials,
    required this.correctVisual,
    required this.correctAudio,
    required this.falsePositivesVisual,
    required this.falsePositivesAudio,
    required this.missesVisual,
    required this.missesAudio,
    required this.visualAccuracy,
    required this.audioAccuracy,
    required this.overallAccuracy,
    required this.averageResponseTime,
    required this.trials,
  });

  int get score => (overallAccuracy * 1000 * nLevel).round();
}