import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/models/achievement.dart';
import '../../shared/providers/achievement_providers.dart';
import '../../features/auth/auth_provider.dart';
import '../../core/utils/extensions.dart';

class AchievementScreen extends ConsumerWidget {
  const AchievementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    
    return authState.when(
      data: (user) {
        if (user == null) {
          return const Scaffold(
            body: Center(child: Text('로그인이 필요합니다')),
          );
        }
        
        return Scaffold(
          appBar: AppBar(
            title: const Text('성취'),
            backgroundColor: context.colors.primary,
            foregroundColor: context.colors.onPrimary,
          ),
          body: DefaultTabController(
            length: 2,
            child: Column(
              children: [
                Container(
                  color: context.colors.primary,
                  child: TabBar(
                    labelColor: context.colors.onPrimary,
                    unselectedLabelColor: context.colors.onPrimary.withValues(alpha: 0.7),
                    indicatorColor: context.colors.onPrimary,
                    tabs: const [
                      Tab(text: '모든 성취'),
                      Tab(text: '최근 달성'),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _AllAchievementsTab(userId: user.id),
                      _RecentAchievementsTab(userId: user.id),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        body: Center(child: Text('오류가 발생했습니다: $error')),
      ),
    );
  }
}

class _AllAchievementsTab extends ConsumerWidget {
  final String userId;
  
  const _AllAchievementsTab({required this.userId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final achievementsAsync = ref.watch(userAchievementsProvider(userId));
    final progressAsync = ref.watch(achievementProgressProvider(userId));
    
    return achievementsAsync.when(
      data: (achievements) {
        return progressAsync.when(
          data: (progress) {
            final unlockedAchievements = achievements.where((a) => a.isUnlocked).toList();
            final lockedAchievements = achievements.where((a) => !a.isUnlocked).toList();
            
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                if (unlockedAchievements.isNotEmpty) ...[
                  Text(
                    '달성한 성취 (${unlockedAchievements.length})',
                    style: context.textTheme.headlineSmall?.copyWith(
                      color: context.colors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...unlockedAchievements.map((achievement) => 
                    _AchievementCard(
                      achievement: achievement,
                      progress: progress[achievement.id] ?? 0,
                      isUnlocked: true,
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
                if (lockedAchievements.isNotEmpty) ...[
                  Text(
                    '미달성 성취 (${lockedAchievements.length})',
                    style: context.textTheme.headlineSmall?.copyWith(
                      color: context.colors.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...lockedAchievements.map((achievement) => 
                    _AchievementCard(
                      achievement: achievement,
                      progress: progress[achievement.id] ?? 0,
                      isUnlocked: false,
                    ),
                  ),
                ],
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('진행률 로딩 오류: $error')),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('성취 로딩 오류: $error')),
    );
  }
}

class _RecentAchievementsTab extends ConsumerWidget {
  final String userId;
  
  const _RecentAchievementsTab({required this.userId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recentAchievementsAsync = ref.watch(recentAchievementsProvider(userId));
    
    return recentAchievementsAsync.when(
      data: (achievements) {
        if (achievements.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.emoji_events_outlined,
                  size: 64,
                  color: context.colors.onSurface.withValues(alpha: 0.3),
                ),
                const SizedBox(height: 16),
                Text(
                  '아직 달성한 성취가 없습니다',
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.colors.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '게임을 플레이해서 첫 번째 성취를 달성해보세요!',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurface.withValues(alpha: 0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: achievements.length,
          itemBuilder: (context, index) {
            final achievement = achievements[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: context.colors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.emoji_events,
                        color: context.colors.onPrimary,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            achievement.title,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            achievement.description,
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.colors.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: context.colors.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '+${achievement.xpReward} XP',
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: context.colors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              if (achievement.unlockedAt != null)
                                Text(
                                  achievement.unlockedAt!.timeAgo,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: context.colors.onSurface.withValues(alpha: 0.5),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('최근 성취 로딩 오류: $error')),
    );
  }
}

class _AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final int progress;
  final bool isUnlocked;

  const _AchievementCard({
    required this.achievement,
    required this.progress,
    required this.isUnlocked,
  });

  @override
  Widget build(BuildContext context) {
    final progressPercent = achievement.maxProgress > 0 
        ? (progress / achievement.maxProgress).clamp(0.0, 1.0)
        : (isUnlocked ? 1.0 : 0.0);

    return Opacity(
      opacity: isUnlocked ? 1.0 : 0.6,
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: isUnlocked 
                    ? context.colors.primary
                    : context.colors.onSurface.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                isUnlocked ? Icons.emoji_events : Icons.lock_outline,
                color: isUnlocked 
                    ? context.colors.onPrimary
                    : context.colors.onSurface.withValues(alpha: 0.5),
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.title,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isUnlocked 
                          ? context.colors.onSurface
                          : context.colors.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    achievement.description,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (!isUnlocked && achievement.maxProgress > 1) ...[
                    LinearProgressIndicator(
                      value: progressPercent,
                      backgroundColor: context.colors.onSurface.withValues(alpha: 0.1),
                      valueColor: AlwaysStoppedAnimation(context.colors.primary),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$progress / ${achievement.maxProgress}',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colors.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isUnlocked 
                              ? context.colors.primary.withValues(alpha: 0.1)
                              : context.colors.onSurface.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '+${achievement.xpReward} XP',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: isUnlocked 
                                ? context.colors.primary
                                : context.colors.onSurface.withValues(alpha: 0.5),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (isUnlocked && achievement.unlockedAt != null)
                        Text(
                          achievement.unlockedAt!.timeAgo,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: context.colors.onSurface.withValues(alpha: 0.5),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
    );
  }
}