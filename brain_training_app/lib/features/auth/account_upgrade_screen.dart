import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/themes/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../core/utils/validators.dart';
import 'auth_provider.dart';

class AccountUpgradeScreen extends ConsumerStatefulWidget {
  const AccountUpgradeScreen({super.key});

  @override
  ConsumerState<AccountUpgradeScreen> createState() => _AccountUpgradeScreenState();
}

class _AccountUpgradeScreenState extends ConsumerState<AccountUpgradeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _signInFormKey = GlobalKey<FormState>();
  final _signUpFormKey = GlobalKey<FormState>();

  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _displayNameController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isAuthenticationInProgress = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _displayNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    ref.listen<AsyncValue<void>>(authProvider, (previous, next) {
      // Only handle auth state changes if we're actually in the middle of an authentication action
      if (!_isAuthenticationInProgress) return;
      
      // Handle error states
      if (next is AsyncError) {
        _isAuthenticationInProgress = false;
        if (mounted) {
          context.showSnackBar(next.error.toString(), isError: true);
        }
      }
      // Handle successful authentication
      else if (previous is AsyncLoading && next is AsyncData) {
        _isAuthenticationInProgress = false;
        if (mounted) {
          Navigator.of(context).pop();
          context.showSnackBar('로그인되었습니다');
        }
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('로그인'),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 32),
              _buildTabBar(),
              const SizedBox(height: 24),
              SizedBox(
                height: 400,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSignInForm(authState),
                    _buildSignUpForm(authState),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              _buildSocialButtons(authState),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: const BoxDecoration(
            color: AppTheme.primaryColor,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.account_circle,
            color: Colors.white,
            size: 40,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          '계정을 만들어 더 많은 기능을 이용하세요',
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          '• 모든 게임 이용 가능\n• 클라우드 데이터 동기화\n• 상세한 진행 상황 추적\n• 친구들과 순위 경쟁',
          style: context.textTheme.bodyLarge?.copyWith(
            color: context.colors.onSurface.withAlpha(178),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: context.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: context.colors.outline.withAlpha(51)),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: context.colors.primary,
        unselectedLabelColor: context.colors.onSurface.withAlpha(153),
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: context.colors.primary.withAlpha(25),
        ),
        tabs: const [
          Tab(text: '로그인'),
          Tab(text: '회원가입'),
        ],
      ),
    );
  }

  Widget _buildSignInForm(AsyncValue<void> authState) {
    return Form(
      key: _signInFormKey,
      child: Column(
        children: [
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            decoration: const InputDecoration(
              labelText: '이메일',
              prefixIcon: Icon(Icons.email_outlined),
            ),
            validator: Validators.email,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: '비밀번호',
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
              ),
            ),
            validator: Validators.password,
            onFieldSubmitted: (_) => _signIn(),
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: _showForgotPasswordDialog,
              child: const Text('비밀번호를 잊으셨나요?'),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: authState.isLoading ? null : _signIn,
              child: authState.isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('로그인'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignUpForm(AsyncValue<void> authState) {
    return Form(
      key: _signUpFormKey,
      child: Column(
        children: [
          TextFormField(
            controller: _displayNameController,
            textInputAction: TextInputAction.next,
            decoration: const InputDecoration(
              labelText: '이름',
              prefixIcon: Icon(Icons.person_outlined),
            ),
            validator: Validators.displayName,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            decoration: const InputDecoration(
              labelText: '이메일',
              prefixIcon: Icon(Icons.email_outlined),
            ),
            validator: Validators.email,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: '비밀번호',
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
              ),
            ),
            validator: Validators.password,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: _obscureConfirmPassword,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: '비밀번호 확인',
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
              ),
            ),
            validator: (value) => Validators.confirmPassword(value, _passwordController.text),
            onFieldSubmitted: (_) => _signUp(),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: authState.isLoading ? null : _signUp,
              child: authState.isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('회원가입'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButtons(AsyncValue<void> authState) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 16),
        const Text(
          '소셜 로그인',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: authState.isLoading ? null : _signInWithGoogle,
            icon: const Icon(Icons.email, color: Colors.white),
            label: const Text('Google로 계속하기'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFDB4437),
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: null, // Disabled for now
            icon: const Icon(Icons.apple, color: Colors.white),
            label: const Text('Apple로 계속하기'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _signIn() async {
    if (!_signInFormKey.currentState!.validate()) return;

    setState(() {
      _isAuthenticationInProgress = true;
    });

    final notifier = ref.read(authProvider.notifier);
    await notifier.signInWithEmailAndPassword(
      _emailController.text.trim(),
      _passwordController.text,
    );
    
    // Error handling and navigation should be handled by ref.listen callback
  }

  void _signUp() async {
    if (!_signUpFormKey.currentState!.validate()) return;

    setState(() {
      _isAuthenticationInProgress = true;
    });

    final notifier = ref.read(authProvider.notifier);
    await notifier.createUserWithEmailAndPassword(
      _emailController.text.trim(),
      _passwordController.text,
      _displayNameController.text.trim(),
    );
    
    // Error handling and navigation should be handled by ref.listen callback
  }

  void _signInWithGoogle() async {
    setState(() {
      _isAuthenticationInProgress = true;
    });

    final notifier = ref.read(authProvider.notifier);
    await notifier.signInWithGoogle();
    
    // Error handling and navigation should be handled by ref.listen callback
  }

  void _showForgotPasswordDialog() {
    final emailController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('비밀번호 재설정'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('비밀번호 재설정 링크를 받을 이메일 주소를 입력해주세요.'),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: '이메일',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              if (emailController.text.isNotEmpty) {
                try {
                  await ref.read(authProvider.notifier).sendPasswordResetEmail(
                    emailController.text.trim(),
                  );
                  if (mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('비밀번호 재설정 링크를 이메일로 전송했습니다.')),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(e.toString()),
                        backgroundColor: Theme.of(context).colorScheme.error,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('전송'),
          ),
        ],
      ),
    );
  }
}