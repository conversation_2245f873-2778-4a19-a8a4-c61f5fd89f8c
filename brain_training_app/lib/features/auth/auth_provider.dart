import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/models/app_user.dart';
import '../../core/services/local_auth_service.dart';
import '../../shared/providers/user_providers.dart';

final localGuestUserNotifierProvider =
    StateNotifierProvider<LocalGuestUserNotifier, AppUser?>((ref) {
  return LocalGuestUserNotifier();
});

class LocalGuestUserNotifier extends StateNotifier<AppUser?> {
  LocalGuestUserNotifier() : super(null);

  Future<void> setGuestUser(AppUser user) async {
    state = user;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('guest_user_id', user.id);
  }

  Future<void> clearGuestUser() async {
    state = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('guest_user_id');
  }
}

final authStateProvider = StreamProvider<AppUser?>((ref) {
  final authService = ref.read(localAuthServiceProvider);
  final localGuestUser = ref.watch(localGuestUserNotifierProvider);

  return authService.authStateChanges.map((user) {
    if (user != null) {
      return user;
    }
    return localGuestUser;
  });
});

final currentUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (error, stackTrace) => null,
  );
});

final authProvider = AsyncNotifierProvider<AuthNotifier, void>(() {
  return AuthNotifier();
});

class AuthNotifier extends AsyncNotifier<void> {
  late LocalAuthService _authService;

  @override
  Future<void> build() async {
    _authService = ref.read(localAuthServiceProvider);
  }

  Future<void> signInWithEmailAndPassword(String email, String password) async {
    print('[DEBUG] AuthNotifier.signInWithEmailAndPassword - Setting state to AsyncLoading');
    state = const AsyncLoading();
    try {
      final appUser = await _authService.signInWithEmailAndPassword(email, password);

      if (appUser == null) {
        print('[DEBUG] AuthNotifier.signInWithEmailAndPassword - appUser is null, throwing AuthException');
        // AuthService가 실패 시 예외를 던지지 않고 null을 반환하는 경우를 처리합니다.
        throw const AuthException('이메일 또는 비밀번호가 올바르지 않습니다.');
      }

      // 로그인이 성공하면 사용자 프로필을 확인/생성합니다.
      // 이 작업은 실패하더라도 로그인 프로세스를 막지 않아야 합니다.
      final userService = ref.read(userServiceProvider);
      try {
        final existingUser = await userService.getUser(appUser.id);
        if (existingUser == null) {
          print('[DEBUG] AuthNotifier.signInWithEmailAndPassword - Creating user profile');
          await userService.createUserProfile(
            appUser.id,
            appUser.email,
            displayName: appUser.displayName,
          );
        }
      } catch (e) {
        // 오류를 기록하되, 사용자가 로그인하는 것을 막기 위해 다시 던지지는 않습니다.
        print('[ERROR] AuthNotifier.signInWithEmailAndPassword - Failed to create user profile during sign-in: $e');
      }

      // 여기까지 도달했다면 인증이 성공한 것입니다.
      print('[DEBUG] AuthNotifier.signInWithEmailAndPassword - Setting state to AsyncData (success)');
      state = const AsyncData(null);
    } catch (e, s) {
      // _authService에서 발생한 예외나 수동으로 발생시킨 AuthException을 포착합니다.
      print('[DEBUG] AuthNotifier.signInWithEmailAndPassword - Caught exception, setting state to AsyncError: $e');
      state = AsyncError(e, s);
    }
  }

  Future<void> createUserWithEmailAndPassword(
    String email,
    String password,
    String displayName,
  ) async {
    state = const AsyncLoading();
    try {
      final appUser = await _authService.createUserWithEmailAndPassword(
          email, password, displayName);

      if (appUser == null) {
        throw const AuthException('회원가입에 실패했습니다. 다시 시도해주세요.');
      }

      final userService = ref.read(userServiceProvider);
      try {
        await userService.createUserProfile(
          appUser.id,
          appUser.email,
          displayName: displayName,
        );
      } catch (e) {
        print('[ERROR] Failed to create user profile after signup: $e');
      }
      state = const AsyncData(null);
    } catch (e, s) {
      state = AsyncError(e, s);
    }
  }

  Future<void> signInAsGuest() async {
    state = const AsyncLoading();
    try {
      final appUser = await _authService.signInAsGuest();
      if (appUser == null) {
        throw const AuthException('게스트 모드로 시작하는데 실패했습니다.');
      }
      await _createUserProfile(appUser, isGuest: true);
      if (appUser.isGuest) {
        await ref
            .read(localGuestUserNotifierProvider.notifier)
            .setGuestUser(appUser);
      }
      state = const AsyncData(null);
    } catch (e, s) {
      state = AsyncError(e, s);
    }
  }

  Future<void> _createUserProfile(AppUser appUser, {bool isGuest = false}) async {
    final userService = ref.read(userServiceProvider);
    final existingUser = await userService.getUser(appUser.id);

    if (existingUser == null) {
      await userService.createUserProfile(
        appUser.id,
        isGuest ? '<EMAIL>' : appUser.email,
        displayName: isGuest ? '게스트' : appUser.displayName,
        isGuest: isGuest,
      );
    }
  }

  Future<void> signInWithGoogle() async {
    // Google sign-in removed for local-only authentication
    state = AsyncError(const AuthException('Google 로그인은 현재 지원되지 않습니다'), StackTrace.current);
  }

  Future<void> signInWithApple() async {
    // Apple sign-in removed for local-only authentication
    state = AsyncError(const AuthException('Apple 로그인은 현재 지원되지 않습니다'), StackTrace.current);
  }

  Future<void> signOut() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final user = ref.read(currentUserProvider);
      if (user?.isGuest ?? false) {
        await ref.read(localGuestUserNotifierProvider.notifier).clearGuestUser();
      }
      await _authService.signOut();
    });
  }

  Future<void> sendPasswordResetEmail(String email) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await _authService.sendPasswordResetEmail(email);
    });
  }

  Future<void> updateProfile({String? displayName, String? photoURL}) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await _authService.updateProfile(
        displayName: displayName,
        photoURL: photoURL,
      );
    });
  }

  Future<void> deleteAccount() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await _authService.deleteAccount();
    });
  }
}
