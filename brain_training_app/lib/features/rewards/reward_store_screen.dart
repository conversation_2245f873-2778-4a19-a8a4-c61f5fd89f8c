import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/models/reward_point.dart';
import '../../core/services/reward_service.dart';
import '../../shared/providers/user_providers.dart';
import '../../features/auth/auth_provider.dart' show currentUserProvider;

class RewardStoreScreen extends ConsumerWidget {
  const RewardStoreScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    
    if (currentUser == null) {
      return const Scaffold(
        body: Center(
          child: Text('로그인이 필요합니다'),
        ),
      );
    }

    final rewardBalanceAsync = ref.watch(rewardBalanceProvider(currentUser.id));
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('리워드 상점'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Reward balance header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: rewardBalanceAsync.when(
              data: (balance) => _buildBalanceHeader(context, balance),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('오류: $error'),
            ),
          ),
          
          // Store items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                const Text(
                  '플래시카드 저장소 확장',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                ...UnlockableRewards.configs
                    .where((config) => config.benefits.containsKey('storage_increase'))
                    .map((config) => _buildStoreItem(context, ref, config, currentUser.id)),
                
                const SizedBox(height: 32),
                
                const Text(
                  '게임 도구',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                ...UnlockableRewards.configs
                    .where((config) => !config.benefits.containsKey('storage_increase'))
                    .map((config) => _buildStoreItem(context, ref, config, currentUser.id)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceHeader(BuildContext context, RewardBalance? balance) {
    final currentBalance = balance?.currentBalance ?? 0;
    
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.stars,
              color: Colors.amber,
              size: 32,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '보유 포인트',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
                Text(
                  '$currentBalance',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Spacer(),
            TextButton(
              onPressed: () => _showEarnPointsDialog(context),
              child: const Text(
                '포인트 얻기',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        if (balance != null) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '총 획득',
                  '${balance.totalEarned}',
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '총 사용',
                  '${balance.totalSpent}',
                  Icons.shopping_cart,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStoreItem(
    BuildContext context,
    WidgetRef ref,
    UnlockableRewardConfig config,
    String userId,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getIconForReward(config.type),
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    config.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    config.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.stars,
                        size: 16,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${config.cost} 포인트',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Purchase button
            Consumer(
              builder: (context, ref, child) {
                final rewardBalanceAsync = ref.watch(rewardBalanceProvider(userId));
                
                return rewardBalanceAsync.when(
                  data: (balance) {
                    final canAfford = (balance?.currentBalance ?? 0) >= config.cost;
                    
                    return ElevatedButton(
                      onPressed: canAfford
                          ? () => _purchaseReward(context, ref, config, userId)
                          : null,
                      child: const Text('구매'),
                    );
                  },
                  loading: () => const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  error: (error, stack) => const SizedBox(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForReward(UnlockableReward reward) {
    switch (reward) {
      case UnlockableReward.flashCardStorage100:
      case UnlockableReward.flashCardStorage500:
      case UnlockableReward.flashCardStorage1000:
        return Icons.storage;
      case UnlockableReward.hints:
        return Icons.lightbulb;
      case UnlockableReward.skipCooldown:
        return Icons.fast_forward;
      case UnlockableReward.premiumTheme:
        return Icons.palette;
    }
  }

  Future<void> _purchaseReward(
    BuildContext context,
    WidgetRef ref,
    UnlockableRewardConfig config,
    String userId,
  ) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('구매 확인'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${config.title}을(를) 구매하시겠습니까?'),
            const SizedBox(height: 8),
            Text(
              '비용: ${config.cost} 포인트',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('구매'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final rewardService = ref.read(rewardServiceProvider);
      final success = await rewardService.unlockReward(
        userId: userId,
        reward: config.type,
      );

      if (success) {
        // Refresh balance
        ref.invalidate(rewardBalanceProvider(userId));
        
        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${config.title}을(를) 성공적으로 구매했습니다!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('포인트가 부족하거나 구매에 실패했습니다.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('구매 중 오류가 발생했습니다: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEarnPointsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('포인트 획득 방법'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('• 매일 로그인: 10포인트'),
            Text('• 게임 완료: 5-10포인트'),
            Text('• 성취 달성: 변동'),
            Text('• 연속 로그인: 보너스'),
            Text('• 광고 시청: 25포인트 (향후 추가)'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }
}