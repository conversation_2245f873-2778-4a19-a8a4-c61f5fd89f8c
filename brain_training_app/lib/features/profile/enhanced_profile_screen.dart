import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/local_auth_service.dart';
import '../../core/services/local_reward_service.dart';
import '../../core/services/cloud_backup_service.dart';
import '../../features/auth/auth_provider.dart';
import '../../shared/models/reward_point.dart';
import 'dart:io';

class EnhancedProfileScreen extends ConsumerStatefulWidget {
  const EnhancedProfileScreen({super.key});

  @override
  ConsumerState<EnhancedProfileScreen> createState() => _EnhancedProfileScreenState();
}

class _EnhancedProfileScreenState extends ConsumerState<EnhancedProfileScreen> {
  final _displayNameController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  bool _isEditingName = false;
  bool _isChangingPassword = false;
  
  @override
  void dispose() {
    _displayNameController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    
    if (currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('로그인이 필요합니다')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('프로필 관리'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _signOut,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileHeader(currentUser),
            const SizedBox(height: 24),
            _buildAccountSection(currentUser),
            const SizedBox(height: 24),
            _buildRewardsSection(currentUser),
            const SizedBox(height: 24),
            _buildDataSection(),
            const SizedBox(height: 24),
            _buildDangerZone(currentUser),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                user.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                style: const TextStyle(fontSize: 32, color: Colors.white),
              ),
            ),
            const SizedBox(height: 16),
            if (_isEditingName) 
              _buildNameEditField(user)
            else
              _buildNameDisplay(user),
            const SizedBox(height: 8),
            Text(
              user.email,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            if (user.isGuest) ...[
              const SizedBox(height: 8),
              Chip(
                label: const Text('게스트 계정'),
                backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.2),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNameDisplay(user) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          user.displayName ?? '이름 없음',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        if (!user.isGuest) ...[
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.edit, size: 20),
            onPressed: () {
              _displayNameController.text = user.displayName ?? '';
              setState(() => _isEditingName = true);
            },
          ),
        ],
      ],
    );
  }

  Widget _buildNameEditField(user) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _displayNameController,
            decoration: const InputDecoration(
              labelText: '이름',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          icon: const Icon(Icons.check),
          onPressed: _saveName,
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => setState(() => _isEditingName = false),
        ),
      ],
    );
  }

  Widget _buildAccountSection(user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '계정 관리',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (user.isGuest) ...[
              ListTile(
                leading: const Icon(Icons.person_add),
                title: const Text('정식 계정으로 전환'),
                subtitle: const Text('데이터를 안전하게 보호하세요'),
                onTap: _convertGuestToRegular,
              ),
            ] else ...[
              ListTile(
                leading: const Icon(Icons.lock),
                title: const Text('비밀번호 변경'),
                onTap: () => setState(() => _isChangingPassword = !_isChangingPassword),
              ),
              if (_isChangingPassword) _buildPasswordChangeSection(),
            ],
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('계정 정보'),
              subtitle: Text('생성일: ${_formatDate(user.createdAt)}'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordChangeSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const SizedBox(height: 16),
          TextField(
            controller: _currentPasswordController,
            obscureText: true,
            decoration: const InputDecoration(
              labelText: '현재 비밀번호',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _newPasswordController,
            obscureText: true,
            decoration: const InputDecoration(
              labelText: '새 비밀번호',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _changePassword,
                  child: const Text('비밀번호 변경'),
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: () {
                  setState(() => _isChangingPassword = false);
                  _currentPasswordController.clear();
                  _newPasswordController.clear();
                },
                child: const Text('취소'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRewardsSection(user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '리워드 포인트',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            FutureBuilder<RewardBalance>(
              future: ref.read(localRewardServiceProvider).getBalance(user.id),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }
                
                final balance = snapshot.data;
                if (balance == null) {
                  return const Text('포인트 정보를 불러올 수 없습니다');
                }

                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildPointsStat('보유 포인트', balance.availablePoints),
                        _buildPointsStat('총 획득', balance.totalPoints),
                        _buildPointsStat('사용 포인트', balance.spentPoints),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showPointsHistory(user.id),
                      child: const Text('포인트 내역 보기'),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPointsStat(String label, int value) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '데이터 관리',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('데이터 백업'),
              subtitle: const Text('데이터를 백업 파일로 저장'),
              onTap: _createBackup,
            ),
            ListTile(
              leading: const Icon(Icons.restore),
              title: const Text('데이터 복원'),
              subtitle: const Text('백업 파일에서 데이터 복원'),
              onTap: _restoreFromBackup,
            ),
            ListTile(
              leading: const Icon(Icons.cloud),
              title: Text(Platform.isIOS ? 'iCloud 동기화' : 'Google Drive 동기화'),
              subtitle: Text(Platform.isIOS 
                  ? '설정 > Apple ID > iCloud에서 활성화' 
                  : 'Google Drive 앱별 폴더에 자동 저장'),
              onTap: _showCloudSyncInfo,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDangerZone(user) {
    return Card(
      color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.3),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '위험한 작업',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.delete_forever, color: Theme.of(context).colorScheme.error),
              title: Text(
                '계정 삭제',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              subtitle: const Text('모든 데이터가 영구적으로 삭제됩니다'),
              onTap: _deleteAccount,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveName() async {
    try {
      final authService = ref.read(localAuthServiceProvider);
      await authService.updateProfile(displayName: _displayNameController.text.trim());
      
      setState(() => _isEditingName = false);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('이름이 변경되었습니다')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('이름 변경 실패: $e')),
        );
      }
    }
  }

  Future<void> _changePassword() async {
    try {
      final authService = ref.read(localAuthServiceProvider);
      await authService.changePassword(
        _currentPasswordController.text,
        _newPasswordController.text,
      );
      
      setState(() => _isChangingPassword = false);
      _currentPasswordController.clear();
      _newPasswordController.clear();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('비밀번호가 변경되었습니다')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('비밀번호 변경 실패: $e')),
        );
      }
    }
  }

  Future<void> _convertGuestToRegular() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => const GuestConversionDialog(),
    );
    
    if (result != null) {
      try {
        final authService = ref.read(localAuthServiceProvider);
        await authService.convertGuestToRegularAccount(
          result['email']!,
          result['password']!,
          result['displayName']!,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('정식 계정으로 전환되었습니다')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('계정 전환 실패: $e')),
          );
        }
      }
    }
  }

  Future<void> _createBackup() async {
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) return;

      final backupService = CloudBackupService();
      final backupFile = await backupService.createTimestampedBackup(currentUser.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('백업 생성 완료: ${backupFile.path}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('백업 생성 실패: $e')),
        );
      }
    }
  }

  Future<void> _restoreFromBackup() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('파일 선택 기능은 추후 구현 예정입니다')),
    );
  }

  void _showCloudSyncInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(Platform.isIOS ? 'iCloud 동기화' : 'Google Drive 동기화'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (Platform.isIOS) ...[
              const Text('앱 데이터는 다음 위치에 자동으로 백업됩니다:'),
              const SizedBox(height: 8),
              const Text('• Documents 폴더 → iCloud Drive'),
              const Text('• 설정 > Apple ID > iCloud에서 활성화'),
            ] else ...[
              const Text('앱 데이터는 다음 위치에 자동으로 백업됩니다:'),
              const SizedBox(height: 8),
              const Text('• Google Drive 앱별 폴더'),
              const Text('• 자동 백업 및 동기화 지원'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _showPointsHistory(String userId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => PointsHistorySheet(userId: userId),
    );
  }

  Future<void> _signOut() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('로그아웃'),
        content: const Text('정말 로그아웃하시겠습니까?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('로그아웃'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(authProvider.notifier).signOut();
    }
  }

  Future<void> _deleteAccount() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('계정 삭제'),
        content: const Text('정말 계정을 삭제하시겠습니까?\n모든 데이터가 영구적으로 삭제되며 복구할 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Theme.of(context).colorScheme.error),
            child: const Text('삭제'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(authProvider.notifier).deleteAccount();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('계정 삭제 실패: $e')),
          );
        }
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}년 ${date.month}월 ${date.day}일';
  }
}

class GuestConversionDialog extends StatefulWidget {
  const GuestConversionDialog({super.key});

  @override
  State<GuestConversionDialog> createState() => _GuestConversionDialogState();
}

class _GuestConversionDialogState extends State<GuestConversionDialog> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('정식 계정으로 전환'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(labelText: '이름'),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _emailController,
            decoration: const InputDecoration(labelText: '이메일'),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _passwordController,
            decoration: const InputDecoration(labelText: '비밀번호'),
            obscureText: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_emailController.text.isNotEmpty && 
                _passwordController.text.isNotEmpty &&
                _nameController.text.isNotEmpty) {
              Navigator.of(context).pop({
                'email': _emailController.text,
                'password': _passwordController.text,
                'displayName': _nameController.text,
              });
            }
          },
          child: const Text('전환'),
        ),
      ],
    );
  }
}

class PointsHistorySheet extends ConsumerWidget {
  final String userId;

  const PointsHistorySheet({super.key, required this.userId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            '포인트 내역',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: FutureBuilder<List<RewardTransaction>>(
              future: ref.read(localRewardServiceProvider).getTransactionHistory(userId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final transactions = snapshot.data ?? [];
                if (transactions.isEmpty) {
                  return const Center(child: Text('거래 내역이 없습니다'));
                }

                return ListView.builder(
                  itemCount: transactions.length,
                  itemBuilder: (context, index) {
                    final transaction = transactions[index];
                    final isEarned = transaction.type == RewardTransactionType.earned;
                    
                    return ListTile(
                      leading: Icon(
                        isEarned ? Icons.add_circle : Icons.remove_circle,
                        color: isEarned ? Colors.green : Colors.red,
                      ),
                      title: Text(transaction.description),
                      subtitle: Text(_formatDate(transaction.timestamp)),
                      trailing: Text(
                        '${isEarned ? '+' : '-'}${transaction.amount}P',
                        style: TextStyle(
                          color: isEarned ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}