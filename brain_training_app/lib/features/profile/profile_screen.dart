import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/themes/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../features/auth/auth_provider.dart';
import '../../features/auth/account_upgrade_screen.dart';
import '../../shared/providers/user_providers.dart';
import '../../shared/models/user.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('[DEBUG] ProfileScreen.build() - Starting...');
    
    final currentUser = ref.watch(currentUserProvider);
    print('[DEBUG] ProfileScreen.build() - currentUser: $currentUser');
    
    final userStats = ref.watch(userStatsProvider);
    print('[DEBUG] ProfileScreen.build() - userStats: $userStats');
    
    final userPreferences = ref.watch(userPreferencesProvider);
    print('[DEBUG] ProfileScreen.build() - userPreferences: $userPreferences');

    if (currentUser == null) {
      print('[DEBUG] ProfileScreen.build() - currentUser is null, showing loading indicator');
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    
    print('[DEBUG] ProfileScreen.build() - Building profile content for user: ${currentUser.id}');

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildProfileHeader(context, currentUser),
            const SizedBox(height: 24),
            _buildStatsSection(userStats),
            const SizedBox(height: 24),
            _buildPreferencesSection(context, ref, userPreferences),
            const SizedBox(height: 24),
            _buildActionsSection(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, dynamic currentUser) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryColor,
              backgroundImage: currentUser.photoURL != null
                  ? NetworkImage(currentUser.photoURL!)
                  : null,
              child: currentUser.photoURL == null
                  ? const Icon(
                      Icons.person,
                      size: 50,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              currentUser.displayName ?? '게스트',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              currentUser.email,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            if (currentUser.isGuest == true) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  '게스트 모드',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => const AccountUpgradeScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.upgrade),
                  label: const Text('계정 업그레이드'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(AsyncValue<UserStats?> userStats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '통계',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            userStats.when(
              data: (stats) {
                if (stats == null) {
                  return const Text('통계를 불러올 수 없습니다.');
                }
                return Column(
                  children: [
                    _buildStatRow('레벨', stats.level.toString()),
                    _buildStatRow('총 XP', stats.totalXP.toString()),
                    _buildStatRow('현재 연속 일수', '${stats.currentStreak}일'),
                    _buildStatRow('최대 연속 일수', '${stats.maxStreak}일'),
                    _buildStatRow('총 게임 수', stats.totalGamesPlayed.toString()),
                    _buildStatRow('총 플레이 시간', stats.totalPlayTime.toString()),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (_, __) => const Text('통계를 불러올 수 없습니다.'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferencesSection(BuildContext context, WidgetRef ref, dynamic userPreferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '설정',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('다크 모드'),
              value: userPreferences.isDarkMode,
              onChanged: (value) {
                ref.read(userPreferencesProvider.notifier).updateDarkMode(value);
              },
            ),
            SwitchListTile(
              title: const Text('사운드'),
              value: userPreferences.soundEnabled,
              onChanged: (value) {
                ref.read(userPreferencesProvider.notifier).updateSoundEnabled(value);
              },
            ),
            SwitchListTile(
              title: const Text('음악'),
              value: userPreferences.musicEnabled,
              onChanged: (value) {
                ref.read(userPreferencesProvider.notifier).updateMusicEnabled(value);
              },
            ),
            SwitchListTile(
              title: const Text('알림'),
              value: userPreferences.notificationsEnabled,
              onChanged: (value) {
                ref.read(userPreferencesProvider.notifier).updateNotificationsEnabled(value);
              },
            ),
            SwitchListTile(
              title: const Text('햅틱 피드백'),
              value: userPreferences.hapticFeedbackEnabled,
              onChanged: (value) {
                ref.read(userPreferencesProvider.notifier).updateHapticFeedback(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final isGuest = currentUser?.isGuest ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '계정',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (isGuest) ...[
              ListTile(
                leading: const Icon(Icons.person_add),
                title: const Text('계정 만들기'),
                subtitle: const Text('더 많은 기능을 이용하세요'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const AccountUpgradeScreen(),
                    ),
                  );
                },
              ),
              const Divider(),
            ],
            if (!isGuest) ...[
              ListTile(
                leading: const Icon(Icons.logout),
                title: const Text('로그아웃'),
                onTap: () async {
                  final shouldLogout = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('로그아웃'),
                      content: const Text('정말 로그아웃하시겠습니까?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context, false),
                          child: const Text('취소'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.pop(context, true),
                          child: const Text('로그아웃'),
                        ),
                      ],
                    ),
                  );

                  if (shouldLogout == true) {
                    await ref.read(authProvider.notifier).signOut();
                    if (context.mounted) {
                      context.showSnackBar('로그아웃되었습니다');
                    }
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}