import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'core/services/local_database_service.dart';
import 'core/services/local_auth_service.dart';
import 'features/auth/auth_provider.dart';
import 'features/auth/auth_screen.dart';
import 'shared/widgets/home_screen.dart';
import 'core/themes/app_theme.dart';

void main() async {
  print('[DEBUG] Starting app initialization...');
  
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialize DotEnv first
    print('[DEBUG] Loading .env file...');
    await dotenv.load(fileName: ".env");
    print('[DEBUG] .env file loaded successfully');
    
    // Firebase removed - using local storage only
    print('[DEBUG] Firebase initialization skipped - using local storage');
    
    // Initialize Hive
    print('[DEBUG] Initializing Hive...');
    await Hive.initFlutter();
    print('[DEBUG] Hive initialized successfully');
    
    // Initialize local database service
    print('[DEBUG] Initializing local database service...');
    final container = ProviderContainer();
    final localDbService = LocalDatabaseService();
    await localDbService.database; // This will initialize the local database
    
    // Initialize local auth service
    final localAuthService = container.read(localAuthServiceProvider);
    await localAuthService.initialize();
    print('[DEBUG] Local services initialized successfully');
    
    print('[DEBUG] All initialization complete, starting app...');
    
    runApp(
      ProviderScope(
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    print('[ERROR] Failed to initialize app: $e');
    print('[ERROR] Stack trace: $stackTrace');
    
    // Show error screen
    runApp(
      MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.red,
          body: Center(
            child: Text(
              'Initialization Error: $e',
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'Brain Training App',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const AuthWrapper(),
    );
  }
}

class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    
    return authState.when(
      data: (user) {
        if (user == null) {
          return const WorkingAuthScreen();
        } else {
          return const HomeScreen();
        }
      },
      loading: () {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
      error: (error, stack) {
        return Scaffold(
          body: Center(
            child: Text(
              'Authentication Error: $error',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        );
      },
    );
  }
}

class WorkingAuthScreen extends ConsumerStatefulWidget {
  const WorkingAuthScreen({super.key});

  @override
  ConsumerState<WorkingAuthScreen> createState() => _WorkingAuthScreenState();
}

class _WorkingAuthScreenState extends ConsumerState<WorkingAuthScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen for auth state changes to show messages
    ref.listen<AsyncValue<void>>(authProvider, (previous, next) {
      if (next is AsyncError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(next.error.toString()),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    });

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 48),
              _buildHeader(),
              const SizedBox(height: 48),
              _buildSignInForm(authState),
              const SizedBox(height: 24),
              _buildSocialButtons(authState),
              const SizedBox(height: 16),
              _buildGuestButton(authState),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.psychology,
            color: Colors.white,
            size: 40,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Brain Training',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '두뇌 훈련으로 더 나은 당신이 되어보세요',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignInForm(AsyncValue<void> authState) {
    return Column(
      children: [
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          decoration: const InputDecoration(
            labelText: '이메일',
            prefixIcon: Icon(Icons.email_outlined),
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: '비밀번호',
            prefixIcon: const Icon(Icons.lock_outlined),
            suffixIcon: IconButton(
              icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
            ),
          ),
          onFieldSubmitted: (_) => _signIn(),
        ),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: authState.isLoading ? null : _signIn,
            child: authState.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('로그인'),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButtons(AsyncValue<void> authState) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('계정이 없으신가요? '),
            TextButton(
              onPressed: _showRegistrationDialog,
              child: const Text('회원가입'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGuestButton(AsyncValue<void> authState) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: authState.isLoading ? null : _signInAsGuest,
            child: const Text('게스트로 시작하기'),
          ),
        ),
      ],
    );
  }

  void _signIn() async {
    final notifier = ref.read(authProvider.notifier);
    await notifier.signInWithEmailAndPassword(
      _emailController.text.trim(),
      _passwordController.text,
    );
  }

  void _signInAsGuest() async {
    final notifier = ref.read(authProvider.notifier);
    await notifier.signInAsGuest();
  }

  void _showRegistrationDialog() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => const RegistrationDialog(),
    );
    
    if (result != null) {
      final notifier = ref.read(authProvider.notifier);
      await notifier.createUserWithEmailAndPassword(
        result['email']!,
        result['password']!,
        result['displayName']!,
      );
    }
  }

  void _signInWithGoogle() async {
    // Google sign-in removed - local authentication only
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('소셜 로그인은 현재 지원되지 않습니다. 이메일/비밀번호를 사용해주세요.'),
      ),
    );
  }
}

class RegistrationDialog extends StatefulWidget {
  const RegistrationDialog({super.key});

  @override
  State<RegistrationDialog> createState() => _RegistrationDialogState();
}

class _RegistrationDialogState extends State<RegistrationDialog> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('회원가입'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '이름',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: '이메일',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: '비밀번호',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              obscureText: _obscureConfirmPassword,
              decoration: InputDecoration(
                labelText: '비밀번호 확인',
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                  onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '• 비밀번호는 최소 6자 이상이어야 합니다',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _isFormValid() ? _createAccount : null,
          child: const Text('계정 생성'),
        ),
      ],
    );
  }

  bool _isFormValid() {
    return _nameController.text.isNotEmpty &&
           _emailController.text.isNotEmpty &&
           _passwordController.text.length >= 6 &&
           _passwordController.text == _confirmPasswordController.text;
  }

  void _createAccount() {
    if (_isFormValid()) {
      Navigator.of(context).pop({
        'email': _emailController.text.trim(),
        'password': _passwordController.text,
        'displayName': _nameController.text.trim(),
      });
    }
  }
}



