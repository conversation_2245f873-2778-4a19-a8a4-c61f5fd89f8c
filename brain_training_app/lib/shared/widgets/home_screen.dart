import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/themes/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../core/constants/game_constants.dart';
import '../../core/constants/app_constants.dart';
import '../../features/auth/auth_provider.dart';
import '../../shared/providers/user_providers.dart';
import '../../shared/models/user.dart';
import '../../features/games/dual_n_back/dual_n_back_screen.dart';
import '../../features/games/flash_cards/flash_cards_screen.dart';
import '../../features/profile/enhanced_profile_screen.dart';
import '../../features/achievements/achievement_screen.dart';
import '../../shared/providers/achievement_providers.dart';
import '../../core/services/notification_service.dart';
import '../../core/services/daily_login_service.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _currentIndex = 0;
  static const platform = MethodChannel('com.windroamer.brain_training_app/buzzvil');

  @override
  void initState() {
    super.initState();
    // Set notification service context for achievement notifications
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationServiceProvider).setContext(context);
      _checkDailyLoginBonus();
      _loginToBuzzvil();
    });
  }

  Future<void> _checkDailyLoginBonus() async {
    try {
      final user = ref.read(currentUserProvider);
      if (user != null) {
        final dailyLoginService = ref.read(dailyLoginServiceProvider);
        final received = await dailyLoginService.checkAndAwardDailyLoginBonus(user.id);
        
        if (received && mounted) {
          // Show login bonus notification
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: const [
                  Icon(Icons.stars, color: Colors.amber),
                  SizedBox(width: 8),
                  Text('일일 로그인 보상 10포인트를 받았습니다!'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          
          // Check for streak bonus
          final streakReceived = await dailyLoginService.awardStreakBonus(user.id);
          if (streakReceived && mounted) {
            final streak = await dailyLoginService.getLoginStreak(user.id);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.local_fire_department, color: Colors.orange),
                    const SizedBox(width: 8),
                    Text('${streak}일 연속 로그인 달성! 보너스 포인트를 받았습니다!'),
                  ],
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 4),
              ),
            );
          }
        }
      }
    } catch (e) {
      print('Error checking daily login bonus: $e');
    }
  }

  Future<void> _loginToBuzzvil() async {
    if (!AppConstants.enableBuzzvil) {
      print('Buzzvil is disabled');
      return;
    }
    
    final user = ref.read(currentUserProvider);
    if (user != null && !user.isGuest) {
      try {
        final bool result = await platform.invokeMethod('login', {
          'userId': user.id,
          'birthYear': user.birthYear,
          'gender': user.gender,
        });
        if (result) {
          print('Buzzvil login successful');
        } else {
          print('Buzzvil login failed');
        }
      } on PlatformException catch (e) {
        print("Failed to login to Buzzvil: '${e.message}'.");
      }
    }
  }

  Future<void> _showBenefitHub() async {
    if (!AppConstants.enableBuzzvil) {
      print('Buzzvil is disabled');
      return;
    }
    
    try {
      await platform.invokeMethod('showBenefitHub');
    } on PlatformException catch (e) {
      print("Failed to show BenefitHub: '${e.message}'.");
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final userStats = ref.watch(userStatsProvider);

    // If currentUser is null, we're probably in the process of setting up guest mode
    // Show the home screen anyway - it should handle guest mode gracefully
    if (currentUser == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [
          _buildHomeTab(userStats),
          _buildGamesTab(),
          const AchievementScreen(),
          const EnhancedProfileScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        backgroundColor: Colors.white,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.games),
            label: '게임',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.emoji_events),
            label: '성취',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '프로필',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeTab(AsyncValue<UserStats?> userStats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          _buildStatsCard(userStats),
          const SizedBox(height: 24),
          if (AppConstants.enableBuzzvil) ...[
            _buildRewardCard(),
            const SizedBox(height: 24),
          ],
          _buildQuickActionsCard(),
          const SizedBox(height: 24),
          _buildAchievementsPreview(),
        ],
      ),
    );
  }

  Widget _buildRewardCard() {
    return Card(
      child: InkWell(
        onTap: _showBenefitHub,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Icon(
                Icons.card_giftcard,
                color: context.colors.primary,
                size: 28,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '포인트 받기',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Icon(Icons.arrow_forward_ios),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final currentUser = ref.watch(currentUserProvider);
    final isGuest = currentUser?.isGuest ?? true; // Default to guest if no user
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '안녕하세요, ${currentUser?.displayName ?? '게스트'}님!',
              style: context.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isGuest
                  ? '게스트 모드로 두뇌 훈련을 시작해보세요'
                  : '오늘도 두뇌 훈련을 시작해보세요',
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colors.onSurface.withAlpha(178),
              ),
            ),
            if (isGuest) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.colors.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.account_circle_outlined,
                      color: context.colors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '계정 생성하면 모든 기능을 사용할 수 있습니다',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(AsyncValue<UserStats?> userStats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '나의 통계',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            userStats.when(
              data: (stats) {
                if (stats == null) {
                  return const Text('통계를 불러올 수 없습니다.');
                }
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem('레벨', stats.level.toString()),
                    _buildStatItem('연속 일수', '${stats.currentStreak}일'),
                    _buildStatItem('총 게임', stats.totalGamesPlayed.toString()),
                    _buildStatItem('XP', stats.totalXP.toString()),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (_, __) => const Text('통계를 불러올 수 없습니다.'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: context.textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildQuickActionsCard() {
    final currentUser = ref.watch(currentUserProvider);
    final isGuest = currentUser?.isGuest ?? true; // Default to guest if no user
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '빠른 시작',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  icon: Icons.psychology,
                  label: 'Dual N-Back',
                  onTap: () => _navigateToGame(GameType.dualNBack),
                ),
                _buildQuickActionButton(
                  icon: Icons.style,
                  label: 'Flash Cards',
                  onTap: () => _navigateToGame(GameType.flashCards),
                ),
                if (!isGuest) ...[
                  _buildQuickActionButton(
                    icon: Icons.memory,
                    label: '작업기억',
                    onTap: () => _navigateToGame(GameType.workingMemory),
                  ),
                  _buildQuickActionButton(
                    icon: Icons.visibility,
                    label: '주의력',
                    onTap: () => _navigateToGame(GameType.attentionTraining),
                  ),
                ],
              ],
            ),
            if (isGuest) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.colors.primary.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: context.colors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '게스트 모드에서는 Dual N-Back과 Flash Cards만 사용 가능합니다.',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(25),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: context.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsPreview() {
    final currentUser = ref.watch(currentUserProvider);
    
    // If no user yet, show a placeholder that encourages playing
    if (currentUser == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '성취',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    color: context.colors.onSurface.withValues(alpha: 0.5),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '게임을 플레이해서 첫 번째 성취를 달성해보세요!',
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.colors.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '최근 성취',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() => _currentIndex = 2); // Navigate to achievements tab
                  },
                  child: const Text('전체보기'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final recentAchievementsAsync = ref.watch(recentAchievementsProvider(currentUser.id));
                
                return recentAchievementsAsync.when(
                  data: (achievements) {
                    if (achievements.isEmpty) {
                      return Row(
                        children: [
                          Icon(
                            Icons.emoji_events_outlined,
                            color: context.colors.onSurface.withValues(alpha: 0.5),
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              '아직 달성한 성취가 없습니다.\n게임을 플레이해서 첫 번째 성취를 달성해보세요!',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.colors.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                    
                    return Column(
                      children: achievements.take(3).map((achievement) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Row(
                            children: [
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: context.colors.primary.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.emoji_events,
                                  color: context.colors.primary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      achievement.title,
                                      style: context.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    if (achievement.unlockedAt != null)
                                      Text(
                                        achievement.unlockedAt!.timeAgo,
                                        style: context.textTheme.bodySmall?.copyWith(
                                          color: context.colors.onSurface.withValues(alpha: 0.6),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: context.colors.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '+${achievement.xpReward}',
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: context.colors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    );
                  },
                  loading: () => const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  error: (error, stack) => Text(
                    '성취를 불러올 수 없습니다.',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.error,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGamesTab() {
    final currentUser = ref.watch(currentUserProvider);
    final isGuest = currentUser?.isGuest ?? true; // Default to guest if no user
    
    // For guest users, only show dual N-back and flash cards
    final availableGames = isGuest
        ? [GameType.dualNBack, GameType.flashCards]
        : GameType.values;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '게임 선택',
            style: context.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          ...availableGames.map((gameType) => _buildGameCard(gameType)),
          if (isGuest) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.colors.primary.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: context.colors.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '게스트 모드 안내',
                        style: context.textTheme.titleSmall?.copyWith(
                          color: context.colors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 게스트 모드에서는 Dual N-Back과 Flash Cards만 사용 가능합니다\n• Flash Cards는 로컬에만 저장됩니다\n• 계정을 만들면 모든 게임과 클라우드 동기화를 사용할 수 있습니다',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: context.colors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGameCard(GameType gameType) {
    final name = GameConstants.gameNames[gameType] ?? '';
    final description = GameConstants.gameDescriptions[gameType] ?? '';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getGameIcon(gameType),
            color: AppTheme.primaryColor,
            size: 28,
          ),
        ),
        title: Text(
          name,
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _navigateToGame(gameType),
      ),
    );
  }

  IconData _getGameIcon(GameType gameType) {
    switch (gameType) {
      case GameType.dualNBack:
        return Icons.psychology;
      case GameType.flashCards:
        return Icons.style;
      case GameType.workingMemory:
        return Icons.memory;
      case GameType.attentionTraining:
        return Icons.visibility;
    }
  }

  void _navigateToGame(GameType gameType) {
    Widget screen;
    
    switch (gameType) {
      case GameType.dualNBack:
        screen = const DualNBackScreen();
        break;
      case GameType.flashCards:
        screen = const FlashCardsScreen();
        break;
      case GameType.workingMemory:
        screen = const Scaffold(
          body: Center(child: Text('작업기억 게임 (구현 예정)')),
        );
        break;
      case GameType.attentionTraining:
        screen = const Scaffold(
          body: Center(child: Text('주의력 훈련 게임 (구현 예정)')),
        );
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }
}
