// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GameSession _$GameSessionFromJson(Map<String, dynamic> json) {
  return _GameSession.fromJson(json);
}

/// @nodoc
mixin _$GameSession {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  GameType get gameType => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  double get accuracy => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  int get score => throw _privateConstructorUsedError;
  DateTime get startTime => throw _privateConstructorUsedError;
  DateTime get endTime => throw _privateConstructorUsedError;
  Map<String, dynamic> get gameSpecificData =>
      throw _privateConstructorUsedError;
  int get xpEarned => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  List<GameEvent> get events => throw _privateConstructorUsedError;

  /// Serializes this GameSession to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GameSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GameSessionCopyWith<GameSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GameSessionCopyWith<$Res> {
  factory $GameSessionCopyWith(
          GameSession value, $Res Function(GameSession) then) =
      _$GameSessionCopyWithImpl<$Res, GameSession>;
  @useResult
  $Res call(
      {String id,
      String userId,
      GameType gameType,
      int level,
      double accuracy,
      Duration duration,
      int score,
      DateTime startTime,
      DateTime endTime,
      Map<String, dynamic> gameSpecificData,
      int xpEarned,
      bool isCompleted,
      List<GameEvent> events});
}

/// @nodoc
class _$GameSessionCopyWithImpl<$Res, $Val extends GameSession>
    implements $GameSessionCopyWith<$Res> {
  _$GameSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GameSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? gameType = null,
    Object? level = null,
    Object? accuracy = null,
    Object? duration = null,
    Object? score = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? gameSpecificData = null,
    Object? xpEarned = null,
    Object? isCompleted = null,
    Object? events = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gameType: null == gameType
          ? _value.gameType
          : gameType // ignore: cast_nullable_to_non_nullable
              as GameType,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gameSpecificData: null == gameSpecificData
          ? _value.gameSpecificData
          : gameSpecificData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      xpEarned: null == xpEarned
          ? _value.xpEarned
          : xpEarned // ignore: cast_nullable_to_non_nullable
              as int,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      events: null == events
          ? _value.events
          : events // ignore: cast_nullable_to_non_nullable
              as List<GameEvent>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GameSessionImplCopyWith<$Res>
    implements $GameSessionCopyWith<$Res> {
  factory _$$GameSessionImplCopyWith(
          _$GameSessionImpl value, $Res Function(_$GameSessionImpl) then) =
      __$$GameSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      GameType gameType,
      int level,
      double accuracy,
      Duration duration,
      int score,
      DateTime startTime,
      DateTime endTime,
      Map<String, dynamic> gameSpecificData,
      int xpEarned,
      bool isCompleted,
      List<GameEvent> events});
}

/// @nodoc
class __$$GameSessionImplCopyWithImpl<$Res>
    extends _$GameSessionCopyWithImpl<$Res, _$GameSessionImpl>
    implements _$$GameSessionImplCopyWith<$Res> {
  __$$GameSessionImplCopyWithImpl(
      _$GameSessionImpl _value, $Res Function(_$GameSessionImpl) _then)
      : super(_value, _then);

  /// Create a copy of GameSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? gameType = null,
    Object? level = null,
    Object? accuracy = null,
    Object? duration = null,
    Object? score = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? gameSpecificData = null,
    Object? xpEarned = null,
    Object? isCompleted = null,
    Object? events = null,
  }) {
    return _then(_$GameSessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gameType: null == gameType
          ? _value.gameType
          : gameType // ignore: cast_nullable_to_non_nullable
              as GameType,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gameSpecificData: null == gameSpecificData
          ? _value._gameSpecificData
          : gameSpecificData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      xpEarned: null == xpEarned
          ? _value.xpEarned
          : xpEarned // ignore: cast_nullable_to_non_nullable
              as int,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      events: null == events
          ? _value._events
          : events // ignore: cast_nullable_to_non_nullable
              as List<GameEvent>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GameSessionImpl implements _GameSession {
  const _$GameSessionImpl(
      {required this.id,
      required this.userId,
      required this.gameType,
      required this.level,
      required this.accuracy,
      required this.duration,
      required this.score,
      required this.startTime,
      required this.endTime,
      required final Map<String, dynamic> gameSpecificData,
      this.xpEarned = 0,
      this.isCompleted = false,
      final List<GameEvent> events = const []})
      : _gameSpecificData = gameSpecificData,
        _events = events;

  factory _$GameSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$GameSessionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final GameType gameType;
  @override
  final int level;
  @override
  final double accuracy;
  @override
  final Duration duration;
  @override
  final int score;
  @override
  final DateTime startTime;
  @override
  final DateTime endTime;
  final Map<String, dynamic> _gameSpecificData;
  @override
  Map<String, dynamic> get gameSpecificData {
    if (_gameSpecificData is EqualUnmodifiableMapView) return _gameSpecificData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_gameSpecificData);
  }

  @override
  @JsonKey()
  final int xpEarned;
  @override
  @JsonKey()
  final bool isCompleted;
  final List<GameEvent> _events;
  @override
  @JsonKey()
  List<GameEvent> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  String toString() {
    return 'GameSession(id: $id, userId: $userId, gameType: $gameType, level: $level, accuracy: $accuracy, duration: $duration, score: $score, startTime: $startTime, endTime: $endTime, gameSpecificData: $gameSpecificData, xpEarned: $xpEarned, isCompleted: $isCompleted, events: $events)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GameSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.gameType, gameType) ||
                other.gameType == gameType) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            const DeepCollectionEquality()
                .equals(other._gameSpecificData, _gameSpecificData) &&
            (identical(other.xpEarned, xpEarned) ||
                other.xpEarned == xpEarned) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      gameType,
      level,
      accuracy,
      duration,
      score,
      startTime,
      endTime,
      const DeepCollectionEquality().hash(_gameSpecificData),
      xpEarned,
      isCompleted,
      const DeepCollectionEquality().hash(_events));

  /// Create a copy of GameSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GameSessionImplCopyWith<_$GameSessionImpl> get copyWith =>
      __$$GameSessionImplCopyWithImpl<_$GameSessionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GameSessionImplToJson(
      this,
    );
  }
}

abstract class _GameSession implements GameSession {
  const factory _GameSession(
      {required final String id,
      required final String userId,
      required final GameType gameType,
      required final int level,
      required final double accuracy,
      required final Duration duration,
      required final int score,
      required final DateTime startTime,
      required final DateTime endTime,
      required final Map<String, dynamic> gameSpecificData,
      final int xpEarned,
      final bool isCompleted,
      final List<GameEvent> events}) = _$GameSessionImpl;

  factory _GameSession.fromJson(Map<String, dynamic> json) =
      _$GameSessionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  GameType get gameType;
  @override
  int get level;
  @override
  double get accuracy;
  @override
  Duration get duration;
  @override
  int get score;
  @override
  DateTime get startTime;
  @override
  DateTime get endTime;
  @override
  Map<String, dynamic> get gameSpecificData;
  @override
  int get xpEarned;
  @override
  bool get isCompleted;
  @override
  List<GameEvent> get events;

  /// Create a copy of GameSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GameSessionImplCopyWith<_$GameSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GameEvent _$GameEventFromJson(Map<String, dynamic> json) {
  return _GameEvent.fromJson(json);
}

/// @nodoc
mixin _$GameEvent {
  DateTime get timestamp => throw _privateConstructorUsedError;
  String get eventType => throw _privateConstructorUsedError;
  Map<String, dynamic> get data => throw _privateConstructorUsedError;

  /// Serializes this GameEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GameEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GameEventCopyWith<GameEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GameEventCopyWith<$Res> {
  factory $GameEventCopyWith(GameEvent value, $Res Function(GameEvent) then) =
      _$GameEventCopyWithImpl<$Res, GameEvent>;
  @useResult
  $Res call({DateTime timestamp, String eventType, Map<String, dynamic> data});
}

/// @nodoc
class _$GameEventCopyWithImpl<$Res, $Val extends GameEvent>
    implements $GameEventCopyWith<$Res> {
  _$GameEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GameEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? eventType = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      eventType: null == eventType
          ? _value.eventType
          : eventType // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GameEventImplCopyWith<$Res>
    implements $GameEventCopyWith<$Res> {
  factory _$$GameEventImplCopyWith(
          _$GameEventImpl value, $Res Function(_$GameEventImpl) then) =
      __$$GameEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime timestamp, String eventType, Map<String, dynamic> data});
}

/// @nodoc
class __$$GameEventImplCopyWithImpl<$Res>
    extends _$GameEventCopyWithImpl<$Res, _$GameEventImpl>
    implements _$$GameEventImplCopyWith<$Res> {
  __$$GameEventImplCopyWithImpl(
      _$GameEventImpl _value, $Res Function(_$GameEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of GameEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? eventType = null,
    Object? data = null,
  }) {
    return _then(_$GameEventImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      eventType: null == eventType
          ? _value.eventType
          : eventType // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GameEventImpl implements _GameEvent {
  const _$GameEventImpl(
      {required this.timestamp,
      required this.eventType,
      required final Map<String, dynamic> data})
      : _data = data;

  factory _$GameEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$GameEventImplFromJson(json);

  @override
  final DateTime timestamp;
  @override
  final String eventType;
  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  String toString() {
    return 'GameEvent(timestamp: $timestamp, eventType: $eventType, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GameEventImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.eventType, eventType) ||
                other.eventType == eventType) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, timestamp, eventType,
      const DeepCollectionEquality().hash(_data));

  /// Create a copy of GameEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GameEventImplCopyWith<_$GameEventImpl> get copyWith =>
      __$$GameEventImplCopyWithImpl<_$GameEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GameEventImplToJson(
      this,
    );
  }
}

abstract class _GameEvent implements GameEvent {
  const factory _GameEvent(
      {required final DateTime timestamp,
      required final String eventType,
      required final Map<String, dynamic> data}) = _$GameEventImpl;

  factory _GameEvent.fromJson(Map<String, dynamic> json) =
      _$GameEventImpl.fromJson;

  @override
  DateTime get timestamp;
  @override
  String get eventType;
  @override
  Map<String, dynamic> get data;

  /// Create a copy of GameEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GameEventImplCopyWith<_$GameEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DualNBackSessionData _$DualNBackSessionDataFromJson(Map<String, dynamic> json) {
  return _DualNBackSessionData.fromJson(json);
}

/// @nodoc
mixin _$DualNBackSessionData {
  int get nLevel => throw _privateConstructorUsedError;
  int get totalTrials => throw _privateConstructorUsedError;
  int get correctVisual => throw _privateConstructorUsedError;
  int get correctAudio => throw _privateConstructorUsedError;
  int get incorrectVisual => throw _privateConstructorUsedError;
  int get incorrectAudio => throw _privateConstructorUsedError;
  List<DualNBackTrial> get trials => throw _privateConstructorUsedError;

  /// Serializes this DualNBackSessionData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DualNBackSessionData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DualNBackSessionDataCopyWith<DualNBackSessionData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DualNBackSessionDataCopyWith<$Res> {
  factory $DualNBackSessionDataCopyWith(DualNBackSessionData value,
          $Res Function(DualNBackSessionData) then) =
      _$DualNBackSessionDataCopyWithImpl<$Res, DualNBackSessionData>;
  @useResult
  $Res call(
      {int nLevel,
      int totalTrials,
      int correctVisual,
      int correctAudio,
      int incorrectVisual,
      int incorrectAudio,
      List<DualNBackTrial> trials});
}

/// @nodoc
class _$DualNBackSessionDataCopyWithImpl<$Res,
        $Val extends DualNBackSessionData>
    implements $DualNBackSessionDataCopyWith<$Res> {
  _$DualNBackSessionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DualNBackSessionData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nLevel = null,
    Object? totalTrials = null,
    Object? correctVisual = null,
    Object? correctAudio = null,
    Object? incorrectVisual = null,
    Object? incorrectAudio = null,
    Object? trials = null,
  }) {
    return _then(_value.copyWith(
      nLevel: null == nLevel
          ? _value.nLevel
          : nLevel // ignore: cast_nullable_to_non_nullable
              as int,
      totalTrials: null == totalTrials
          ? _value.totalTrials
          : totalTrials // ignore: cast_nullable_to_non_nullable
              as int,
      correctVisual: null == correctVisual
          ? _value.correctVisual
          : correctVisual // ignore: cast_nullable_to_non_nullable
              as int,
      correctAudio: null == correctAudio
          ? _value.correctAudio
          : correctAudio // ignore: cast_nullable_to_non_nullable
              as int,
      incorrectVisual: null == incorrectVisual
          ? _value.incorrectVisual
          : incorrectVisual // ignore: cast_nullable_to_non_nullable
              as int,
      incorrectAudio: null == incorrectAudio
          ? _value.incorrectAudio
          : incorrectAudio // ignore: cast_nullable_to_non_nullable
              as int,
      trials: null == trials
          ? _value.trials
          : trials // ignore: cast_nullable_to_non_nullable
              as List<DualNBackTrial>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DualNBackSessionDataImplCopyWith<$Res>
    implements $DualNBackSessionDataCopyWith<$Res> {
  factory _$$DualNBackSessionDataImplCopyWith(_$DualNBackSessionDataImpl value,
          $Res Function(_$DualNBackSessionDataImpl) then) =
      __$$DualNBackSessionDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int nLevel,
      int totalTrials,
      int correctVisual,
      int correctAudio,
      int incorrectVisual,
      int incorrectAudio,
      List<DualNBackTrial> trials});
}

/// @nodoc
class __$$DualNBackSessionDataImplCopyWithImpl<$Res>
    extends _$DualNBackSessionDataCopyWithImpl<$Res, _$DualNBackSessionDataImpl>
    implements _$$DualNBackSessionDataImplCopyWith<$Res> {
  __$$DualNBackSessionDataImplCopyWithImpl(_$DualNBackSessionDataImpl _value,
      $Res Function(_$DualNBackSessionDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of DualNBackSessionData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nLevel = null,
    Object? totalTrials = null,
    Object? correctVisual = null,
    Object? correctAudio = null,
    Object? incorrectVisual = null,
    Object? incorrectAudio = null,
    Object? trials = null,
  }) {
    return _then(_$DualNBackSessionDataImpl(
      nLevel: null == nLevel
          ? _value.nLevel
          : nLevel // ignore: cast_nullable_to_non_nullable
              as int,
      totalTrials: null == totalTrials
          ? _value.totalTrials
          : totalTrials // ignore: cast_nullable_to_non_nullable
              as int,
      correctVisual: null == correctVisual
          ? _value.correctVisual
          : correctVisual // ignore: cast_nullable_to_non_nullable
              as int,
      correctAudio: null == correctAudio
          ? _value.correctAudio
          : correctAudio // ignore: cast_nullable_to_non_nullable
              as int,
      incorrectVisual: null == incorrectVisual
          ? _value.incorrectVisual
          : incorrectVisual // ignore: cast_nullable_to_non_nullable
              as int,
      incorrectAudio: null == incorrectAudio
          ? _value.incorrectAudio
          : incorrectAudio // ignore: cast_nullable_to_non_nullable
              as int,
      trials: null == trials
          ? _value._trials
          : trials // ignore: cast_nullable_to_non_nullable
              as List<DualNBackTrial>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DualNBackSessionDataImpl implements _DualNBackSessionData {
  const _$DualNBackSessionDataImpl(
      {required this.nLevel,
      required this.totalTrials,
      required this.correctVisual,
      required this.correctAudio,
      required this.incorrectVisual,
      required this.incorrectAudio,
      final List<DualNBackTrial> trials = const []})
      : _trials = trials;

  factory _$DualNBackSessionDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$DualNBackSessionDataImplFromJson(json);

  @override
  final int nLevel;
  @override
  final int totalTrials;
  @override
  final int correctVisual;
  @override
  final int correctAudio;
  @override
  final int incorrectVisual;
  @override
  final int incorrectAudio;
  final List<DualNBackTrial> _trials;
  @override
  @JsonKey()
  List<DualNBackTrial> get trials {
    if (_trials is EqualUnmodifiableListView) return _trials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trials);
  }

  @override
  String toString() {
    return 'DualNBackSessionData(nLevel: $nLevel, totalTrials: $totalTrials, correctVisual: $correctVisual, correctAudio: $correctAudio, incorrectVisual: $incorrectVisual, incorrectAudio: $incorrectAudio, trials: $trials)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DualNBackSessionDataImpl &&
            (identical(other.nLevel, nLevel) || other.nLevel == nLevel) &&
            (identical(other.totalTrials, totalTrials) ||
                other.totalTrials == totalTrials) &&
            (identical(other.correctVisual, correctVisual) ||
                other.correctVisual == correctVisual) &&
            (identical(other.correctAudio, correctAudio) ||
                other.correctAudio == correctAudio) &&
            (identical(other.incorrectVisual, incorrectVisual) ||
                other.incorrectVisual == incorrectVisual) &&
            (identical(other.incorrectAudio, incorrectAudio) ||
                other.incorrectAudio == incorrectAudio) &&
            const DeepCollectionEquality().equals(other._trials, _trials));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nLevel,
      totalTrials,
      correctVisual,
      correctAudio,
      incorrectVisual,
      incorrectAudio,
      const DeepCollectionEquality().hash(_trials));

  /// Create a copy of DualNBackSessionData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DualNBackSessionDataImplCopyWith<_$DualNBackSessionDataImpl>
      get copyWith =>
          __$$DualNBackSessionDataImplCopyWithImpl<_$DualNBackSessionDataImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DualNBackSessionDataImplToJson(
      this,
    );
  }
}

abstract class _DualNBackSessionData implements DualNBackSessionData {
  const factory _DualNBackSessionData(
      {required final int nLevel,
      required final int totalTrials,
      required final int correctVisual,
      required final int correctAudio,
      required final int incorrectVisual,
      required final int incorrectAudio,
      final List<DualNBackTrial> trials}) = _$DualNBackSessionDataImpl;

  factory _DualNBackSessionData.fromJson(Map<String, dynamic> json) =
      _$DualNBackSessionDataImpl.fromJson;

  @override
  int get nLevel;
  @override
  int get totalTrials;
  @override
  int get correctVisual;
  @override
  int get correctAudio;
  @override
  int get incorrectVisual;
  @override
  int get incorrectAudio;
  @override
  List<DualNBackTrial> get trials;

  /// Create a copy of DualNBackSessionData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DualNBackSessionDataImplCopyWith<_$DualNBackSessionDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DualNBackTrial _$DualNBackTrialFromJson(Map<String, dynamic> json) {
  return _DualNBackTrial.fromJson(json);
}

/// @nodoc
mixin _$DualNBackTrial {
  int get trialNumber => throw _privateConstructorUsedError;
  String get visualPosition => throw _privateConstructorUsedError;
  String get audioStimulus => throw _privateConstructorUsedError;
  bool get shouldMatchVisual => throw _privateConstructorUsedError;
  bool get shouldMatchAudio => throw _privateConstructorUsedError;
  bool get userResponseVisual => throw _privateConstructorUsedError;
  bool get userResponseAudio => throw _privateConstructorUsedError;
  Duration get responseTime => throw _privateConstructorUsedError;

  /// Serializes this DualNBackTrial to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DualNBackTrial
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DualNBackTrialCopyWith<DualNBackTrial> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DualNBackTrialCopyWith<$Res> {
  factory $DualNBackTrialCopyWith(
          DualNBackTrial value, $Res Function(DualNBackTrial) then) =
      _$DualNBackTrialCopyWithImpl<$Res, DualNBackTrial>;
  @useResult
  $Res call(
      {int trialNumber,
      String visualPosition,
      String audioStimulus,
      bool shouldMatchVisual,
      bool shouldMatchAudio,
      bool userResponseVisual,
      bool userResponseAudio,
      Duration responseTime});
}

/// @nodoc
class _$DualNBackTrialCopyWithImpl<$Res, $Val extends DualNBackTrial>
    implements $DualNBackTrialCopyWith<$Res> {
  _$DualNBackTrialCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DualNBackTrial
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trialNumber = null,
    Object? visualPosition = null,
    Object? audioStimulus = null,
    Object? shouldMatchVisual = null,
    Object? shouldMatchAudio = null,
    Object? userResponseVisual = null,
    Object? userResponseAudio = null,
    Object? responseTime = null,
  }) {
    return _then(_value.copyWith(
      trialNumber: null == trialNumber
          ? _value.trialNumber
          : trialNumber // ignore: cast_nullable_to_non_nullable
              as int,
      visualPosition: null == visualPosition
          ? _value.visualPosition
          : visualPosition // ignore: cast_nullable_to_non_nullable
              as String,
      audioStimulus: null == audioStimulus
          ? _value.audioStimulus
          : audioStimulus // ignore: cast_nullable_to_non_nullable
              as String,
      shouldMatchVisual: null == shouldMatchVisual
          ? _value.shouldMatchVisual
          : shouldMatchVisual // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldMatchAudio: null == shouldMatchAudio
          ? _value.shouldMatchAudio
          : shouldMatchAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      userResponseVisual: null == userResponseVisual
          ? _value.userResponseVisual
          : userResponseVisual // ignore: cast_nullable_to_non_nullable
              as bool,
      userResponseAudio: null == userResponseAudio
          ? _value.userResponseAudio
          : userResponseAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DualNBackTrialImplCopyWith<$Res>
    implements $DualNBackTrialCopyWith<$Res> {
  factory _$$DualNBackTrialImplCopyWith(_$DualNBackTrialImpl value,
          $Res Function(_$DualNBackTrialImpl) then) =
      __$$DualNBackTrialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int trialNumber,
      String visualPosition,
      String audioStimulus,
      bool shouldMatchVisual,
      bool shouldMatchAudio,
      bool userResponseVisual,
      bool userResponseAudio,
      Duration responseTime});
}

/// @nodoc
class __$$DualNBackTrialImplCopyWithImpl<$Res>
    extends _$DualNBackTrialCopyWithImpl<$Res, _$DualNBackTrialImpl>
    implements _$$DualNBackTrialImplCopyWith<$Res> {
  __$$DualNBackTrialImplCopyWithImpl(
      _$DualNBackTrialImpl _value, $Res Function(_$DualNBackTrialImpl) _then)
      : super(_value, _then);

  /// Create a copy of DualNBackTrial
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trialNumber = null,
    Object? visualPosition = null,
    Object? audioStimulus = null,
    Object? shouldMatchVisual = null,
    Object? shouldMatchAudio = null,
    Object? userResponseVisual = null,
    Object? userResponseAudio = null,
    Object? responseTime = null,
  }) {
    return _then(_$DualNBackTrialImpl(
      trialNumber: null == trialNumber
          ? _value.trialNumber
          : trialNumber // ignore: cast_nullable_to_non_nullable
              as int,
      visualPosition: null == visualPosition
          ? _value.visualPosition
          : visualPosition // ignore: cast_nullable_to_non_nullable
              as String,
      audioStimulus: null == audioStimulus
          ? _value.audioStimulus
          : audioStimulus // ignore: cast_nullable_to_non_nullable
              as String,
      shouldMatchVisual: null == shouldMatchVisual
          ? _value.shouldMatchVisual
          : shouldMatchVisual // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldMatchAudio: null == shouldMatchAudio
          ? _value.shouldMatchAudio
          : shouldMatchAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      userResponseVisual: null == userResponseVisual
          ? _value.userResponseVisual
          : userResponseVisual // ignore: cast_nullable_to_non_nullable
              as bool,
      userResponseAudio: null == userResponseAudio
          ? _value.userResponseAudio
          : userResponseAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DualNBackTrialImpl implements _DualNBackTrial {
  const _$DualNBackTrialImpl(
      {required this.trialNumber,
      required this.visualPosition,
      required this.audioStimulus,
      required this.shouldMatchVisual,
      required this.shouldMatchAudio,
      required this.userResponseVisual,
      required this.userResponseAudio,
      required this.responseTime});

  factory _$DualNBackTrialImpl.fromJson(Map<String, dynamic> json) =>
      _$$DualNBackTrialImplFromJson(json);

  @override
  final int trialNumber;
  @override
  final String visualPosition;
  @override
  final String audioStimulus;
  @override
  final bool shouldMatchVisual;
  @override
  final bool shouldMatchAudio;
  @override
  final bool userResponseVisual;
  @override
  final bool userResponseAudio;
  @override
  final Duration responseTime;

  @override
  String toString() {
    return 'DualNBackTrial(trialNumber: $trialNumber, visualPosition: $visualPosition, audioStimulus: $audioStimulus, shouldMatchVisual: $shouldMatchVisual, shouldMatchAudio: $shouldMatchAudio, userResponseVisual: $userResponseVisual, userResponseAudio: $userResponseAudio, responseTime: $responseTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DualNBackTrialImpl &&
            (identical(other.trialNumber, trialNumber) ||
                other.trialNumber == trialNumber) &&
            (identical(other.visualPosition, visualPosition) ||
                other.visualPosition == visualPosition) &&
            (identical(other.audioStimulus, audioStimulus) ||
                other.audioStimulus == audioStimulus) &&
            (identical(other.shouldMatchVisual, shouldMatchVisual) ||
                other.shouldMatchVisual == shouldMatchVisual) &&
            (identical(other.shouldMatchAudio, shouldMatchAudio) ||
                other.shouldMatchAudio == shouldMatchAudio) &&
            (identical(other.userResponseVisual, userResponseVisual) ||
                other.userResponseVisual == userResponseVisual) &&
            (identical(other.userResponseAudio, userResponseAudio) ||
                other.userResponseAudio == userResponseAudio) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      trialNumber,
      visualPosition,
      audioStimulus,
      shouldMatchVisual,
      shouldMatchAudio,
      userResponseVisual,
      userResponseAudio,
      responseTime);

  /// Create a copy of DualNBackTrial
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DualNBackTrialImplCopyWith<_$DualNBackTrialImpl> get copyWith =>
      __$$DualNBackTrialImplCopyWithImpl<_$DualNBackTrialImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DualNBackTrialImplToJson(
      this,
    );
  }
}

abstract class _DualNBackTrial implements DualNBackTrial {
  const factory _DualNBackTrial(
      {required final int trialNumber,
      required final String visualPosition,
      required final String audioStimulus,
      required final bool shouldMatchVisual,
      required final bool shouldMatchAudio,
      required final bool userResponseVisual,
      required final bool userResponseAudio,
      required final Duration responseTime}) = _$DualNBackTrialImpl;

  factory _DualNBackTrial.fromJson(Map<String, dynamic> json) =
      _$DualNBackTrialImpl.fromJson;

  @override
  int get trialNumber;
  @override
  String get visualPosition;
  @override
  String get audioStimulus;
  @override
  bool get shouldMatchVisual;
  @override
  bool get shouldMatchAudio;
  @override
  bool get userResponseVisual;
  @override
  bool get userResponseAudio;
  @override
  Duration get responseTime;

  /// Create a copy of DualNBackTrial
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DualNBackTrialImplCopyWith<_$DualNBackTrialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
