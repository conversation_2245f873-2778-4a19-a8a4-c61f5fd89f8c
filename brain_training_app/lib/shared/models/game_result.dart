import 'package:freezed_annotation/freezed_annotation.dart';

part 'game_result.freezed.dart';
part 'game_result.g.dart';

enum GameType {
  dualNBack,
  flashCards,
  workingMemory,
  attentionTraining,
}

@freezed
class GameResult with _$GameResult {
  const factory GameResult({
    required String id,
    required String userId,
    required GameType gameType,
    required int level,
    required int score,
    required double accuracy,
    required Duration duration,
    required DateTime timestamp,
    @Default({}) Map<String, dynamic> gameSpecificData,
  }) = _GameResult;

  factory GameResult.fromJson(Map<String, dynamic> json) =>
      _$GameResultFromJson(json);
}
