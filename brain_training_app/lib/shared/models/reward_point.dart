import 'package:freezed_annotation/freezed_annotation.dart';

part 'reward_point.freezed.dart';
part 'reward_point.g.dart';

/// Types of rewards that can be earned
enum RewardType {
  dailyLogin,
  gameCompletion,
  achievement,
  streak,
  adWatch,
  purchase,
  bonus,
}

/// Types of rewards that can be unlocked with points
enum UnlockableReward {
  flashCardStorage100,  // +100 storage slots
  flashCardStorage500,  // +500 storage slots
  flashCardStorage1000, // +1000 storage slots
  premiumTheme,
  hints,
  skipCooldown,
}

@freezed
class RewardPoint with _$RewardPoint {
  const factory RewardPoint({
    required String id,
    required String userId,
    required RewardType type,
    required int amount,
    required String source,
    required DateTime earnedAt,
    String? description,
    Map<String, dynamic>? metadata,
  }) = _RewardPoint;

  factory RewardPoint.fromJson(Map<String, dynamic> json) => _$RewardPointFromJson(json);
}

@freezed
class RewardBalance with _$RewardBalance {
  const factory RewardBalance({
    required String userId,
    required int totalEarned,
    required int totalSpent,
    required int currentBalance,
    required DateTime lastUpdated,
    @Default({}) Map<RewardType, int> earnedByType,
    @Default({}) Map<UnlockableReward, int> spentOnRewards,
  }) = _RewardBalance;

  factory RewardBalance.fromJson(Map<String, dynamic> json) => _$RewardBalanceFromJson(json);
}

@freezed
class RewardTransaction with _$RewardTransaction {
  const factory RewardTransaction({
    required String id,
    required String userId,
    required int amount,
    required String description,
    required DateTime timestamp,
    required bool isEarned, // true for earned, false for spent
    String? source,
    UnlockableReward? unlockableReward,
    Map<String, dynamic>? metadata,
  }) = _RewardTransaction;

  factory RewardTransaction.fromJson(Map<String, dynamic> json) => _$RewardTransactionFromJson(json);
}

@freezed
class UnlockableRewardConfig with _$UnlockableRewardConfig {
  const factory UnlockableRewardConfig({
    required UnlockableReward type,
    required String title,
    required String description,
    required int cost,
    required bool isRepeatable,
    String? iconPath,
    @Default({}) Map<String, dynamic> benefits,
    @Default([]) List<String> requirements,
  }) = _UnlockableRewardConfig;

  factory UnlockableRewardConfig.fromJson(Map<String, dynamic> json) => _$UnlockableRewardConfigFromJson(json);
}

@freezed
class UserUnlock with _$UserUnlock {
  const factory UserUnlock({
    required String id,
    required String userId,
    required UnlockableReward reward,
    required int pointsSpent,
    required DateTime unlockedAt,
    String? transactionId,
    @Default(1) int quantity,
    Map<String, dynamic>? metadata,
  }) = _UserUnlock;

  factory UserUnlock.fromJson(Map<String, dynamic> json) => _$UserUnlockFromJson(json);
}

/// Abstract class for reward acquisition sources
abstract class RewardSource {
  String get name;
  String get description;
  Future<RewardPoint?> checkEligibility(String userId);
  Future<RewardPoint> award(String userId, Map<String, dynamic> context);
}

/// Daily login reward source
class DailyLoginRewardSource implements RewardSource {
  @override
  String get name => 'daily_login';
  
  @override
  String get description => '매일 로그인 보상';

  @override
  Future<RewardPoint?> checkEligibility(String userId) async {
    // Implementation will check if user hasn't logged in today
    return null; // Placeholder
  }

  @override
  Future<RewardPoint> award(String userId, Map<String, dynamic> context) async {
    return RewardPoint(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: RewardType.dailyLogin,
      amount: 10,
      source: name,
      earnedAt: DateTime.now(),
      description: '일일 로그인 보상',
    );
  }
}

/// Game completion reward source
class GameCompletionRewardSource implements RewardSource {
  @override
  String get name => 'game_completion';
  
  @override
  String get description => '게임 완료 보상';

  @override
  Future<RewardPoint?> checkEligibility(String userId) async {
    return null; // Placeholder
  }

  @override
  Future<RewardPoint> award(String userId, Map<String, dynamic> context) async {
    final score = context['score'] as int? ?? 0;
    final baseReward = 5;
    final bonusReward = (score / 1000).floor();
    
    return RewardPoint(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: RewardType.gameCompletion,
      amount: baseReward + bonusReward,
      source: name,
      earnedAt: DateTime.now(),
      description: '게임 완료 보상',
      metadata: context,
    );
  }
}

/// Ad watch reward source (for future implementation)
class AdWatchRewardSource implements RewardSource {
  @override
  String get name => 'ad_watch';
  
  @override
  String get description => '광고 시청 보상';

  @override
  Future<RewardPoint?> checkEligibility(String userId) async {
    // Check daily ad watch limit
    return null; // Placeholder
  }

  @override
  Future<RewardPoint> award(String userId, Map<String, dynamic> context) async {
    return RewardPoint(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: RewardType.adWatch,
      amount: 25,
      source: name,
      earnedAt: DateTime.now(),
      description: '광고 시청 보상',
    );
  }
}

/// Unlockable reward configurations
class UnlockableRewards {
  static const List<UnlockableRewardConfig> configs = [
    UnlockableRewardConfig(
      type: UnlockableReward.flashCardStorage100,
      title: '플래시카드 저장소 +100',
      description: '플래시카드 저장 한도를 100개 늘립니다',
      cost: 500,
      isRepeatable: true,
      iconPath: 'assets/icons/storage_small.png',
      benefits: {'storage_increase': 100},
    ),
    UnlockableRewardConfig(
      type: UnlockableReward.flashCardStorage500,
      title: '플래시카드 저장소 +500',
      description: '플래시카드 저장 한도를 500개 늘립니다',
      cost: 2000,
      isRepeatable: true,
      iconPath: 'assets/icons/storage_medium.png',
      benefits: {'storage_increase': 500},
    ),
    UnlockableRewardConfig(
      type: UnlockableReward.flashCardStorage1000,
      title: '플래시카드 저장소 +1000',
      description: '플래시카드 저장 한도를 1000개 늘립니다',
      cost: 3500,
      isRepeatable: true,
      iconPath: 'assets/icons/storage_large.png',
      benefits: {'storage_increase': 1000},
    ),
    UnlockableRewardConfig(
      type: UnlockableReward.hints,
      title: '힌트 팩',
      description: '게임에서 사용할 수 있는 힌트 5개를 받습니다',
      cost: 100,
      isRepeatable: true,
      iconPath: 'assets/icons/hint.png',
      benefits: {'hints': 5},
    ),
  ];

  static UnlockableRewardConfig? getConfig(UnlockableReward reward) {
    try {
      return configs.firstWhere((config) => config.type == reward);
    } catch (e) {
      return null;
    }
  }
}