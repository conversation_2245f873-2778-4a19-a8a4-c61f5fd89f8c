import 'package:freezed_annotation/freezed_annotation.dart';

part 'flash_card.freezed.dart';
part 'flash_card.g.dart';

@freezed
class FlashCard with _$FlashCard {
  const factory FlashCard({
    required String id,
    required String deckId,
    required String front,
    required String back,
    String? hint,
    @Default([]) List<String> tags,
    required DateTime createdAt,
    DateTime? updatedAt,
    required CardReview review,
  }) = _FlashCard;

  factory FlashCard.fromJson(Map<String, dynamic> json) => _$FlashCardFromJson(json);
}

@freezed
class CardDeck with _$CardDeck {
  const factory CardDeck({
    required String id,
    required String userId,
    required String name,
    required String description,
    String? category,
    @Default([]) List<FlashCard> cards,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(true) bool isActive,
    @Default(20) int dailyGoal,
    @Default({}) Map<String, dynamic> settings,
  }) = _CardDeck;

  factory CardDeck.fromJson(Map<String, dynamic> json) => _$Card<PERSON>eck<PERSON>rom<PERSON>son(json);
}

@freezed
class CardReview with _$CardReview {
  const factory CardReview({
    @Default(2.5) double easeFactor,
    @Default(1) int interval,
    @Default(0) int repetitions,
    DateTime? nextReviewDate,
    DateTime? lastReviewedAt,
    @Default(CardQuality.none) CardQuality lastQuality,
  }) = _CardReview;

  factory CardReview.fromJson(Map<String, dynamic> json) => _$CardReviewFromJson(json);
}

enum CardQuality {
  none,
  blackout,
  incorrect,
  hard,
  good,
  easy,
}

@freezed
class CardStudySession with _$CardStudySession {
  const factory CardStudySession({
    required String id,
    required String userId,
    required String deckId,
    required DateTime startTime,
    DateTime? endTime,
    @Default([]) List<CardReviewResult> reviews,
    @Default(0) int cardsReviewed,
    @Default(0) int correctAnswers,
  }) = _CardStudySession;

  factory CardStudySession.fromJson(Map<String, dynamic> json) => _$CardStudySessionFromJson(json);
}

@freezed
class CardReviewResult with _$CardReviewResult {
  const factory CardReviewResult({
    required String cardId,
    required CardQuality quality,
    required Duration responseTime,
    required DateTime reviewedAt,
  }) = _CardReviewResult;

  factory CardReviewResult.fromJson(Map<String, dynamic> json) => _$CardReviewResultFromJson(json);
}

@freezed
class StorageUsage with _$StorageUsage {
  const factory StorageUsage({
    required int currentCount,
    required int limit,
    required int remainingCount,
    required double usagePercentage,
  }) = _StorageUsage;

  factory StorageUsage.fromJson(Map<String, dynamic> json) => _$StorageUsageFromJson(json);
}