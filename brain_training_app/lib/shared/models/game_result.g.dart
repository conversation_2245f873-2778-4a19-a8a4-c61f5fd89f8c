// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GameResultImpl _$$GameResultImplFromJson(Map<String, dynamic> json) =>
    _$GameResultImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      gameType: $enumDecode(_$GameTypeEnumMap, json['gameType']),
      level: (json['level'] as num).toInt(),
      score: (json['score'] as num).toInt(),
      accuracy: (json['accuracy'] as num).toDouble(),
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      timestamp: DateTime.parse(json['timestamp'] as String),
      gameSpecificData:
          json['gameSpecificData'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$GameResultImplToJson(_$GameResultImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'gameType': _$GameTypeEnumMap[instance.gameType]!,
      'level': instance.level,
      'score': instance.score,
      'accuracy': instance.accuracy,
      'duration': instance.duration.inMicroseconds,
      'timestamp': instance.timestamp.toIso8601String(),
      'gameSpecificData': instance.gameSpecificData,
    };

const _$GameTypeEnumMap = {
  GameType.dualNBack: 'dualNBack',
  GameType.flashCards: 'flashCards',
  GameType.workingMemory: 'workingMemory',
  GameType.attentionTraining: 'attentionTraining',
};
