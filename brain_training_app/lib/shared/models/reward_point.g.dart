// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reward_point.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RewardPointImpl _$$RewardPointImplFromJson(Map<String, dynamic> json) =>
    _$RewardPointImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$RewardTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toInt(),
      source: json['source'] as String,
      earnedAt: DateTime.parse(json['earnedAt'] as String),
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$RewardPointImplToJson(_$RewardPointImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$RewardTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'source': instance.source,
      'earnedAt': instance.earnedAt.toIso8601String(),
      'description': instance.description,
      'metadata': instance.metadata,
    };

const _$RewardTypeEnumMap = {
  RewardType.dailyLogin: 'dailyLogin',
  RewardType.gameCompletion: 'gameCompletion',
  RewardType.achievement: 'achievement',
  RewardType.streak: 'streak',
  RewardType.adWatch: 'adWatch',
  RewardType.purchase: 'purchase',
  RewardType.bonus: 'bonus',
};

_$RewardBalanceImpl _$$RewardBalanceImplFromJson(Map<String, dynamic> json) =>
    _$RewardBalanceImpl(
      userId: json['userId'] as String,
      totalEarned: (json['totalEarned'] as num).toInt(),
      totalSpent: (json['totalSpent'] as num).toInt(),
      currentBalance: (json['currentBalance'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      earnedByType: (json['earnedByType'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                $enumDecode(_$RewardTypeEnumMap, k), (e as num).toInt()),
          ) ??
          const {},
      spentOnRewards: (json['spentOnRewards'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                $enumDecode(_$UnlockableRewardEnumMap, k), (e as num).toInt()),
          ) ??
          const {},
    );

Map<String, dynamic> _$$RewardBalanceImplToJson(_$RewardBalanceImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'totalEarned': instance.totalEarned,
      'totalSpent': instance.totalSpent,
      'currentBalance': instance.currentBalance,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'earnedByType': instance.earnedByType
          .map((k, e) => MapEntry(_$RewardTypeEnumMap[k]!, e)),
      'spentOnRewards': instance.spentOnRewards
          .map((k, e) => MapEntry(_$UnlockableRewardEnumMap[k]!, e)),
    };

const _$UnlockableRewardEnumMap = {
  UnlockableReward.flashCardStorage100: 'flashCardStorage100',
  UnlockableReward.flashCardStorage500: 'flashCardStorage500',
  UnlockableReward.flashCardStorage1000: 'flashCardStorage1000',
  UnlockableReward.premiumTheme: 'premiumTheme',
  UnlockableReward.hints: 'hints',
  UnlockableReward.skipCooldown: 'skipCooldown',
};

_$RewardTransactionImpl _$$RewardTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$RewardTransactionImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      amount: (json['amount'] as num).toInt(),
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isEarned: json['isEarned'] as bool,
      source: json['source'] as String?,
      unlockableReward: $enumDecodeNullable(
          _$UnlockableRewardEnumMap, json['unlockableReward']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$RewardTransactionImplToJson(
        _$RewardTransactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'amount': instance.amount,
      'description': instance.description,
      'timestamp': instance.timestamp.toIso8601String(),
      'isEarned': instance.isEarned,
      'source': instance.source,
      'unlockableReward': _$UnlockableRewardEnumMap[instance.unlockableReward],
      'metadata': instance.metadata,
    };

_$UnlockableRewardConfigImpl _$$UnlockableRewardConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$UnlockableRewardConfigImpl(
      type: $enumDecode(_$UnlockableRewardEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      cost: (json['cost'] as num).toInt(),
      isRepeatable: json['isRepeatable'] as bool,
      iconPath: json['iconPath'] as String?,
      benefits: json['benefits'] as Map<String, dynamic>? ?? const {},
      requirements: (json['requirements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$UnlockableRewardConfigImplToJson(
        _$UnlockableRewardConfigImpl instance) =>
    <String, dynamic>{
      'type': _$UnlockableRewardEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'cost': instance.cost,
      'isRepeatable': instance.isRepeatable,
      'iconPath': instance.iconPath,
      'benefits': instance.benefits,
      'requirements': instance.requirements,
    };

_$UserUnlockImpl _$$UserUnlockImplFromJson(Map<String, dynamic> json) =>
    _$UserUnlockImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      reward: $enumDecode(_$UnlockableRewardEnumMap, json['reward']),
      pointsSpent: (json['pointsSpent'] as num).toInt(),
      unlockedAt: DateTime.parse(json['unlockedAt'] as String),
      transactionId: json['transactionId'] as String?,
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$UserUnlockImplToJson(_$UserUnlockImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'reward': _$UnlockableRewardEnumMap[instance.reward]!,
      'pointsSpent': instance.pointsSpent,
      'unlockedAt': instance.unlockedAt.toIso8601String(),
      'transactionId': instance.transactionId,
      'quantity': instance.quantity,
      'metadata': instance.metadata,
    };
