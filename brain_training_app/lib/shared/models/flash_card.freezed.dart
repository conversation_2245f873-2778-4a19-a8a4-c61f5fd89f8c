// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flash_card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FlashCard _$FlashCardFromJson(Map<String, dynamic> json) {
  return _FlashCard.fromJson(json);
}

/// @nodoc
mixin _$FlashCard {
  String get id => throw _privateConstructorUsedError;
  String get deckId => throw _privateConstructorUsedError;
  String get front => throw _privateConstructorUsedError;
  String get back => throw _privateConstructorUsedError;
  String? get hint => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  CardReview get review => throw _privateConstructorUsedError;

  /// Serializes this FlashCard to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlashCardCopyWith<FlashCard> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlashCardCopyWith<$Res> {
  factory $FlashCardCopyWith(FlashCard value, $Res Function(FlashCard) then) =
      _$FlashCardCopyWithImpl<$Res, FlashCard>;
  @useResult
  $Res call(
      {String id,
      String deckId,
      String front,
      String back,
      String? hint,
      List<String> tags,
      DateTime createdAt,
      DateTime? updatedAt,
      CardReview review});

  $CardReviewCopyWith<$Res> get review;
}

/// @nodoc
class _$FlashCardCopyWithImpl<$Res, $Val extends FlashCard>
    implements $FlashCardCopyWith<$Res> {
  _$FlashCardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deckId = null,
    Object? front = null,
    Object? back = null,
    Object? hint = freezed,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? review = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      deckId: null == deckId
          ? _value.deckId
          : deckId // ignore: cast_nullable_to_non_nullable
              as String,
      front: null == front
          ? _value.front
          : front // ignore: cast_nullable_to_non_nullable
              as String,
      back: null == back
          ? _value.back
          : back // ignore: cast_nullable_to_non_nullable
              as String,
      hint: freezed == hint
          ? _value.hint
          : hint // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      review: null == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as CardReview,
    ) as $Val);
  }

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CardReviewCopyWith<$Res> get review {
    return $CardReviewCopyWith<$Res>(_value.review, (value) {
      return _then(_value.copyWith(review: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FlashCardImplCopyWith<$Res>
    implements $FlashCardCopyWith<$Res> {
  factory _$$FlashCardImplCopyWith(
          _$FlashCardImpl value, $Res Function(_$FlashCardImpl) then) =
      __$$FlashCardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String deckId,
      String front,
      String back,
      String? hint,
      List<String> tags,
      DateTime createdAt,
      DateTime? updatedAt,
      CardReview review});

  @override
  $CardReviewCopyWith<$Res> get review;
}

/// @nodoc
class __$$FlashCardImplCopyWithImpl<$Res>
    extends _$FlashCardCopyWithImpl<$Res, _$FlashCardImpl>
    implements _$$FlashCardImplCopyWith<$Res> {
  __$$FlashCardImplCopyWithImpl(
      _$FlashCardImpl _value, $Res Function(_$FlashCardImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deckId = null,
    Object? front = null,
    Object? back = null,
    Object? hint = freezed,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? review = null,
  }) {
    return _then(_$FlashCardImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      deckId: null == deckId
          ? _value.deckId
          : deckId // ignore: cast_nullable_to_non_nullable
              as String,
      front: null == front
          ? _value.front
          : front // ignore: cast_nullable_to_non_nullable
              as String,
      back: null == back
          ? _value.back
          : back // ignore: cast_nullable_to_non_nullable
              as String,
      hint: freezed == hint
          ? _value.hint
          : hint // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      review: null == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as CardReview,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlashCardImpl implements _FlashCard {
  const _$FlashCardImpl(
      {required this.id,
      required this.deckId,
      required this.front,
      required this.back,
      this.hint,
      final List<String> tags = const [],
      required this.createdAt,
      this.updatedAt,
      required this.review})
      : _tags = tags;

  factory _$FlashCardImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlashCardImplFromJson(json);

  @override
  final String id;
  @override
  final String deckId;
  @override
  final String front;
  @override
  final String back;
  @override
  final String? hint;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final CardReview review;

  @override
  String toString() {
    return 'FlashCard(id: $id, deckId: $deckId, front: $front, back: $back, hint: $hint, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt, review: $review)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlashCardImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.deckId, deckId) || other.deckId == deckId) &&
            (identical(other.front, front) || other.front == front) &&
            (identical(other.back, back) || other.back == back) &&
            (identical(other.hint, hint) || other.hint == hint) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.review, review) || other.review == review));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, deckId, front, back, hint,
      const DeepCollectionEquality().hash(_tags), createdAt, updatedAt, review);

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FlashCardImplCopyWith<_$FlashCardImpl> get copyWith =>
      __$$FlashCardImplCopyWithImpl<_$FlashCardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlashCardImplToJson(
      this,
    );
  }
}

abstract class _FlashCard implements FlashCard {
  const factory _FlashCard(
      {required final String id,
      required final String deckId,
      required final String front,
      required final String back,
      final String? hint,
      final List<String> tags,
      required final DateTime createdAt,
      final DateTime? updatedAt,
      required final CardReview review}) = _$FlashCardImpl;

  factory _FlashCard.fromJson(Map<String, dynamic> json) =
      _$FlashCardImpl.fromJson;

  @override
  String get id;
  @override
  String get deckId;
  @override
  String get front;
  @override
  String get back;
  @override
  String? get hint;
  @override
  List<String> get tags;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  CardReview get review;

  /// Create a copy of FlashCard
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FlashCardImplCopyWith<_$FlashCardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CardDeck _$CardDeckFromJson(Map<String, dynamic> json) {
  return _CardDeck.fromJson(json);
}

/// @nodoc
mixin _$CardDeck {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;
  List<FlashCard> get cards => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  int get dailyGoal => throw _privateConstructorUsedError;
  Map<String, dynamic> get settings => throw _privateConstructorUsedError;

  /// Serializes this CardDeck to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CardDeck
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CardDeckCopyWith<CardDeck> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardDeckCopyWith<$Res> {
  factory $CardDeckCopyWith(CardDeck value, $Res Function(CardDeck) then) =
      _$CardDeckCopyWithImpl<$Res, CardDeck>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      String description,
      String? category,
      List<FlashCard> cards,
      DateTime createdAt,
      DateTime? updatedAt,
      bool isActive,
      int dailyGoal,
      Map<String, dynamic> settings});
}

/// @nodoc
class _$CardDeckCopyWithImpl<$Res, $Val extends CardDeck>
    implements $CardDeckCopyWith<$Res> {
  _$CardDeckCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardDeck
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? description = null,
    Object? category = freezed,
    Object? cards = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? dailyGoal = null,
    Object? settings = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      cards: null == cards
          ? _value.cards
          : cards // ignore: cast_nullable_to_non_nullable
              as List<FlashCard>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyGoal: null == dailyGoal
          ? _value.dailyGoal
          : dailyGoal // ignore: cast_nullable_to_non_nullable
              as int,
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CardDeckImplCopyWith<$Res>
    implements $CardDeckCopyWith<$Res> {
  factory _$$CardDeckImplCopyWith(
          _$CardDeckImpl value, $Res Function(_$CardDeckImpl) then) =
      __$$CardDeckImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      String description,
      String? category,
      List<FlashCard> cards,
      DateTime createdAt,
      DateTime? updatedAt,
      bool isActive,
      int dailyGoal,
      Map<String, dynamic> settings});
}

/// @nodoc
class __$$CardDeckImplCopyWithImpl<$Res>
    extends _$CardDeckCopyWithImpl<$Res, _$CardDeckImpl>
    implements _$$CardDeckImplCopyWith<$Res> {
  __$$CardDeckImplCopyWithImpl(
      _$CardDeckImpl _value, $Res Function(_$CardDeckImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardDeck
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? description = null,
    Object? category = freezed,
    Object? cards = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? dailyGoal = null,
    Object? settings = null,
  }) {
    return _then(_$CardDeckImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      cards: null == cards
          ? _value._cards
          : cards // ignore: cast_nullable_to_non_nullable
              as List<FlashCard>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyGoal: null == dailyGoal
          ? _value.dailyGoal
          : dailyGoal // ignore: cast_nullable_to_non_nullable
              as int,
      settings: null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CardDeckImpl implements _CardDeck {
  const _$CardDeckImpl(
      {required this.id,
      required this.userId,
      required this.name,
      required this.description,
      this.category,
      final List<FlashCard> cards = const [],
      required this.createdAt,
      this.updatedAt,
      this.isActive = true,
      this.dailyGoal = 20,
      final Map<String, dynamic> settings = const {}})
      : _cards = cards,
        _settings = settings;

  factory _$CardDeckImpl.fromJson(Map<String, dynamic> json) =>
      _$$CardDeckImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String name;
  @override
  final String description;
  @override
  final String? category;
  final List<FlashCard> _cards;
  @override
  @JsonKey()
  List<FlashCard> get cards {
    if (_cards is EqualUnmodifiableListView) return _cards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cards);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final int dailyGoal;
  final Map<String, dynamic> _settings;
  @override
  @JsonKey()
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'CardDeck(id: $id, userId: $userId, name: $name, description: $description, category: $category, cards: $cards, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive, dailyGoal: $dailyGoal, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardDeckImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._cards, _cards) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.dailyGoal, dailyGoal) ||
                other.dailyGoal == dailyGoal) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      name,
      description,
      category,
      const DeepCollectionEquality().hash(_cards),
      createdAt,
      updatedAt,
      isActive,
      dailyGoal,
      const DeepCollectionEquality().hash(_settings));

  /// Create a copy of CardDeck
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardDeckImplCopyWith<_$CardDeckImpl> get copyWith =>
      __$$CardDeckImplCopyWithImpl<_$CardDeckImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CardDeckImplToJson(
      this,
    );
  }
}

abstract class _CardDeck implements CardDeck {
  const factory _CardDeck(
      {required final String id,
      required final String userId,
      required final String name,
      required final String description,
      final String? category,
      final List<FlashCard> cards,
      required final DateTime createdAt,
      final DateTime? updatedAt,
      final bool isActive,
      final int dailyGoal,
      final Map<String, dynamic> settings}) = _$CardDeckImpl;

  factory _CardDeck.fromJson(Map<String, dynamic> json) =
      _$CardDeckImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get name;
  @override
  String get description;
  @override
  String? get category;
  @override
  List<FlashCard> get cards;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  bool get isActive;
  @override
  int get dailyGoal;
  @override
  Map<String, dynamic> get settings;

  /// Create a copy of CardDeck
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardDeckImplCopyWith<_$CardDeckImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CardReview _$CardReviewFromJson(Map<String, dynamic> json) {
  return _CardReview.fromJson(json);
}

/// @nodoc
mixin _$CardReview {
  double get easeFactor => throw _privateConstructorUsedError;
  int get interval => throw _privateConstructorUsedError;
  int get repetitions => throw _privateConstructorUsedError;
  DateTime? get nextReviewDate => throw _privateConstructorUsedError;
  DateTime? get lastReviewedAt => throw _privateConstructorUsedError;
  CardQuality get lastQuality => throw _privateConstructorUsedError;

  /// Serializes this CardReview to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CardReview
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CardReviewCopyWith<CardReview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardReviewCopyWith<$Res> {
  factory $CardReviewCopyWith(
          CardReview value, $Res Function(CardReview) then) =
      _$CardReviewCopyWithImpl<$Res, CardReview>;
  @useResult
  $Res call(
      {double easeFactor,
      int interval,
      int repetitions,
      DateTime? nextReviewDate,
      DateTime? lastReviewedAt,
      CardQuality lastQuality});
}

/// @nodoc
class _$CardReviewCopyWithImpl<$Res, $Val extends CardReview>
    implements $CardReviewCopyWith<$Res> {
  _$CardReviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardReview
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? easeFactor = null,
    Object? interval = null,
    Object? repetitions = null,
    Object? nextReviewDate = freezed,
    Object? lastReviewedAt = freezed,
    Object? lastQuality = null,
  }) {
    return _then(_value.copyWith(
      easeFactor: null == easeFactor
          ? _value.easeFactor
          : easeFactor // ignore: cast_nullable_to_non_nullable
              as double,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as int,
      repetitions: null == repetitions
          ? _value.repetitions
          : repetitions // ignore: cast_nullable_to_non_nullable
              as int,
      nextReviewDate: freezed == nextReviewDate
          ? _value.nextReviewDate
          : nextReviewDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastReviewedAt: freezed == lastReviewedAt
          ? _value.lastReviewedAt
          : lastReviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastQuality: null == lastQuality
          ? _value.lastQuality
          : lastQuality // ignore: cast_nullable_to_non_nullable
              as CardQuality,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CardReviewImplCopyWith<$Res>
    implements $CardReviewCopyWith<$Res> {
  factory _$$CardReviewImplCopyWith(
          _$CardReviewImpl value, $Res Function(_$CardReviewImpl) then) =
      __$$CardReviewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double easeFactor,
      int interval,
      int repetitions,
      DateTime? nextReviewDate,
      DateTime? lastReviewedAt,
      CardQuality lastQuality});
}

/// @nodoc
class __$$CardReviewImplCopyWithImpl<$Res>
    extends _$CardReviewCopyWithImpl<$Res, _$CardReviewImpl>
    implements _$$CardReviewImplCopyWith<$Res> {
  __$$CardReviewImplCopyWithImpl(
      _$CardReviewImpl _value, $Res Function(_$CardReviewImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardReview
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? easeFactor = null,
    Object? interval = null,
    Object? repetitions = null,
    Object? nextReviewDate = freezed,
    Object? lastReviewedAt = freezed,
    Object? lastQuality = null,
  }) {
    return _then(_$CardReviewImpl(
      easeFactor: null == easeFactor
          ? _value.easeFactor
          : easeFactor // ignore: cast_nullable_to_non_nullable
              as double,
      interval: null == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as int,
      repetitions: null == repetitions
          ? _value.repetitions
          : repetitions // ignore: cast_nullable_to_non_nullable
              as int,
      nextReviewDate: freezed == nextReviewDate
          ? _value.nextReviewDate
          : nextReviewDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastReviewedAt: freezed == lastReviewedAt
          ? _value.lastReviewedAt
          : lastReviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastQuality: null == lastQuality
          ? _value.lastQuality
          : lastQuality // ignore: cast_nullable_to_non_nullable
              as CardQuality,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CardReviewImpl implements _CardReview {
  const _$CardReviewImpl(
      {this.easeFactor = 2.5,
      this.interval = 1,
      this.repetitions = 0,
      this.nextReviewDate,
      this.lastReviewedAt,
      this.lastQuality = CardQuality.none});

  factory _$CardReviewImpl.fromJson(Map<String, dynamic> json) =>
      _$$CardReviewImplFromJson(json);

  @override
  @JsonKey()
  final double easeFactor;
  @override
  @JsonKey()
  final int interval;
  @override
  @JsonKey()
  final int repetitions;
  @override
  final DateTime? nextReviewDate;
  @override
  final DateTime? lastReviewedAt;
  @override
  @JsonKey()
  final CardQuality lastQuality;

  @override
  String toString() {
    return 'CardReview(easeFactor: $easeFactor, interval: $interval, repetitions: $repetitions, nextReviewDate: $nextReviewDate, lastReviewedAt: $lastReviewedAt, lastQuality: $lastQuality)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardReviewImpl &&
            (identical(other.easeFactor, easeFactor) ||
                other.easeFactor == easeFactor) &&
            (identical(other.interval, interval) ||
                other.interval == interval) &&
            (identical(other.repetitions, repetitions) ||
                other.repetitions == repetitions) &&
            (identical(other.nextReviewDate, nextReviewDate) ||
                other.nextReviewDate == nextReviewDate) &&
            (identical(other.lastReviewedAt, lastReviewedAt) ||
                other.lastReviewedAt == lastReviewedAt) &&
            (identical(other.lastQuality, lastQuality) ||
                other.lastQuality == lastQuality));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, easeFactor, interval,
      repetitions, nextReviewDate, lastReviewedAt, lastQuality);

  /// Create a copy of CardReview
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardReviewImplCopyWith<_$CardReviewImpl> get copyWith =>
      __$$CardReviewImplCopyWithImpl<_$CardReviewImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CardReviewImplToJson(
      this,
    );
  }
}

abstract class _CardReview implements CardReview {
  const factory _CardReview(
      {final double easeFactor,
      final int interval,
      final int repetitions,
      final DateTime? nextReviewDate,
      final DateTime? lastReviewedAt,
      final CardQuality lastQuality}) = _$CardReviewImpl;

  factory _CardReview.fromJson(Map<String, dynamic> json) =
      _$CardReviewImpl.fromJson;

  @override
  double get easeFactor;
  @override
  int get interval;
  @override
  int get repetitions;
  @override
  DateTime? get nextReviewDate;
  @override
  DateTime? get lastReviewedAt;
  @override
  CardQuality get lastQuality;

  /// Create a copy of CardReview
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardReviewImplCopyWith<_$CardReviewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CardStudySession _$CardStudySessionFromJson(Map<String, dynamic> json) {
  return _CardStudySession.fromJson(json);
}

/// @nodoc
mixin _$CardStudySession {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get deckId => throw _privateConstructorUsedError;
  DateTime get startTime => throw _privateConstructorUsedError;
  DateTime? get endTime => throw _privateConstructorUsedError;
  List<CardReviewResult> get reviews => throw _privateConstructorUsedError;
  int get cardsReviewed => throw _privateConstructorUsedError;
  int get correctAnswers => throw _privateConstructorUsedError;

  /// Serializes this CardStudySession to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CardStudySession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CardStudySessionCopyWith<CardStudySession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardStudySessionCopyWith<$Res> {
  factory $CardStudySessionCopyWith(
          CardStudySession value, $Res Function(CardStudySession) then) =
      _$CardStudySessionCopyWithImpl<$Res, CardStudySession>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String deckId,
      DateTime startTime,
      DateTime? endTime,
      List<CardReviewResult> reviews,
      int cardsReviewed,
      int correctAnswers});
}

/// @nodoc
class _$CardStudySessionCopyWithImpl<$Res, $Val extends CardStudySession>
    implements $CardStudySessionCopyWith<$Res> {
  _$CardStudySessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardStudySession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? deckId = null,
    Object? startTime = null,
    Object? endTime = freezed,
    Object? reviews = null,
    Object? cardsReviewed = null,
    Object? correctAnswers = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      deckId: null == deckId
          ? _value.deckId
          : deckId // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reviews: null == reviews
          ? _value.reviews
          : reviews // ignore: cast_nullable_to_non_nullable
              as List<CardReviewResult>,
      cardsReviewed: null == cardsReviewed
          ? _value.cardsReviewed
          : cardsReviewed // ignore: cast_nullable_to_non_nullable
              as int,
      correctAnswers: null == correctAnswers
          ? _value.correctAnswers
          : correctAnswers // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CardStudySessionImplCopyWith<$Res>
    implements $CardStudySessionCopyWith<$Res> {
  factory _$$CardStudySessionImplCopyWith(_$CardStudySessionImpl value,
          $Res Function(_$CardStudySessionImpl) then) =
      __$$CardStudySessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String deckId,
      DateTime startTime,
      DateTime? endTime,
      List<CardReviewResult> reviews,
      int cardsReviewed,
      int correctAnswers});
}

/// @nodoc
class __$$CardStudySessionImplCopyWithImpl<$Res>
    extends _$CardStudySessionCopyWithImpl<$Res, _$CardStudySessionImpl>
    implements _$$CardStudySessionImplCopyWith<$Res> {
  __$$CardStudySessionImplCopyWithImpl(_$CardStudySessionImpl _value,
      $Res Function(_$CardStudySessionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardStudySession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? deckId = null,
    Object? startTime = null,
    Object? endTime = freezed,
    Object? reviews = null,
    Object? cardsReviewed = null,
    Object? correctAnswers = null,
  }) {
    return _then(_$CardStudySessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      deckId: null == deckId
          ? _value.deckId
          : deckId // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reviews: null == reviews
          ? _value._reviews
          : reviews // ignore: cast_nullable_to_non_nullable
              as List<CardReviewResult>,
      cardsReviewed: null == cardsReviewed
          ? _value.cardsReviewed
          : cardsReviewed // ignore: cast_nullable_to_non_nullable
              as int,
      correctAnswers: null == correctAnswers
          ? _value.correctAnswers
          : correctAnswers // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CardStudySessionImpl implements _CardStudySession {
  const _$CardStudySessionImpl(
      {required this.id,
      required this.userId,
      required this.deckId,
      required this.startTime,
      this.endTime,
      final List<CardReviewResult> reviews = const [],
      this.cardsReviewed = 0,
      this.correctAnswers = 0})
      : _reviews = reviews;

  factory _$CardStudySessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$CardStudySessionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String deckId;
  @override
  final DateTime startTime;
  @override
  final DateTime? endTime;
  final List<CardReviewResult> _reviews;
  @override
  @JsonKey()
  List<CardReviewResult> get reviews {
    if (_reviews is EqualUnmodifiableListView) return _reviews;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reviews);
  }

  @override
  @JsonKey()
  final int cardsReviewed;
  @override
  @JsonKey()
  final int correctAnswers;

  @override
  String toString() {
    return 'CardStudySession(id: $id, userId: $userId, deckId: $deckId, startTime: $startTime, endTime: $endTime, reviews: $reviews, cardsReviewed: $cardsReviewed, correctAnswers: $correctAnswers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardStudySessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.deckId, deckId) || other.deckId == deckId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            const DeepCollectionEquality().equals(other._reviews, _reviews) &&
            (identical(other.cardsReviewed, cardsReviewed) ||
                other.cardsReviewed == cardsReviewed) &&
            (identical(other.correctAnswers, correctAnswers) ||
                other.correctAnswers == correctAnswers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      deckId,
      startTime,
      endTime,
      const DeepCollectionEquality().hash(_reviews),
      cardsReviewed,
      correctAnswers);

  /// Create a copy of CardStudySession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardStudySessionImplCopyWith<_$CardStudySessionImpl> get copyWith =>
      __$$CardStudySessionImplCopyWithImpl<_$CardStudySessionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CardStudySessionImplToJson(
      this,
    );
  }
}

abstract class _CardStudySession implements CardStudySession {
  const factory _CardStudySession(
      {required final String id,
      required final String userId,
      required final String deckId,
      required final DateTime startTime,
      final DateTime? endTime,
      final List<CardReviewResult> reviews,
      final int cardsReviewed,
      final int correctAnswers}) = _$CardStudySessionImpl;

  factory _CardStudySession.fromJson(Map<String, dynamic> json) =
      _$CardStudySessionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get deckId;
  @override
  DateTime get startTime;
  @override
  DateTime? get endTime;
  @override
  List<CardReviewResult> get reviews;
  @override
  int get cardsReviewed;
  @override
  int get correctAnswers;

  /// Create a copy of CardStudySession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardStudySessionImplCopyWith<_$CardStudySessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CardReviewResult _$CardReviewResultFromJson(Map<String, dynamic> json) {
  return _CardReviewResult.fromJson(json);
}

/// @nodoc
mixin _$CardReviewResult {
  String get cardId => throw _privateConstructorUsedError;
  CardQuality get quality => throw _privateConstructorUsedError;
  Duration get responseTime => throw _privateConstructorUsedError;
  DateTime get reviewedAt => throw _privateConstructorUsedError;

  /// Serializes this CardReviewResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CardReviewResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CardReviewResultCopyWith<CardReviewResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardReviewResultCopyWith<$Res> {
  factory $CardReviewResultCopyWith(
          CardReviewResult value, $Res Function(CardReviewResult) then) =
      _$CardReviewResultCopyWithImpl<$Res, CardReviewResult>;
  @useResult
  $Res call(
      {String cardId,
      CardQuality quality,
      Duration responseTime,
      DateTime reviewedAt});
}

/// @nodoc
class _$CardReviewResultCopyWithImpl<$Res, $Val extends CardReviewResult>
    implements $CardReviewResultCopyWith<$Res> {
  _$CardReviewResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardReviewResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardId = null,
    Object? quality = null,
    Object? responseTime = null,
    Object? reviewedAt = null,
  }) {
    return _then(_value.copyWith(
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as CardQuality,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      reviewedAt: null == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CardReviewResultImplCopyWith<$Res>
    implements $CardReviewResultCopyWith<$Res> {
  factory _$$CardReviewResultImplCopyWith(_$CardReviewResultImpl value,
          $Res Function(_$CardReviewResultImpl) then) =
      __$$CardReviewResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String cardId,
      CardQuality quality,
      Duration responseTime,
      DateTime reviewedAt});
}

/// @nodoc
class __$$CardReviewResultImplCopyWithImpl<$Res>
    extends _$CardReviewResultCopyWithImpl<$Res, _$CardReviewResultImpl>
    implements _$$CardReviewResultImplCopyWith<$Res> {
  __$$CardReviewResultImplCopyWithImpl(_$CardReviewResultImpl _value,
      $Res Function(_$CardReviewResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardReviewResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardId = null,
    Object? quality = null,
    Object? responseTime = null,
    Object? reviewedAt = null,
  }) {
    return _then(_$CardReviewResultImpl(
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as CardQuality,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      reviewedAt: null == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CardReviewResultImpl implements _CardReviewResult {
  const _$CardReviewResultImpl(
      {required this.cardId,
      required this.quality,
      required this.responseTime,
      required this.reviewedAt});

  factory _$CardReviewResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$CardReviewResultImplFromJson(json);

  @override
  final String cardId;
  @override
  final CardQuality quality;
  @override
  final Duration responseTime;
  @override
  final DateTime reviewedAt;

  @override
  String toString() {
    return 'CardReviewResult(cardId: $cardId, quality: $quality, responseTime: $responseTime, reviewedAt: $reviewedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardReviewResultImpl &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, cardId, quality, responseTime, reviewedAt);

  /// Create a copy of CardReviewResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardReviewResultImplCopyWith<_$CardReviewResultImpl> get copyWith =>
      __$$CardReviewResultImplCopyWithImpl<_$CardReviewResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CardReviewResultImplToJson(
      this,
    );
  }
}

abstract class _CardReviewResult implements CardReviewResult {
  const factory _CardReviewResult(
      {required final String cardId,
      required final CardQuality quality,
      required final Duration responseTime,
      required final DateTime reviewedAt}) = _$CardReviewResultImpl;

  factory _CardReviewResult.fromJson(Map<String, dynamic> json) =
      _$CardReviewResultImpl.fromJson;

  @override
  String get cardId;
  @override
  CardQuality get quality;
  @override
  Duration get responseTime;
  @override
  DateTime get reviewedAt;

  /// Create a copy of CardReviewResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardReviewResultImplCopyWith<_$CardReviewResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StorageUsage _$StorageUsageFromJson(Map<String, dynamic> json) {
  return _StorageUsage.fromJson(json);
}

/// @nodoc
mixin _$StorageUsage {
  int get currentCount => throw _privateConstructorUsedError;
  int get limit => throw _privateConstructorUsedError;
  int get remainingCount => throw _privateConstructorUsedError;
  double get usagePercentage => throw _privateConstructorUsedError;

  /// Serializes this StorageUsage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StorageUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StorageUsageCopyWith<StorageUsage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StorageUsageCopyWith<$Res> {
  factory $StorageUsageCopyWith(
          StorageUsage value, $Res Function(StorageUsage) then) =
      _$StorageUsageCopyWithImpl<$Res, StorageUsage>;
  @useResult
  $Res call(
      {int currentCount,
      int limit,
      int remainingCount,
      double usagePercentage});
}

/// @nodoc
class _$StorageUsageCopyWithImpl<$Res, $Val extends StorageUsage>
    implements $StorageUsageCopyWith<$Res> {
  _$StorageUsageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StorageUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCount = null,
    Object? limit = null,
    Object? remainingCount = null,
    Object? usagePercentage = null,
  }) {
    return _then(_value.copyWith(
      currentCount: null == currentCount
          ? _value.currentCount
          : currentCount // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      remainingCount: null == remainingCount
          ? _value.remainingCount
          : remainingCount // ignore: cast_nullable_to_non_nullable
              as int,
      usagePercentage: null == usagePercentage
          ? _value.usagePercentage
          : usagePercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StorageUsageImplCopyWith<$Res>
    implements $StorageUsageCopyWith<$Res> {
  factory _$$StorageUsageImplCopyWith(
          _$StorageUsageImpl value, $Res Function(_$StorageUsageImpl) then) =
      __$$StorageUsageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentCount,
      int limit,
      int remainingCount,
      double usagePercentage});
}

/// @nodoc
class __$$StorageUsageImplCopyWithImpl<$Res>
    extends _$StorageUsageCopyWithImpl<$Res, _$StorageUsageImpl>
    implements _$$StorageUsageImplCopyWith<$Res> {
  __$$StorageUsageImplCopyWithImpl(
      _$StorageUsageImpl _value, $Res Function(_$StorageUsageImpl) _then)
      : super(_value, _then);

  /// Create a copy of StorageUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCount = null,
    Object? limit = null,
    Object? remainingCount = null,
    Object? usagePercentage = null,
  }) {
    return _then(_$StorageUsageImpl(
      currentCount: null == currentCount
          ? _value.currentCount
          : currentCount // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      remainingCount: null == remainingCount
          ? _value.remainingCount
          : remainingCount // ignore: cast_nullable_to_non_nullable
              as int,
      usagePercentage: null == usagePercentage
          ? _value.usagePercentage
          : usagePercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StorageUsageImpl implements _StorageUsage {
  const _$StorageUsageImpl(
      {required this.currentCount,
      required this.limit,
      required this.remainingCount,
      required this.usagePercentage});

  factory _$StorageUsageImpl.fromJson(Map<String, dynamic> json) =>
      _$$StorageUsageImplFromJson(json);

  @override
  final int currentCount;
  @override
  final int limit;
  @override
  final int remainingCount;
  @override
  final double usagePercentage;

  @override
  String toString() {
    return 'StorageUsage(currentCount: $currentCount, limit: $limit, remainingCount: $remainingCount, usagePercentage: $usagePercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageUsageImpl &&
            (identical(other.currentCount, currentCount) ||
                other.currentCount == currentCount) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.remainingCount, remainingCount) ||
                other.remainingCount == remainingCount) &&
            (identical(other.usagePercentage, usagePercentage) ||
                other.usagePercentage == usagePercentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, currentCount, limit, remainingCount, usagePercentage);

  /// Create a copy of StorageUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageUsageImplCopyWith<_$StorageUsageImpl> get copyWith =>
      __$$StorageUsageImplCopyWithImpl<_$StorageUsageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StorageUsageImplToJson(
      this,
    );
  }
}

abstract class _StorageUsage implements StorageUsage {
  const factory _StorageUsage(
      {required final int currentCount,
      required final int limit,
      required final int remainingCount,
      required final double usagePercentage}) = _$StorageUsageImpl;

  factory _StorageUsage.fromJson(Map<String, dynamic> json) =
      _$StorageUsageImpl.fromJson;

  @override
  int get currentCount;
  @override
  int get limit;
  @override
  int get remainingCount;
  @override
  double get usagePercentage;

  /// Create a copy of StorageUsage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageUsageImplCopyWith<_$StorageUsageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
