import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/constants/game_constants.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    String? displayName,
    String? photoURL,
    required DateTime createdAt,
    required UserStats stats,
    required UserPreferences preferences,
    DateTime? lastLoginAt,
    bool? isGuest,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    @Default(0) int totalGamesPlayed,
    @Default(0) int currentStreak,
    @Default(0) int maxStreak,
    @Default({}) Map<GameType, GameStats> gameStats,
    @Default(0) int totalXP,
    @Default(1) int level,
    DateTime? lastGamePlayedAt,
    @Default(0) double averageAccuracy,
    @Default(Duration.zero) Duration totalPlayTime,
  }) = _UserStats;

  factory UserStats.fromJson(Map<String, dynamic> json) => _$UserStatsFromJson(json);
}

@freezed
class GameStats with _$GameStats {
  const factory GameStats({
    @Default(0) int gamesPlayed,
    @Default(0) int bestScore,
    @Default(0.0) double averageAccuracy,
    @Default(1) int currentLevel,
    @Default(Duration.zero) Duration totalPlayTime,
    DateTime? lastPlayedAt,
    @Default(0) int totalXP,
    Map<String, dynamic>? gameSpecificStats,
  }) = _GameStats;

  factory GameStats.fromJson(Map<String, dynamic> json) => _$GameStatsFromJson(json);
}

@freezed
class UserPreferences with _$UserPreferences {
  const factory UserPreferences({
    @Default('ko') String language,
    @Default(true) bool soundEnabled,
    @Default(true) bool musicEnabled,
    @Default(true) bool notificationsEnabled,
    @Default(true) bool dailyReminderEnabled,
    @Default(TimeOfDay(hour: 19, minute: 0)) TimeOfDay dailyReminderTime,
    @Default(false) bool isDarkMode,
    @Default(1.0) double gameSpeed,
    @Default(true) bool hapticFeedbackEnabled,
  }) = _UserPreferences;

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
}

@freezed
class TimeOfDay with _$TimeOfDay {
  const factory TimeOfDay({
    required int hour,
    required int minute,
  }) = _TimeOfDay;

  factory TimeOfDay.fromJson(Map<String, dynamic> json) => _$TimeOfDayFromJson(json);
}