// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  String? get photoURL => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  UserStats get stats => throw _privateConstructorUsedError;
  UserPreferences get preferences => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  bool? get isGuest => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {String id,
      String email,
      String? displayName,
      String? photoURL,
      DateTime createdAt,
      UserStats stats,
      UserPreferences preferences,
      DateTime? lastLoginAt,
      bool? isGuest});

  $UserStatsCopyWith<$Res> get stats;
  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? displayName = freezed,
    Object? photoURL = freezed,
    Object? createdAt = null,
    Object? stats = null,
    Object? preferences = null,
    Object? lastLoginAt = freezed,
    Object? isGuest = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoURL: freezed == photoURL
          ? _value.photoURL
          : photoURL // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      stats: null == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as UserStats,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isGuest: freezed == isGuest
          ? _value.isGuest
          : isGuest // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserStatsCopyWith<$Res> get stats {
    return $UserStatsCopyWith<$Res>(_value.stats, (value) {
      return _then(_value.copyWith(stats: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserPreferencesCopyWith<$Res> get preferences {
    return $UserPreferencesCopyWith<$Res>(_value.preferences, (value) {
      return _then(_value.copyWith(preferences: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String? displayName,
      String? photoURL,
      DateTime createdAt,
      UserStats stats,
      UserPreferences preferences,
      DateTime? lastLoginAt,
      bool? isGuest});

  @override
  $UserStatsCopyWith<$Res> get stats;
  @override
  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? displayName = freezed,
    Object? photoURL = freezed,
    Object? createdAt = null,
    Object? stats = null,
    Object? preferences = null,
    Object? lastLoginAt = freezed,
    Object? isGuest = freezed,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoURL: freezed == photoURL
          ? _value.photoURL
          : photoURL // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      stats: null == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as UserStats,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isGuest: freezed == isGuest
          ? _value.isGuest
          : isGuest // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {required this.id,
      required this.email,
      this.displayName,
      this.photoURL,
      required this.createdAt,
      required this.stats,
      required this.preferences,
      this.lastLoginAt,
      this.isGuest});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String? displayName;
  @override
  final String? photoURL;
  @override
  final DateTime createdAt;
  @override
  final UserStats stats;
  @override
  final UserPreferences preferences;
  @override
  final DateTime? lastLoginAt;
  @override
  final bool? isGuest;

  @override
  String toString() {
    return 'User(id: $id, email: $email, displayName: $displayName, photoURL: $photoURL, createdAt: $createdAt, stats: $stats, preferences: $preferences, lastLoginAt: $lastLoginAt, isGuest: $isGuest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.photoURL, photoURL) ||
                other.photoURL == photoURL) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.preferences, preferences) ||
                other.preferences == preferences) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.isGuest, isGuest) || other.isGuest == isGuest));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, email, displayName, photoURL,
      createdAt, stats, preferences, lastLoginAt, isGuest);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {required final String id,
      required final String email,
      final String? displayName,
      final String? photoURL,
      required final DateTime createdAt,
      required final UserStats stats,
      required final UserPreferences preferences,
      final DateTime? lastLoginAt,
      final bool? isGuest}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String? get displayName;
  @override
  String? get photoURL;
  @override
  DateTime get createdAt;
  @override
  UserStats get stats;
  @override
  UserPreferences get preferences;
  @override
  DateTime? get lastLoginAt;
  @override
  bool? get isGuest;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserStats _$UserStatsFromJson(Map<String, dynamic> json) {
  return _UserStats.fromJson(json);
}

/// @nodoc
mixin _$UserStats {
  int get totalGamesPlayed => throw _privateConstructorUsedError;
  int get currentStreak => throw _privateConstructorUsedError;
  int get maxStreak => throw _privateConstructorUsedError;
  Map<GameType, GameStats> get gameStats => throw _privateConstructorUsedError;
  int get totalXP => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  DateTime? get lastGamePlayedAt => throw _privateConstructorUsedError;
  double get averageAccuracy => throw _privateConstructorUsedError;
  Duration get totalPlayTime => throw _privateConstructorUsedError;

  /// Serializes this UserStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserStatsCopyWith<UserStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserStatsCopyWith<$Res> {
  factory $UserStatsCopyWith(UserStats value, $Res Function(UserStats) then) =
      _$UserStatsCopyWithImpl<$Res, UserStats>;
  @useResult
  $Res call(
      {int totalGamesPlayed,
      int currentStreak,
      int maxStreak,
      Map<GameType, GameStats> gameStats,
      int totalXP,
      int level,
      DateTime? lastGamePlayedAt,
      double averageAccuracy,
      Duration totalPlayTime});
}

/// @nodoc
class _$UserStatsCopyWithImpl<$Res, $Val extends UserStats>
    implements $UserStatsCopyWith<$Res> {
  _$UserStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalGamesPlayed = null,
    Object? currentStreak = null,
    Object? maxStreak = null,
    Object? gameStats = null,
    Object? totalXP = null,
    Object? level = null,
    Object? lastGamePlayedAt = freezed,
    Object? averageAccuracy = null,
    Object? totalPlayTime = null,
  }) {
    return _then(_value.copyWith(
      totalGamesPlayed: null == totalGamesPlayed
          ? _value.totalGamesPlayed
          : totalGamesPlayed // ignore: cast_nullable_to_non_nullable
              as int,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      maxStreak: null == maxStreak
          ? _value.maxStreak
          : maxStreak // ignore: cast_nullable_to_non_nullable
              as int,
      gameStats: null == gameStats
          ? _value.gameStats
          : gameStats // ignore: cast_nullable_to_non_nullable
              as Map<GameType, GameStats>,
      totalXP: null == totalXP
          ? _value.totalXP
          : totalXP // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      lastGamePlayedAt: freezed == lastGamePlayedAt
          ? _value.lastGamePlayedAt
          : lastGamePlayedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      averageAccuracy: null == averageAccuracy
          ? _value.averageAccuracy
          : averageAccuracy // ignore: cast_nullable_to_non_nullable
              as double,
      totalPlayTime: null == totalPlayTime
          ? _value.totalPlayTime
          : totalPlayTime // ignore: cast_nullable_to_non_nullable
              as Duration,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserStatsImplCopyWith<$Res>
    implements $UserStatsCopyWith<$Res> {
  factory _$$UserStatsImplCopyWith(
          _$UserStatsImpl value, $Res Function(_$UserStatsImpl) then) =
      __$$UserStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalGamesPlayed,
      int currentStreak,
      int maxStreak,
      Map<GameType, GameStats> gameStats,
      int totalXP,
      int level,
      DateTime? lastGamePlayedAt,
      double averageAccuracy,
      Duration totalPlayTime});
}

/// @nodoc
class __$$UserStatsImplCopyWithImpl<$Res>
    extends _$UserStatsCopyWithImpl<$Res, _$UserStatsImpl>
    implements _$$UserStatsImplCopyWith<$Res> {
  __$$UserStatsImplCopyWithImpl(
      _$UserStatsImpl _value, $Res Function(_$UserStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalGamesPlayed = null,
    Object? currentStreak = null,
    Object? maxStreak = null,
    Object? gameStats = null,
    Object? totalXP = null,
    Object? level = null,
    Object? lastGamePlayedAt = freezed,
    Object? averageAccuracy = null,
    Object? totalPlayTime = null,
  }) {
    return _then(_$UserStatsImpl(
      totalGamesPlayed: null == totalGamesPlayed
          ? _value.totalGamesPlayed
          : totalGamesPlayed // ignore: cast_nullable_to_non_nullable
              as int,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      maxStreak: null == maxStreak
          ? _value.maxStreak
          : maxStreak // ignore: cast_nullable_to_non_nullable
              as int,
      gameStats: null == gameStats
          ? _value._gameStats
          : gameStats // ignore: cast_nullable_to_non_nullable
              as Map<GameType, GameStats>,
      totalXP: null == totalXP
          ? _value.totalXP
          : totalXP // ignore: cast_nullable_to_non_nullable
              as int,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      lastGamePlayedAt: freezed == lastGamePlayedAt
          ? _value.lastGamePlayedAt
          : lastGamePlayedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      averageAccuracy: null == averageAccuracy
          ? _value.averageAccuracy
          : averageAccuracy // ignore: cast_nullable_to_non_nullable
              as double,
      totalPlayTime: null == totalPlayTime
          ? _value.totalPlayTime
          : totalPlayTime // ignore: cast_nullable_to_non_nullable
              as Duration,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserStatsImpl implements _UserStats {
  const _$UserStatsImpl(
      {this.totalGamesPlayed = 0,
      this.currentStreak = 0,
      this.maxStreak = 0,
      final Map<GameType, GameStats> gameStats = const {},
      this.totalXP = 0,
      this.level = 1,
      this.lastGamePlayedAt,
      this.averageAccuracy = 0,
      this.totalPlayTime = Duration.zero})
      : _gameStats = gameStats;

  factory _$UserStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserStatsImplFromJson(json);

  @override
  @JsonKey()
  final int totalGamesPlayed;
  @override
  @JsonKey()
  final int currentStreak;
  @override
  @JsonKey()
  final int maxStreak;
  final Map<GameType, GameStats> _gameStats;
  @override
  @JsonKey()
  Map<GameType, GameStats> get gameStats {
    if (_gameStats is EqualUnmodifiableMapView) return _gameStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_gameStats);
  }

  @override
  @JsonKey()
  final int totalXP;
  @override
  @JsonKey()
  final int level;
  @override
  final DateTime? lastGamePlayedAt;
  @override
  @JsonKey()
  final double averageAccuracy;
  @override
  @JsonKey()
  final Duration totalPlayTime;

  @override
  String toString() {
    return 'UserStats(totalGamesPlayed: $totalGamesPlayed, currentStreak: $currentStreak, maxStreak: $maxStreak, gameStats: $gameStats, totalXP: $totalXP, level: $level, lastGamePlayedAt: $lastGamePlayedAt, averageAccuracy: $averageAccuracy, totalPlayTime: $totalPlayTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserStatsImpl &&
            (identical(other.totalGamesPlayed, totalGamesPlayed) ||
                other.totalGamesPlayed == totalGamesPlayed) &&
            (identical(other.currentStreak, currentStreak) ||
                other.currentStreak == currentStreak) &&
            (identical(other.maxStreak, maxStreak) ||
                other.maxStreak == maxStreak) &&
            const DeepCollectionEquality()
                .equals(other._gameStats, _gameStats) &&
            (identical(other.totalXP, totalXP) || other.totalXP == totalXP) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.lastGamePlayedAt, lastGamePlayedAt) ||
                other.lastGamePlayedAt == lastGamePlayedAt) &&
            (identical(other.averageAccuracy, averageAccuracy) ||
                other.averageAccuracy == averageAccuracy) &&
            (identical(other.totalPlayTime, totalPlayTime) ||
                other.totalPlayTime == totalPlayTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalGamesPlayed,
      currentStreak,
      maxStreak,
      const DeepCollectionEquality().hash(_gameStats),
      totalXP,
      level,
      lastGamePlayedAt,
      averageAccuracy,
      totalPlayTime);

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserStatsImplCopyWith<_$UserStatsImpl> get copyWith =>
      __$$UserStatsImplCopyWithImpl<_$UserStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserStatsImplToJson(
      this,
    );
  }
}

abstract class _UserStats implements UserStats {
  const factory _UserStats(
      {final int totalGamesPlayed,
      final int currentStreak,
      final int maxStreak,
      final Map<GameType, GameStats> gameStats,
      final int totalXP,
      final int level,
      final DateTime? lastGamePlayedAt,
      final double averageAccuracy,
      final Duration totalPlayTime}) = _$UserStatsImpl;

  factory _UserStats.fromJson(Map<String, dynamic> json) =
      _$UserStatsImpl.fromJson;

  @override
  int get totalGamesPlayed;
  @override
  int get currentStreak;
  @override
  int get maxStreak;
  @override
  Map<GameType, GameStats> get gameStats;
  @override
  int get totalXP;
  @override
  int get level;
  @override
  DateTime? get lastGamePlayedAt;
  @override
  double get averageAccuracy;
  @override
  Duration get totalPlayTime;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserStatsImplCopyWith<_$UserStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GameStats _$GameStatsFromJson(Map<String, dynamic> json) {
  return _GameStats.fromJson(json);
}

/// @nodoc
mixin _$GameStats {
  int get gamesPlayed => throw _privateConstructorUsedError;
  int get bestScore => throw _privateConstructorUsedError;
  double get averageAccuracy => throw _privateConstructorUsedError;
  int get currentLevel => throw _privateConstructorUsedError;
  Duration get totalPlayTime => throw _privateConstructorUsedError;
  DateTime? get lastPlayedAt => throw _privateConstructorUsedError;
  int get totalXP => throw _privateConstructorUsedError;
  Map<String, dynamic>? get gameSpecificStats =>
      throw _privateConstructorUsedError;

  /// Serializes this GameStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GameStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GameStatsCopyWith<GameStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GameStatsCopyWith<$Res> {
  factory $GameStatsCopyWith(GameStats value, $Res Function(GameStats) then) =
      _$GameStatsCopyWithImpl<$Res, GameStats>;
  @useResult
  $Res call(
      {int gamesPlayed,
      int bestScore,
      double averageAccuracy,
      int currentLevel,
      Duration totalPlayTime,
      DateTime? lastPlayedAt,
      int totalXP,
      Map<String, dynamic>? gameSpecificStats});
}

/// @nodoc
class _$GameStatsCopyWithImpl<$Res, $Val extends GameStats>
    implements $GameStatsCopyWith<$Res> {
  _$GameStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GameStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gamesPlayed = null,
    Object? bestScore = null,
    Object? averageAccuracy = null,
    Object? currentLevel = null,
    Object? totalPlayTime = null,
    Object? lastPlayedAt = freezed,
    Object? totalXP = null,
    Object? gameSpecificStats = freezed,
  }) {
    return _then(_value.copyWith(
      gamesPlayed: null == gamesPlayed
          ? _value.gamesPlayed
          : gamesPlayed // ignore: cast_nullable_to_non_nullable
              as int,
      bestScore: null == bestScore
          ? _value.bestScore
          : bestScore // ignore: cast_nullable_to_non_nullable
              as int,
      averageAccuracy: null == averageAccuracy
          ? _value.averageAccuracy
          : averageAccuracy // ignore: cast_nullable_to_non_nullable
              as double,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      totalPlayTime: null == totalPlayTime
          ? _value.totalPlayTime
          : totalPlayTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      lastPlayedAt: freezed == lastPlayedAt
          ? _value.lastPlayedAt
          : lastPlayedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalXP: null == totalXP
          ? _value.totalXP
          : totalXP // ignore: cast_nullable_to_non_nullable
              as int,
      gameSpecificStats: freezed == gameSpecificStats
          ? _value.gameSpecificStats
          : gameSpecificStats // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GameStatsImplCopyWith<$Res>
    implements $GameStatsCopyWith<$Res> {
  factory _$$GameStatsImplCopyWith(
          _$GameStatsImpl value, $Res Function(_$GameStatsImpl) then) =
      __$$GameStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int gamesPlayed,
      int bestScore,
      double averageAccuracy,
      int currentLevel,
      Duration totalPlayTime,
      DateTime? lastPlayedAt,
      int totalXP,
      Map<String, dynamic>? gameSpecificStats});
}

/// @nodoc
class __$$GameStatsImplCopyWithImpl<$Res>
    extends _$GameStatsCopyWithImpl<$Res, _$GameStatsImpl>
    implements _$$GameStatsImplCopyWith<$Res> {
  __$$GameStatsImplCopyWithImpl(
      _$GameStatsImpl _value, $Res Function(_$GameStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GameStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gamesPlayed = null,
    Object? bestScore = null,
    Object? averageAccuracy = null,
    Object? currentLevel = null,
    Object? totalPlayTime = null,
    Object? lastPlayedAt = freezed,
    Object? totalXP = null,
    Object? gameSpecificStats = freezed,
  }) {
    return _then(_$GameStatsImpl(
      gamesPlayed: null == gamesPlayed
          ? _value.gamesPlayed
          : gamesPlayed // ignore: cast_nullable_to_non_nullable
              as int,
      bestScore: null == bestScore
          ? _value.bestScore
          : bestScore // ignore: cast_nullable_to_non_nullable
              as int,
      averageAccuracy: null == averageAccuracy
          ? _value.averageAccuracy
          : averageAccuracy // ignore: cast_nullable_to_non_nullable
              as double,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      totalPlayTime: null == totalPlayTime
          ? _value.totalPlayTime
          : totalPlayTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      lastPlayedAt: freezed == lastPlayedAt
          ? _value.lastPlayedAt
          : lastPlayedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalXP: null == totalXP
          ? _value.totalXP
          : totalXP // ignore: cast_nullable_to_non_nullable
              as int,
      gameSpecificStats: freezed == gameSpecificStats
          ? _value._gameSpecificStats
          : gameSpecificStats // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GameStatsImpl implements _GameStats {
  const _$GameStatsImpl(
      {this.gamesPlayed = 0,
      this.bestScore = 0,
      this.averageAccuracy = 0.0,
      this.currentLevel = 1,
      this.totalPlayTime = Duration.zero,
      this.lastPlayedAt,
      this.totalXP = 0,
      final Map<String, dynamic>? gameSpecificStats})
      : _gameSpecificStats = gameSpecificStats;

  factory _$GameStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GameStatsImplFromJson(json);

  @override
  @JsonKey()
  final int gamesPlayed;
  @override
  @JsonKey()
  final int bestScore;
  @override
  @JsonKey()
  final double averageAccuracy;
  @override
  @JsonKey()
  final int currentLevel;
  @override
  @JsonKey()
  final Duration totalPlayTime;
  @override
  final DateTime? lastPlayedAt;
  @override
  @JsonKey()
  final int totalXP;
  final Map<String, dynamic>? _gameSpecificStats;
  @override
  Map<String, dynamic>? get gameSpecificStats {
    final value = _gameSpecificStats;
    if (value == null) return null;
    if (_gameSpecificStats is EqualUnmodifiableMapView)
      return _gameSpecificStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'GameStats(gamesPlayed: $gamesPlayed, bestScore: $bestScore, averageAccuracy: $averageAccuracy, currentLevel: $currentLevel, totalPlayTime: $totalPlayTime, lastPlayedAt: $lastPlayedAt, totalXP: $totalXP, gameSpecificStats: $gameSpecificStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GameStatsImpl &&
            (identical(other.gamesPlayed, gamesPlayed) ||
                other.gamesPlayed == gamesPlayed) &&
            (identical(other.bestScore, bestScore) ||
                other.bestScore == bestScore) &&
            (identical(other.averageAccuracy, averageAccuracy) ||
                other.averageAccuracy == averageAccuracy) &&
            (identical(other.currentLevel, currentLevel) ||
                other.currentLevel == currentLevel) &&
            (identical(other.totalPlayTime, totalPlayTime) ||
                other.totalPlayTime == totalPlayTime) &&
            (identical(other.lastPlayedAt, lastPlayedAt) ||
                other.lastPlayedAt == lastPlayedAt) &&
            (identical(other.totalXP, totalXP) || other.totalXP == totalXP) &&
            const DeepCollectionEquality()
                .equals(other._gameSpecificStats, _gameSpecificStats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gamesPlayed,
      bestScore,
      averageAccuracy,
      currentLevel,
      totalPlayTime,
      lastPlayedAt,
      totalXP,
      const DeepCollectionEquality().hash(_gameSpecificStats));

  /// Create a copy of GameStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GameStatsImplCopyWith<_$GameStatsImpl> get copyWith =>
      __$$GameStatsImplCopyWithImpl<_$GameStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GameStatsImplToJson(
      this,
    );
  }
}

abstract class _GameStats implements GameStats {
  const factory _GameStats(
      {final int gamesPlayed,
      final int bestScore,
      final double averageAccuracy,
      final int currentLevel,
      final Duration totalPlayTime,
      final DateTime? lastPlayedAt,
      final int totalXP,
      final Map<String, dynamic>? gameSpecificStats}) = _$GameStatsImpl;

  factory _GameStats.fromJson(Map<String, dynamic> json) =
      _$GameStatsImpl.fromJson;

  @override
  int get gamesPlayed;
  @override
  int get bestScore;
  @override
  double get averageAccuracy;
  @override
  int get currentLevel;
  @override
  Duration get totalPlayTime;
  @override
  DateTime? get lastPlayedAt;
  @override
  int get totalXP;
  @override
  Map<String, dynamic>? get gameSpecificStats;

  /// Create a copy of GameStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GameStatsImplCopyWith<_$GameStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) {
  return _UserPreferences.fromJson(json);
}

/// @nodoc
mixin _$UserPreferences {
  String get language => throw _privateConstructorUsedError;
  bool get soundEnabled => throw _privateConstructorUsedError;
  bool get musicEnabled => throw _privateConstructorUsedError;
  bool get notificationsEnabled => throw _privateConstructorUsedError;
  bool get dailyReminderEnabled => throw _privateConstructorUsedError;
  TimeOfDay get dailyReminderTime => throw _privateConstructorUsedError;
  bool get isDarkMode => throw _privateConstructorUsedError;
  double get gameSpeed => throw _privateConstructorUsedError;
  bool get hapticFeedbackEnabled => throw _privateConstructorUsedError;

  /// Serializes this UserPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserPreferencesCopyWith<UserPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPreferencesCopyWith<$Res> {
  factory $UserPreferencesCopyWith(
          UserPreferences value, $Res Function(UserPreferences) then) =
      _$UserPreferencesCopyWithImpl<$Res, UserPreferences>;
  @useResult
  $Res call(
      {String language,
      bool soundEnabled,
      bool musicEnabled,
      bool notificationsEnabled,
      bool dailyReminderEnabled,
      TimeOfDay dailyReminderTime,
      bool isDarkMode,
      double gameSpeed,
      bool hapticFeedbackEnabled});

  $TimeOfDayCopyWith<$Res> get dailyReminderTime;
}

/// @nodoc
class _$UserPreferencesCopyWithImpl<$Res, $Val extends UserPreferences>
    implements $UserPreferencesCopyWith<$Res> {
  _$UserPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? language = null,
    Object? soundEnabled = null,
    Object? musicEnabled = null,
    Object? notificationsEnabled = null,
    Object? dailyReminderEnabled = null,
    Object? dailyReminderTime = null,
    Object? isDarkMode = null,
    Object? gameSpeed = null,
    Object? hapticFeedbackEnabled = null,
  }) {
    return _then(_value.copyWith(
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      soundEnabled: null == soundEnabled
          ? _value.soundEnabled
          : soundEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      musicEnabled: null == musicEnabled
          ? _value.musicEnabled
          : musicEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyReminderEnabled: null == dailyReminderEnabled
          ? _value.dailyReminderEnabled
          : dailyReminderEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyReminderTime: null == dailyReminderTime
          ? _value.dailyReminderTime
          : dailyReminderTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      isDarkMode: null == isDarkMode
          ? _value.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool,
      gameSpeed: null == gameSpeed
          ? _value.gameSpeed
          : gameSpeed // ignore: cast_nullable_to_non_nullable
              as double,
      hapticFeedbackEnabled: null == hapticFeedbackEnabled
          ? _value.hapticFeedbackEnabled
          : hapticFeedbackEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TimeOfDayCopyWith<$Res> get dailyReminderTime {
    return $TimeOfDayCopyWith<$Res>(_value.dailyReminderTime, (value) {
      return _then(_value.copyWith(dailyReminderTime: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserPreferencesImplCopyWith<$Res>
    implements $UserPreferencesCopyWith<$Res> {
  factory _$$UserPreferencesImplCopyWith(_$UserPreferencesImpl value,
          $Res Function(_$UserPreferencesImpl) then) =
      __$$UserPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String language,
      bool soundEnabled,
      bool musicEnabled,
      bool notificationsEnabled,
      bool dailyReminderEnabled,
      TimeOfDay dailyReminderTime,
      bool isDarkMode,
      double gameSpeed,
      bool hapticFeedbackEnabled});

  @override
  $TimeOfDayCopyWith<$Res> get dailyReminderTime;
}

/// @nodoc
class __$$UserPreferencesImplCopyWithImpl<$Res>
    extends _$UserPreferencesCopyWithImpl<$Res, _$UserPreferencesImpl>
    implements _$$UserPreferencesImplCopyWith<$Res> {
  __$$UserPreferencesImplCopyWithImpl(
      _$UserPreferencesImpl _value, $Res Function(_$UserPreferencesImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? language = null,
    Object? soundEnabled = null,
    Object? musicEnabled = null,
    Object? notificationsEnabled = null,
    Object? dailyReminderEnabled = null,
    Object? dailyReminderTime = null,
    Object? isDarkMode = null,
    Object? gameSpeed = null,
    Object? hapticFeedbackEnabled = null,
  }) {
    return _then(_$UserPreferencesImpl(
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      soundEnabled: null == soundEnabled
          ? _value.soundEnabled
          : soundEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      musicEnabled: null == musicEnabled
          ? _value.musicEnabled
          : musicEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyReminderEnabled: null == dailyReminderEnabled
          ? _value.dailyReminderEnabled
          : dailyReminderEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyReminderTime: null == dailyReminderTime
          ? _value.dailyReminderTime
          : dailyReminderTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      isDarkMode: null == isDarkMode
          ? _value.isDarkMode
          : isDarkMode // ignore: cast_nullable_to_non_nullable
              as bool,
      gameSpeed: null == gameSpeed
          ? _value.gameSpeed
          : gameSpeed // ignore: cast_nullable_to_non_nullable
              as double,
      hapticFeedbackEnabled: null == hapticFeedbackEnabled
          ? _value.hapticFeedbackEnabled
          : hapticFeedbackEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserPreferencesImpl implements _UserPreferences {
  const _$UserPreferencesImpl(
      {this.language = 'ko',
      this.soundEnabled = true,
      this.musicEnabled = true,
      this.notificationsEnabled = true,
      this.dailyReminderEnabled = true,
      this.dailyReminderTime = const TimeOfDay(hour: 19, minute: 0),
      this.isDarkMode = false,
      this.gameSpeed = 1.0,
      this.hapticFeedbackEnabled = true});

  factory _$UserPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey()
  final bool soundEnabled;
  @override
  @JsonKey()
  final bool musicEnabled;
  @override
  @JsonKey()
  final bool notificationsEnabled;
  @override
  @JsonKey()
  final bool dailyReminderEnabled;
  @override
  @JsonKey()
  final TimeOfDay dailyReminderTime;
  @override
  @JsonKey()
  final bool isDarkMode;
  @override
  @JsonKey()
  final double gameSpeed;
  @override
  @JsonKey()
  final bool hapticFeedbackEnabled;

  @override
  String toString() {
    return 'UserPreferences(language: $language, soundEnabled: $soundEnabled, musicEnabled: $musicEnabled, notificationsEnabled: $notificationsEnabled, dailyReminderEnabled: $dailyReminderEnabled, dailyReminderTime: $dailyReminderTime, isDarkMode: $isDarkMode, gameSpeed: $gameSpeed, hapticFeedbackEnabled: $hapticFeedbackEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserPreferencesImpl &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.soundEnabled, soundEnabled) ||
                other.soundEnabled == soundEnabled) &&
            (identical(other.musicEnabled, musicEnabled) ||
                other.musicEnabled == musicEnabled) &&
            (identical(other.notificationsEnabled, notificationsEnabled) ||
                other.notificationsEnabled == notificationsEnabled) &&
            (identical(other.dailyReminderEnabled, dailyReminderEnabled) ||
                other.dailyReminderEnabled == dailyReminderEnabled) &&
            (identical(other.dailyReminderTime, dailyReminderTime) ||
                other.dailyReminderTime == dailyReminderTime) &&
            (identical(other.isDarkMode, isDarkMode) ||
                other.isDarkMode == isDarkMode) &&
            (identical(other.gameSpeed, gameSpeed) ||
                other.gameSpeed == gameSpeed) &&
            (identical(other.hapticFeedbackEnabled, hapticFeedbackEnabled) ||
                other.hapticFeedbackEnabled == hapticFeedbackEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      language,
      soundEnabled,
      musicEnabled,
      notificationsEnabled,
      dailyReminderEnabled,
      dailyReminderTime,
      isDarkMode,
      gameSpeed,
      hapticFeedbackEnabled);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      __$$UserPreferencesImplCopyWithImpl<_$UserPreferencesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserPreferencesImplToJson(
      this,
    );
  }
}

abstract class _UserPreferences implements UserPreferences {
  const factory _UserPreferences(
      {final String language,
      final bool soundEnabled,
      final bool musicEnabled,
      final bool notificationsEnabled,
      final bool dailyReminderEnabled,
      final TimeOfDay dailyReminderTime,
      final bool isDarkMode,
      final double gameSpeed,
      final bool hapticFeedbackEnabled}) = _$UserPreferencesImpl;

  factory _UserPreferences.fromJson(Map<String, dynamic> json) =
      _$UserPreferencesImpl.fromJson;

  @override
  String get language;
  @override
  bool get soundEnabled;
  @override
  bool get musicEnabled;
  @override
  bool get notificationsEnabled;
  @override
  bool get dailyReminderEnabled;
  @override
  TimeOfDay get dailyReminderTime;
  @override
  bool get isDarkMode;
  @override
  double get gameSpeed;
  @override
  bool get hapticFeedbackEnabled;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimeOfDay _$TimeOfDayFromJson(Map<String, dynamic> json) {
  return _TimeOfDay.fromJson(json);
}

/// @nodoc
mixin _$TimeOfDay {
  int get hour => throw _privateConstructorUsedError;
  int get minute => throw _privateConstructorUsedError;

  /// Serializes this TimeOfDay to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimeOfDay
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimeOfDayCopyWith<TimeOfDay> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimeOfDayCopyWith<$Res> {
  factory $TimeOfDayCopyWith(TimeOfDay value, $Res Function(TimeOfDay) then) =
      _$TimeOfDayCopyWithImpl<$Res, TimeOfDay>;
  @useResult
  $Res call({int hour, int minute});
}

/// @nodoc
class _$TimeOfDayCopyWithImpl<$Res, $Val extends TimeOfDay>
    implements $TimeOfDayCopyWith<$Res> {
  _$TimeOfDayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimeOfDay
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hour = null,
    Object? minute = null,
  }) {
    return _then(_value.copyWith(
      hour: null == hour
          ? _value.hour
          : hour // ignore: cast_nullable_to_non_nullable
              as int,
      minute: null == minute
          ? _value.minute
          : minute // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimeOfDayImplCopyWith<$Res>
    implements $TimeOfDayCopyWith<$Res> {
  factory _$$TimeOfDayImplCopyWith(
          _$TimeOfDayImpl value, $Res Function(_$TimeOfDayImpl) then) =
      __$$TimeOfDayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int hour, int minute});
}

/// @nodoc
class __$$TimeOfDayImplCopyWithImpl<$Res>
    extends _$TimeOfDayCopyWithImpl<$Res, _$TimeOfDayImpl>
    implements _$$TimeOfDayImplCopyWith<$Res> {
  __$$TimeOfDayImplCopyWithImpl(
      _$TimeOfDayImpl _value, $Res Function(_$TimeOfDayImpl) _then)
      : super(_value, _then);

  /// Create a copy of TimeOfDay
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hour = null,
    Object? minute = null,
  }) {
    return _then(_$TimeOfDayImpl(
      hour: null == hour
          ? _value.hour
          : hour // ignore: cast_nullable_to_non_nullable
              as int,
      minute: null == minute
          ? _value.minute
          : minute // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimeOfDayImpl implements _TimeOfDay {
  const _$TimeOfDayImpl({required this.hour, required this.minute});

  factory _$TimeOfDayImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimeOfDayImplFromJson(json);

  @override
  final int hour;
  @override
  final int minute;

  @override
  String toString() {
    return 'TimeOfDay(hour: $hour, minute: $minute)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeOfDayImpl &&
            (identical(other.hour, hour) || other.hour == hour) &&
            (identical(other.minute, minute) || other.minute == minute));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, hour, minute);

  /// Create a copy of TimeOfDay
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeOfDayImplCopyWith<_$TimeOfDayImpl> get copyWith =>
      __$$TimeOfDayImplCopyWithImpl<_$TimeOfDayImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimeOfDayImplToJson(
      this,
    );
  }
}

abstract class _TimeOfDay implements TimeOfDay {
  const factory _TimeOfDay(
      {required final int hour, required final int minute}) = _$TimeOfDayImpl;

  factory _TimeOfDay.fromJson(Map<String, dynamic> json) =
      _$TimeOfDayImpl.fromJson;

  @override
  int get hour;
  @override
  int get minute;

  /// Create a copy of TimeOfDay
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeOfDayImplCopyWith<_$TimeOfDayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
