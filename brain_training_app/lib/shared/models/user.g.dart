// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      stats: UserStats.fromJson(json['stats'] as Map<String, dynamic>),
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      isGuest: json['isGuest'] as bool?,
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoURL': instance.photoURL,
      'createdAt': instance.createdAt.toIso8601String(),
      'stats': instance.stats,
      'preferences': instance.preferences,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'isGuest': instance.isGuest,
    };

_$UserStatsImpl _$$UserStatsImplFromJson(Map<String, dynamic> json) =>
    _$UserStatsImpl(
      totalGamesPlayed: (json['totalGamesPlayed'] as num?)?.toInt() ?? 0,
      currentStreak: (json['currentStreak'] as num?)?.toInt() ?? 0,
      maxStreak: (json['maxStreak'] as num?)?.toInt() ?? 0,
      gameStats: (json['gameStats'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry($enumDecode(_$GameTypeEnumMap, k),
                GameStats.fromJson(e as Map<String, dynamic>)),
          ) ??
          const {},
      totalXP: (json['totalXP'] as num?)?.toInt() ?? 0,
      level: (json['level'] as num?)?.toInt() ?? 1,
      lastGamePlayedAt: json['lastGamePlayedAt'] == null
          ? null
          : DateTime.parse(json['lastGamePlayedAt'] as String),
      averageAccuracy: (json['averageAccuracy'] as num?)?.toDouble() ?? 0,
      totalPlayTime: json['totalPlayTime'] == null
          ? Duration.zero
          : Duration(microseconds: (json['totalPlayTime'] as num).toInt()),
    );

Map<String, dynamic> _$$UserStatsImplToJson(_$UserStatsImpl instance) =>
    <String, dynamic>{
      'totalGamesPlayed': instance.totalGamesPlayed,
      'currentStreak': instance.currentStreak,
      'maxStreak': instance.maxStreak,
      'gameStats':
          instance.gameStats.map((k, e) => MapEntry(_$GameTypeEnumMap[k]!, e)),
      'totalXP': instance.totalXP,
      'level': instance.level,
      'lastGamePlayedAt': instance.lastGamePlayedAt?.toIso8601String(),
      'averageAccuracy': instance.averageAccuracy,
      'totalPlayTime': instance.totalPlayTime.inMicroseconds,
    };

const _$GameTypeEnumMap = {
  GameType.dualNBack: 'dualNBack',
  GameType.flashCards: 'flashCards',
  GameType.workingMemory: 'workingMemory',
  GameType.attentionTraining: 'attentionTraining',
};

_$GameStatsImpl _$$GameStatsImplFromJson(Map<String, dynamic> json) =>
    _$GameStatsImpl(
      gamesPlayed: (json['gamesPlayed'] as num?)?.toInt() ?? 0,
      bestScore: (json['bestScore'] as num?)?.toInt() ?? 0,
      averageAccuracy: (json['averageAccuracy'] as num?)?.toDouble() ?? 0.0,
      currentLevel: (json['currentLevel'] as num?)?.toInt() ?? 1,
      totalPlayTime: json['totalPlayTime'] == null
          ? Duration.zero
          : Duration(microseconds: (json['totalPlayTime'] as num).toInt()),
      lastPlayedAt: json['lastPlayedAt'] == null
          ? null
          : DateTime.parse(json['lastPlayedAt'] as String),
      totalXP: (json['totalXP'] as num?)?.toInt() ?? 0,
      gameSpecificStats: json['gameSpecificStats'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$GameStatsImplToJson(_$GameStatsImpl instance) =>
    <String, dynamic>{
      'gamesPlayed': instance.gamesPlayed,
      'bestScore': instance.bestScore,
      'averageAccuracy': instance.averageAccuracy,
      'currentLevel': instance.currentLevel,
      'totalPlayTime': instance.totalPlayTime.inMicroseconds,
      'lastPlayedAt': instance.lastPlayedAt?.toIso8601String(),
      'totalXP': instance.totalXP,
      'gameSpecificStats': instance.gameSpecificStats,
    };

_$UserPreferencesImpl _$$UserPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$UserPreferencesImpl(
      language: json['language'] as String? ?? 'ko',
      soundEnabled: json['soundEnabled'] as bool? ?? true,
      musicEnabled: json['musicEnabled'] as bool? ?? true,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      dailyReminderEnabled: json['dailyReminderEnabled'] as bool? ?? true,
      dailyReminderTime: json['dailyReminderTime'] == null
          ? const TimeOfDay(hour: 19, minute: 0)
          : TimeOfDay.fromJson(
              json['dailyReminderTime'] as Map<String, dynamic>),
      isDarkMode: json['isDarkMode'] as bool? ?? false,
      gameSpeed: (json['gameSpeed'] as num?)?.toDouble() ?? 1.0,
      hapticFeedbackEnabled: json['hapticFeedbackEnabled'] as bool? ?? true,
    );

Map<String, dynamic> _$$UserPreferencesImplToJson(
        _$UserPreferencesImpl instance) =>
    <String, dynamic>{
      'language': instance.language,
      'soundEnabled': instance.soundEnabled,
      'musicEnabled': instance.musicEnabled,
      'notificationsEnabled': instance.notificationsEnabled,
      'dailyReminderEnabled': instance.dailyReminderEnabled,
      'dailyReminderTime': instance.dailyReminderTime,
      'isDarkMode': instance.isDarkMode,
      'gameSpeed': instance.gameSpeed,
      'hapticFeedbackEnabled': instance.hapticFeedbackEnabled,
    };

_$TimeOfDayImpl _$$TimeOfDayImplFromJson(Map<String, dynamic> json) =>
    _$TimeOfDayImpl(
      hour: (json['hour'] as num).toInt(),
      minute: (json['minute'] as num).toInt(),
    );

Map<String, dynamic> _$$TimeOfDayImplToJson(_$TimeOfDayImpl instance) =>
    <String, dynamic>{
      'hour': instance.hour,
      'minute': instance.minute,
    };
