// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flash_card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FlashCardImpl _$$FlashCardImplFromJson(Map<String, dynamic> json) =>
    _$FlashCardImpl(
      id: json['id'] as String,
      deckId: json['deckId'] as String,
      front: json['front'] as String,
      back: json['back'] as String,
      hint: json['hint'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      review: CardReview.fromJson(json['review'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$FlashCardImplToJson(_$FlashCardImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deckId': instance.deckId,
      'front': instance.front,
      'back': instance.back,
      'hint': instance.hint,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'review': instance.review,
    };

_$CardDeckImpl _$$CardDeckImplFromJson(Map<String, dynamic> json) =>
    _$CardDeckImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String?,
      cards: (json['cards'] as List<dynamic>?)
              ?.map((e) => FlashCard.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      dailyGoal: (json['dailyGoal'] as num?)?.toInt() ?? 20,
      settings: json['settings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$CardDeckImplToJson(_$CardDeckImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'cards': instance.cards,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isActive': instance.isActive,
      'dailyGoal': instance.dailyGoal,
      'settings': instance.settings,
    };

_$CardReviewImpl _$$CardReviewImplFromJson(Map<String, dynamic> json) =>
    _$CardReviewImpl(
      easeFactor: (json['easeFactor'] as num?)?.toDouble() ?? 2.5,
      interval: (json['interval'] as num?)?.toInt() ?? 1,
      repetitions: (json['repetitions'] as num?)?.toInt() ?? 0,
      nextReviewDate: json['nextReviewDate'] == null
          ? null
          : DateTime.parse(json['nextReviewDate'] as String),
      lastReviewedAt: json['lastReviewedAt'] == null
          ? null
          : DateTime.parse(json['lastReviewedAt'] as String),
      lastQuality:
          $enumDecodeNullable(_$CardQualityEnumMap, json['lastQuality']) ??
              CardQuality.none,
    );

Map<String, dynamic> _$$CardReviewImplToJson(_$CardReviewImpl instance) =>
    <String, dynamic>{
      'easeFactor': instance.easeFactor,
      'interval': instance.interval,
      'repetitions': instance.repetitions,
      'nextReviewDate': instance.nextReviewDate?.toIso8601String(),
      'lastReviewedAt': instance.lastReviewedAt?.toIso8601String(),
      'lastQuality': _$CardQualityEnumMap[instance.lastQuality]!,
    };

const _$CardQualityEnumMap = {
  CardQuality.none: 'none',
  CardQuality.blackout: 'blackout',
  CardQuality.incorrect: 'incorrect',
  CardQuality.hard: 'hard',
  CardQuality.good: 'good',
  CardQuality.easy: 'easy',
};

_$CardStudySessionImpl _$$CardStudySessionImplFromJson(
        Map<String, dynamic> json) =>
    _$CardStudySessionImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      deckId: json['deckId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      reviews: (json['reviews'] as List<dynamic>?)
              ?.map((e) => CardReviewResult.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      cardsReviewed: (json['cardsReviewed'] as num?)?.toInt() ?? 0,
      correctAnswers: (json['correctAnswers'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CardStudySessionImplToJson(
        _$CardStudySessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'deckId': instance.deckId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'reviews': instance.reviews,
      'cardsReviewed': instance.cardsReviewed,
      'correctAnswers': instance.correctAnswers,
    };

_$CardReviewResultImpl _$$CardReviewResultImplFromJson(
        Map<String, dynamic> json) =>
    _$CardReviewResultImpl(
      cardId: json['cardId'] as String,
      quality: $enumDecode(_$CardQualityEnumMap, json['quality']),
      responseTime:
          Duration(microseconds: (json['responseTime'] as num).toInt()),
      reviewedAt: DateTime.parse(json['reviewedAt'] as String),
    );

Map<String, dynamic> _$$CardReviewResultImplToJson(
        _$CardReviewResultImpl instance) =>
    <String, dynamic>{
      'cardId': instance.cardId,
      'quality': _$CardQualityEnumMap[instance.quality]!,
      'responseTime': instance.responseTime.inMicroseconds,
      'reviewedAt': instance.reviewedAt.toIso8601String(),
    };

_$StorageUsageImpl _$$StorageUsageImplFromJson(Map<String, dynamic> json) =>
    _$StorageUsageImpl(
      currentCount: (json['currentCount'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      remainingCount: (json['remainingCount'] as num).toInt(),
      usagePercentage: (json['usagePercentage'] as num).toDouble(),
    );

Map<String, dynamic> _$$StorageUsageImplToJson(_$StorageUsageImpl instance) =>
    <String, dynamic>{
      'currentCount': instance.currentCount,
      'limit': instance.limit,
      'remainingCount': instance.remainingCount,
      'usagePercentage': instance.usagePercentage,
    };
