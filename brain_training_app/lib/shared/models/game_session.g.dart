// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GameSessionImpl _$$GameSessionImplFromJson(Map<String, dynamic> json) =>
    _$GameSessionImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      gameType: $enumDecode(_$GameTypeEnumMap, json['gameType']),
      level: (json['level'] as num).toInt(),
      accuracy: (json['accuracy'] as num).toDouble(),
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      score: (json['score'] as num).toInt(),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      gameSpecificData: json['gameSpecificData'] as Map<String, dynamic>,
      xpEarned: (json['xpEarned'] as num?)?.toInt() ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      events: (json['events'] as List<dynamic>?)
              ?.map((e) => GameEvent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$GameSessionImplToJson(_$GameSessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'gameType': _$GameTypeEnumMap[instance.gameType]!,
      'level': instance.level,
      'accuracy': instance.accuracy,
      'duration': instance.duration.inMicroseconds,
      'score': instance.score,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'gameSpecificData': instance.gameSpecificData,
      'xpEarned': instance.xpEarned,
      'isCompleted': instance.isCompleted,
      'events': instance.events,
    };

const _$GameTypeEnumMap = {
  GameType.dualNBack: 'dualNBack',
  GameType.flashCards: 'flashCards',
  GameType.workingMemory: 'workingMemory',
  GameType.attentionTraining: 'attentionTraining',
};

_$GameEventImpl _$$GameEventImplFromJson(Map<String, dynamic> json) =>
    _$GameEventImpl(
      timestamp: DateTime.parse(json['timestamp'] as String),
      eventType: json['eventType'] as String,
      data: json['data'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$$GameEventImplToJson(_$GameEventImpl instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp.toIso8601String(),
      'eventType': instance.eventType,
      'data': instance.data,
    };

_$DualNBackSessionDataImpl _$$DualNBackSessionDataImplFromJson(
        Map<String, dynamic> json) =>
    _$DualNBackSessionDataImpl(
      nLevel: (json['nLevel'] as num).toInt(),
      totalTrials: (json['totalTrials'] as num).toInt(),
      correctVisual: (json['correctVisual'] as num).toInt(),
      correctAudio: (json['correctAudio'] as num).toInt(),
      incorrectVisual: (json['incorrectVisual'] as num).toInt(),
      incorrectAudio: (json['incorrectAudio'] as num).toInt(),
      trials: (json['trials'] as List<dynamic>?)
              ?.map((e) => DualNBackTrial.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$DualNBackSessionDataImplToJson(
        _$DualNBackSessionDataImpl instance) =>
    <String, dynamic>{
      'nLevel': instance.nLevel,
      'totalTrials': instance.totalTrials,
      'correctVisual': instance.correctVisual,
      'correctAudio': instance.correctAudio,
      'incorrectVisual': instance.incorrectVisual,
      'incorrectAudio': instance.incorrectAudio,
      'trials': instance.trials,
    };

_$DualNBackTrialImpl _$$DualNBackTrialImplFromJson(Map<String, dynamic> json) =>
    _$DualNBackTrialImpl(
      trialNumber: (json['trialNumber'] as num).toInt(),
      visualPosition: json['visualPosition'] as String,
      audioStimulus: json['audioStimulus'] as String,
      shouldMatchVisual: json['shouldMatchVisual'] as bool,
      shouldMatchAudio: json['shouldMatchAudio'] as bool,
      userResponseVisual: json['userResponseVisual'] as bool,
      userResponseAudio: json['userResponseAudio'] as bool,
      responseTime:
          Duration(microseconds: (json['responseTime'] as num).toInt()),
    );

Map<String, dynamic> _$$DualNBackTrialImplToJson(
        _$DualNBackTrialImpl instance) =>
    <String, dynamic>{
      'trialNumber': instance.trialNumber,
      'visualPosition': instance.visualPosition,
      'audioStimulus': instance.audioStimulus,
      'shouldMatchVisual': instance.shouldMatchVisual,
      'shouldMatchAudio': instance.shouldMatchAudio,
      'userResponseVisual': instance.userResponseVisual,
      'userResponseAudio': instance.userResponseAudio,
      'responseTime': instance.responseTime.inMicroseconds,
    };
