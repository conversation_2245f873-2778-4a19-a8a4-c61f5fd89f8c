// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GameResult _$GameResultFromJson(Map<String, dynamic> json) {
  return _GameResult.fromJson(json);
}

/// @nodoc
mixin _$GameResult {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  GameType get gameType => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get score => throw _privateConstructorUsedError;
  double get accuracy => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  Map<String, dynamic> get gameSpecificData =>
      throw _privateConstructorUsedError;

  /// Serializes this GameResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GameResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GameResultCopyWith<GameResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GameResultCopyWith<$Res> {
  factory $GameResultCopyWith(
          GameResult value, $Res Function(GameResult) then) =
      _$GameResultCopyWithImpl<$Res, GameResult>;
  @useResult
  $Res call(
      {String id,
      String userId,
      GameType gameType,
      int level,
      int score,
      double accuracy,
      Duration duration,
      DateTime timestamp,
      Map<String, dynamic> gameSpecificData});
}

/// @nodoc
class _$GameResultCopyWithImpl<$Res, $Val extends GameResult>
    implements $GameResultCopyWith<$Res> {
  _$GameResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GameResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? gameType = null,
    Object? level = null,
    Object? score = null,
    Object? accuracy = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? gameSpecificData = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gameType: null == gameType
          ? _value.gameType
          : gameType // ignore: cast_nullable_to_non_nullable
              as GameType,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gameSpecificData: null == gameSpecificData
          ? _value.gameSpecificData
          : gameSpecificData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GameResultImplCopyWith<$Res>
    implements $GameResultCopyWith<$Res> {
  factory _$$GameResultImplCopyWith(
          _$GameResultImpl value, $Res Function(_$GameResultImpl) then) =
      __$$GameResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      GameType gameType,
      int level,
      int score,
      double accuracy,
      Duration duration,
      DateTime timestamp,
      Map<String, dynamic> gameSpecificData});
}

/// @nodoc
class __$$GameResultImplCopyWithImpl<$Res>
    extends _$GameResultCopyWithImpl<$Res, _$GameResultImpl>
    implements _$$GameResultImplCopyWith<$Res> {
  __$$GameResultImplCopyWithImpl(
      _$GameResultImpl _value, $Res Function(_$GameResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of GameResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? gameType = null,
    Object? level = null,
    Object? score = null,
    Object? accuracy = null,
    Object? duration = null,
    Object? timestamp = null,
    Object? gameSpecificData = null,
  }) {
    return _then(_$GameResultImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gameType: null == gameType
          ? _value.gameType
          : gameType // ignore: cast_nullable_to_non_nullable
              as GameType,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      accuracy: null == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gameSpecificData: null == gameSpecificData
          ? _value._gameSpecificData
          : gameSpecificData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GameResultImpl implements _GameResult {
  const _$GameResultImpl(
      {required this.id,
      required this.userId,
      required this.gameType,
      required this.level,
      required this.score,
      required this.accuracy,
      required this.duration,
      required this.timestamp,
      final Map<String, dynamic> gameSpecificData = const {}})
      : _gameSpecificData = gameSpecificData;

  factory _$GameResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$GameResultImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final GameType gameType;
  @override
  final int level;
  @override
  final int score;
  @override
  final double accuracy;
  @override
  final Duration duration;
  @override
  final DateTime timestamp;
  final Map<String, dynamic> _gameSpecificData;
  @override
  @JsonKey()
  Map<String, dynamic> get gameSpecificData {
    if (_gameSpecificData is EqualUnmodifiableMapView) return _gameSpecificData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_gameSpecificData);
  }

  @override
  String toString() {
    return 'GameResult(id: $id, userId: $userId, gameType: $gameType, level: $level, score: $score, accuracy: $accuracy, duration: $duration, timestamp: $timestamp, gameSpecificData: $gameSpecificData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GameResultImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.gameType, gameType) ||
                other.gameType == gameType) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            const DeepCollectionEquality()
                .equals(other._gameSpecificData, _gameSpecificData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      gameType,
      level,
      score,
      accuracy,
      duration,
      timestamp,
      const DeepCollectionEquality().hash(_gameSpecificData));

  /// Create a copy of GameResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GameResultImplCopyWith<_$GameResultImpl> get copyWith =>
      __$$GameResultImplCopyWithImpl<_$GameResultImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GameResultImplToJson(
      this,
    );
  }
}

abstract class _GameResult implements GameResult {
  const factory _GameResult(
      {required final String id,
      required final String userId,
      required final GameType gameType,
      required final int level,
      required final int score,
      required final double accuracy,
      required final Duration duration,
      required final DateTime timestamp,
      final Map<String, dynamic> gameSpecificData}) = _$GameResultImpl;

  factory _GameResult.fromJson(Map<String, dynamic> json) =
      _$GameResultImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  GameType get gameType;
  @override
  int get level;
  @override
  int get score;
  @override
  double get accuracy;
  @override
  Duration get duration;
  @override
  DateTime get timestamp;
  @override
  Map<String, dynamic> get gameSpecificData;

  /// Create a copy of GameResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GameResultImplCopyWith<_$GameResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
