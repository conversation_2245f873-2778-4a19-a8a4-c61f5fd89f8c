// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reward_point.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RewardPoint _$RewardPointFromJson(Map<String, dynamic> json) {
  return _RewardPoint.fromJson(json);
}

/// @nodoc
mixin _$RewardPoint {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  RewardType get type => throw _privateConstructorUsedError;
  int get amount => throw _privateConstructorUsedError;
  String get source => throw _privateConstructorUsedError;
  DateTime get earnedAt => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this RewardPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RewardPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RewardPointCopyWith<RewardPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardPointCopyWith<$Res> {
  factory $RewardPointCopyWith(
          RewardPoint value, $Res Function(RewardPoint) then) =
      _$RewardPointCopyWithImpl<$Res, RewardPoint>;
  @useResult
  $Res call(
      {String id,
      String userId,
      RewardType type,
      int amount,
      String source,
      DateTime earnedAt,
      String? description,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$RewardPointCopyWithImpl<$Res, $Val extends RewardPoint>
    implements $RewardPointCopyWith<$Res> {
  _$RewardPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RewardPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? amount = null,
    Object? source = null,
    Object? earnedAt = null,
    Object? description = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RewardType,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      source: null == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String,
      earnedAt: null == earnedAt
          ? _value.earnedAt
          : earnedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RewardPointImplCopyWith<$Res>
    implements $RewardPointCopyWith<$Res> {
  factory _$$RewardPointImplCopyWith(
          _$RewardPointImpl value, $Res Function(_$RewardPointImpl) then) =
      __$$RewardPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      RewardType type,
      int amount,
      String source,
      DateTime earnedAt,
      String? description,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$RewardPointImplCopyWithImpl<$Res>
    extends _$RewardPointCopyWithImpl<$Res, _$RewardPointImpl>
    implements _$$RewardPointImplCopyWith<$Res> {
  __$$RewardPointImplCopyWithImpl(
      _$RewardPointImpl _value, $Res Function(_$RewardPointImpl) _then)
      : super(_value, _then);

  /// Create a copy of RewardPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? amount = null,
    Object? source = null,
    Object? earnedAt = null,
    Object? description = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$RewardPointImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RewardType,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      source: null == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String,
      earnedAt: null == earnedAt
          ? _value.earnedAt
          : earnedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardPointImpl implements _RewardPoint {
  const _$RewardPointImpl(
      {required this.id,
      required this.userId,
      required this.type,
      required this.amount,
      required this.source,
      required this.earnedAt,
      this.description,
      final Map<String, dynamic>? metadata})
      : _metadata = metadata;

  factory _$RewardPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardPointImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final RewardType type;
  @override
  final int amount;
  @override
  final String source;
  @override
  final DateTime earnedAt;
  @override
  final String? description;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'RewardPoint(id: $id, userId: $userId, type: $type, amount: $amount, source: $source, earnedAt: $earnedAt, description: $description, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardPointImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.earnedAt, earnedAt) ||
                other.earnedAt == earnedAt) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, type, amount, source,
      earnedAt, description, const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of RewardPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardPointImplCopyWith<_$RewardPointImpl> get copyWith =>
      __$$RewardPointImplCopyWithImpl<_$RewardPointImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardPointImplToJson(
      this,
    );
  }
}

abstract class _RewardPoint implements RewardPoint {
  const factory _RewardPoint(
      {required final String id,
      required final String userId,
      required final RewardType type,
      required final int amount,
      required final String source,
      required final DateTime earnedAt,
      final String? description,
      final Map<String, dynamic>? metadata}) = _$RewardPointImpl;

  factory _RewardPoint.fromJson(Map<String, dynamic> json) =
      _$RewardPointImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  RewardType get type;
  @override
  int get amount;
  @override
  String get source;
  @override
  DateTime get earnedAt;
  @override
  String? get description;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of RewardPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RewardPointImplCopyWith<_$RewardPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardBalance _$RewardBalanceFromJson(Map<String, dynamic> json) {
  return _RewardBalance.fromJson(json);
}

/// @nodoc
mixin _$RewardBalance {
  String get userId => throw _privateConstructorUsedError;
  int get totalEarned => throw _privateConstructorUsedError;
  int get totalSpent => throw _privateConstructorUsedError;
  int get currentBalance => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  Map<RewardType, int> get earnedByType => throw _privateConstructorUsedError;
  Map<UnlockableReward, int> get spentOnRewards =>
      throw _privateConstructorUsedError;

  /// Serializes this RewardBalance to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RewardBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RewardBalanceCopyWith<RewardBalance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardBalanceCopyWith<$Res> {
  factory $RewardBalanceCopyWith(
          RewardBalance value, $Res Function(RewardBalance) then) =
      _$RewardBalanceCopyWithImpl<$Res, RewardBalance>;
  @useResult
  $Res call(
      {String userId,
      int totalEarned,
      int totalSpent,
      int currentBalance,
      DateTime lastUpdated,
      Map<RewardType, int> earnedByType,
      Map<UnlockableReward, int> spentOnRewards});
}

/// @nodoc
class _$RewardBalanceCopyWithImpl<$Res, $Val extends RewardBalance>
    implements $RewardBalanceCopyWith<$Res> {
  _$RewardBalanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RewardBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? totalEarned = null,
    Object? totalSpent = null,
    Object? currentBalance = null,
    Object? lastUpdated = null,
    Object? earnedByType = null,
    Object? spentOnRewards = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as int,
      currentBalance: null == currentBalance
          ? _value.currentBalance
          : currentBalance // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      earnedByType: null == earnedByType
          ? _value.earnedByType
          : earnedByType // ignore: cast_nullable_to_non_nullable
              as Map<RewardType, int>,
      spentOnRewards: null == spentOnRewards
          ? _value.spentOnRewards
          : spentOnRewards // ignore: cast_nullable_to_non_nullable
              as Map<UnlockableReward, int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RewardBalanceImplCopyWith<$Res>
    implements $RewardBalanceCopyWith<$Res> {
  factory _$$RewardBalanceImplCopyWith(
          _$RewardBalanceImpl value, $Res Function(_$RewardBalanceImpl) then) =
      __$$RewardBalanceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      int totalEarned,
      int totalSpent,
      int currentBalance,
      DateTime lastUpdated,
      Map<RewardType, int> earnedByType,
      Map<UnlockableReward, int> spentOnRewards});
}

/// @nodoc
class __$$RewardBalanceImplCopyWithImpl<$Res>
    extends _$RewardBalanceCopyWithImpl<$Res, _$RewardBalanceImpl>
    implements _$$RewardBalanceImplCopyWith<$Res> {
  __$$RewardBalanceImplCopyWithImpl(
      _$RewardBalanceImpl _value, $Res Function(_$RewardBalanceImpl) _then)
      : super(_value, _then);

  /// Create a copy of RewardBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? totalEarned = null,
    Object? totalSpent = null,
    Object? currentBalance = null,
    Object? lastUpdated = null,
    Object? earnedByType = null,
    Object? spentOnRewards = null,
  }) {
    return _then(_$RewardBalanceImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as int,
      currentBalance: null == currentBalance
          ? _value.currentBalance
          : currentBalance // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      earnedByType: null == earnedByType
          ? _value._earnedByType
          : earnedByType // ignore: cast_nullable_to_non_nullable
              as Map<RewardType, int>,
      spentOnRewards: null == spentOnRewards
          ? _value._spentOnRewards
          : spentOnRewards // ignore: cast_nullable_to_non_nullable
              as Map<UnlockableReward, int>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardBalanceImpl implements _RewardBalance {
  const _$RewardBalanceImpl(
      {required this.userId,
      required this.totalEarned,
      required this.totalSpent,
      required this.currentBalance,
      required this.lastUpdated,
      final Map<RewardType, int> earnedByType = const {},
      final Map<UnlockableReward, int> spentOnRewards = const {}})
      : _earnedByType = earnedByType,
        _spentOnRewards = spentOnRewards;

  factory _$RewardBalanceImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardBalanceImplFromJson(json);

  @override
  final String userId;
  @override
  final int totalEarned;
  @override
  final int totalSpent;
  @override
  final int currentBalance;
  @override
  final DateTime lastUpdated;
  final Map<RewardType, int> _earnedByType;
  @override
  @JsonKey()
  Map<RewardType, int> get earnedByType {
    if (_earnedByType is EqualUnmodifiableMapView) return _earnedByType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_earnedByType);
  }

  final Map<UnlockableReward, int> _spentOnRewards;
  @override
  @JsonKey()
  Map<UnlockableReward, int> get spentOnRewards {
    if (_spentOnRewards is EqualUnmodifiableMapView) return _spentOnRewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_spentOnRewards);
  }

  @override
  String toString() {
    return 'RewardBalance(userId: $userId, totalEarned: $totalEarned, totalSpent: $totalSpent, currentBalance: $currentBalance, lastUpdated: $lastUpdated, earnedByType: $earnedByType, spentOnRewards: $spentOnRewards)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardBalanceImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.totalEarned, totalEarned) ||
                other.totalEarned == totalEarned) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent) &&
            (identical(other.currentBalance, currentBalance) ||
                other.currentBalance == currentBalance) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            const DeepCollectionEquality()
                .equals(other._earnedByType, _earnedByType) &&
            const DeepCollectionEquality()
                .equals(other._spentOnRewards, _spentOnRewards));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      totalEarned,
      totalSpent,
      currentBalance,
      lastUpdated,
      const DeepCollectionEquality().hash(_earnedByType),
      const DeepCollectionEquality().hash(_spentOnRewards));

  /// Create a copy of RewardBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardBalanceImplCopyWith<_$RewardBalanceImpl> get copyWith =>
      __$$RewardBalanceImplCopyWithImpl<_$RewardBalanceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardBalanceImplToJson(
      this,
    );
  }
}

abstract class _RewardBalance implements RewardBalance {
  const factory _RewardBalance(
      {required final String userId,
      required final int totalEarned,
      required final int totalSpent,
      required final int currentBalance,
      required final DateTime lastUpdated,
      final Map<RewardType, int> earnedByType,
      final Map<UnlockableReward, int> spentOnRewards}) = _$RewardBalanceImpl;

  factory _RewardBalance.fromJson(Map<String, dynamic> json) =
      _$RewardBalanceImpl.fromJson;

  @override
  String get userId;
  @override
  int get totalEarned;
  @override
  int get totalSpent;
  @override
  int get currentBalance;
  @override
  DateTime get lastUpdated;
  @override
  Map<RewardType, int> get earnedByType;
  @override
  Map<UnlockableReward, int> get spentOnRewards;

  /// Create a copy of RewardBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RewardBalanceImplCopyWith<_$RewardBalanceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardTransaction _$RewardTransactionFromJson(Map<String, dynamic> json) {
  return _RewardTransaction.fromJson(json);
}

/// @nodoc
mixin _$RewardTransaction {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  int get amount => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isEarned =>
      throw _privateConstructorUsedError; // true for earned, false for spent
  String? get source => throw _privateConstructorUsedError;
  UnlockableReward? get unlockableReward => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this RewardTransaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RewardTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RewardTransactionCopyWith<RewardTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardTransactionCopyWith<$Res> {
  factory $RewardTransactionCopyWith(
          RewardTransaction value, $Res Function(RewardTransaction) then) =
      _$RewardTransactionCopyWithImpl<$Res, RewardTransaction>;
  @useResult
  $Res call(
      {String id,
      String userId,
      int amount,
      String description,
      DateTime timestamp,
      bool isEarned,
      String? source,
      UnlockableReward? unlockableReward,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$RewardTransactionCopyWithImpl<$Res, $Val extends RewardTransaction>
    implements $RewardTransactionCopyWith<$Res> {
  _$RewardTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RewardTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? amount = null,
    Object? description = null,
    Object? timestamp = null,
    Object? isEarned = null,
    Object? source = freezed,
    Object? unlockableReward = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isEarned: null == isEarned
          ? _value.isEarned
          : isEarned // ignore: cast_nullable_to_non_nullable
              as bool,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockableReward: freezed == unlockableReward
          ? _value.unlockableReward
          : unlockableReward // ignore: cast_nullable_to_non_nullable
              as UnlockableReward?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RewardTransactionImplCopyWith<$Res>
    implements $RewardTransactionCopyWith<$Res> {
  factory _$$RewardTransactionImplCopyWith(_$RewardTransactionImpl value,
          $Res Function(_$RewardTransactionImpl) then) =
      __$$RewardTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      int amount,
      String description,
      DateTime timestamp,
      bool isEarned,
      String? source,
      UnlockableReward? unlockableReward,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$RewardTransactionImplCopyWithImpl<$Res>
    extends _$RewardTransactionCopyWithImpl<$Res, _$RewardTransactionImpl>
    implements _$$RewardTransactionImplCopyWith<$Res> {
  __$$RewardTransactionImplCopyWithImpl(_$RewardTransactionImpl _value,
      $Res Function(_$RewardTransactionImpl) _then)
      : super(_value, _then);

  /// Create a copy of RewardTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? amount = null,
    Object? description = null,
    Object? timestamp = null,
    Object? isEarned = null,
    Object? source = freezed,
    Object? unlockableReward = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$RewardTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isEarned: null == isEarned
          ? _value.isEarned
          : isEarned // ignore: cast_nullable_to_non_nullable
              as bool,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockableReward: freezed == unlockableReward
          ? _value.unlockableReward
          : unlockableReward // ignore: cast_nullable_to_non_nullable
              as UnlockableReward?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardTransactionImpl implements _RewardTransaction {
  const _$RewardTransactionImpl(
      {required this.id,
      required this.userId,
      required this.amount,
      required this.description,
      required this.timestamp,
      required this.isEarned,
      this.source,
      this.unlockableReward,
      final Map<String, dynamic>? metadata})
      : _metadata = metadata;

  factory _$RewardTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardTransactionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final int amount;
  @override
  final String description;
  @override
  final DateTime timestamp;
  @override
  final bool isEarned;
// true for earned, false for spent
  @override
  final String? source;
  @override
  final UnlockableReward? unlockableReward;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'RewardTransaction(id: $id, userId: $userId, amount: $amount, description: $description, timestamp: $timestamp, isEarned: $isEarned, source: $source, unlockableReward: $unlockableReward, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isEarned, isEarned) ||
                other.isEarned == isEarned) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.unlockableReward, unlockableReward) ||
                other.unlockableReward == unlockableReward) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      amount,
      description,
      timestamp,
      isEarned,
      source,
      unlockableReward,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of RewardTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardTransactionImplCopyWith<_$RewardTransactionImpl> get copyWith =>
      __$$RewardTransactionImplCopyWithImpl<_$RewardTransactionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardTransactionImplToJson(
      this,
    );
  }
}

abstract class _RewardTransaction implements RewardTransaction {
  const factory _RewardTransaction(
      {required final String id,
      required final String userId,
      required final int amount,
      required final String description,
      required final DateTime timestamp,
      required final bool isEarned,
      final String? source,
      final UnlockableReward? unlockableReward,
      final Map<String, dynamic>? metadata}) = _$RewardTransactionImpl;

  factory _RewardTransaction.fromJson(Map<String, dynamic> json) =
      _$RewardTransactionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  int get amount;
  @override
  String get description;
  @override
  DateTime get timestamp;
  @override
  bool get isEarned; // true for earned, false for spent
  @override
  String? get source;
  @override
  UnlockableReward? get unlockableReward;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of RewardTransaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RewardTransactionImplCopyWith<_$RewardTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UnlockableRewardConfig _$UnlockableRewardConfigFromJson(
    Map<String, dynamic> json) {
  return _UnlockableRewardConfig.fromJson(json);
}

/// @nodoc
mixin _$UnlockableRewardConfig {
  UnlockableReward get type => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  int get cost => throw _privateConstructorUsedError;
  bool get isRepeatable => throw _privateConstructorUsedError;
  String? get iconPath => throw _privateConstructorUsedError;
  Map<String, dynamic> get benefits => throw _privateConstructorUsedError;
  List<String> get requirements => throw _privateConstructorUsedError;

  /// Serializes this UnlockableRewardConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UnlockableRewardConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UnlockableRewardConfigCopyWith<UnlockableRewardConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnlockableRewardConfigCopyWith<$Res> {
  factory $UnlockableRewardConfigCopyWith(UnlockableRewardConfig value,
          $Res Function(UnlockableRewardConfig) then) =
      _$UnlockableRewardConfigCopyWithImpl<$Res, UnlockableRewardConfig>;
  @useResult
  $Res call(
      {UnlockableReward type,
      String title,
      String description,
      int cost,
      bool isRepeatable,
      String? iconPath,
      Map<String, dynamic> benefits,
      List<String> requirements});
}

/// @nodoc
class _$UnlockableRewardConfigCopyWithImpl<$Res,
        $Val extends UnlockableRewardConfig>
    implements $UnlockableRewardConfigCopyWith<$Res> {
  _$UnlockableRewardConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UnlockableRewardConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? title = null,
    Object? description = null,
    Object? cost = null,
    Object? isRepeatable = null,
    Object? iconPath = freezed,
    Object? benefits = null,
    Object? requirements = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnlockableReward,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      cost: null == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int,
      isRepeatable: null == isRepeatable
          ? _value.isRepeatable
          : isRepeatable // ignore: cast_nullable_to_non_nullable
              as bool,
      iconPath: freezed == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      benefits: null == benefits
          ? _value.benefits
          : benefits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      requirements: null == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UnlockableRewardConfigImplCopyWith<$Res>
    implements $UnlockableRewardConfigCopyWith<$Res> {
  factory _$$UnlockableRewardConfigImplCopyWith(
          _$UnlockableRewardConfigImpl value,
          $Res Function(_$UnlockableRewardConfigImpl) then) =
      __$$UnlockableRewardConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UnlockableReward type,
      String title,
      String description,
      int cost,
      bool isRepeatable,
      String? iconPath,
      Map<String, dynamic> benefits,
      List<String> requirements});
}

/// @nodoc
class __$$UnlockableRewardConfigImplCopyWithImpl<$Res>
    extends _$UnlockableRewardConfigCopyWithImpl<$Res,
        _$UnlockableRewardConfigImpl>
    implements _$$UnlockableRewardConfigImplCopyWith<$Res> {
  __$$UnlockableRewardConfigImplCopyWithImpl(
      _$UnlockableRewardConfigImpl _value,
      $Res Function(_$UnlockableRewardConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of UnlockableRewardConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? title = null,
    Object? description = null,
    Object? cost = null,
    Object? isRepeatable = null,
    Object? iconPath = freezed,
    Object? benefits = null,
    Object? requirements = null,
  }) {
    return _then(_$UnlockableRewardConfigImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnlockableReward,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      cost: null == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int,
      isRepeatable: null == isRepeatable
          ? _value.isRepeatable
          : isRepeatable // ignore: cast_nullable_to_non_nullable
              as bool,
      iconPath: freezed == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      benefits: null == benefits
          ? _value._benefits
          : benefits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      requirements: null == requirements
          ? _value._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnlockableRewardConfigImpl implements _UnlockableRewardConfig {
  const _$UnlockableRewardConfigImpl(
      {required this.type,
      required this.title,
      required this.description,
      required this.cost,
      required this.isRepeatable,
      this.iconPath,
      final Map<String, dynamic> benefits = const {},
      final List<String> requirements = const []})
      : _benefits = benefits,
        _requirements = requirements;

  factory _$UnlockableRewardConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$UnlockableRewardConfigImplFromJson(json);

  @override
  final UnlockableReward type;
  @override
  final String title;
  @override
  final String description;
  @override
  final int cost;
  @override
  final bool isRepeatable;
  @override
  final String? iconPath;
  final Map<String, dynamic> _benefits;
  @override
  @JsonKey()
  Map<String, dynamic> get benefits {
    if (_benefits is EqualUnmodifiableMapView) return _benefits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_benefits);
  }

  final List<String> _requirements;
  @override
  @JsonKey()
  List<String> get requirements {
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requirements);
  }

  @override
  String toString() {
    return 'UnlockableRewardConfig(type: $type, title: $title, description: $description, cost: $cost, isRepeatable: $isRepeatable, iconPath: $iconPath, benefits: $benefits, requirements: $requirements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnlockableRewardConfigImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.isRepeatable, isRepeatable) ||
                other.isRepeatable == isRepeatable) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            const DeepCollectionEquality().equals(other._benefits, _benefits) &&
            const DeepCollectionEquality()
                .equals(other._requirements, _requirements));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      title,
      description,
      cost,
      isRepeatable,
      iconPath,
      const DeepCollectionEquality().hash(_benefits),
      const DeepCollectionEquality().hash(_requirements));

  /// Create a copy of UnlockableRewardConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnlockableRewardConfigImplCopyWith<_$UnlockableRewardConfigImpl>
      get copyWith => __$$UnlockableRewardConfigImplCopyWithImpl<
          _$UnlockableRewardConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnlockableRewardConfigImplToJson(
      this,
    );
  }
}

abstract class _UnlockableRewardConfig implements UnlockableRewardConfig {
  const factory _UnlockableRewardConfig(
      {required final UnlockableReward type,
      required final String title,
      required final String description,
      required final int cost,
      required final bool isRepeatable,
      final String? iconPath,
      final Map<String, dynamic> benefits,
      final List<String> requirements}) = _$UnlockableRewardConfigImpl;

  factory _UnlockableRewardConfig.fromJson(Map<String, dynamic> json) =
      _$UnlockableRewardConfigImpl.fromJson;

  @override
  UnlockableReward get type;
  @override
  String get title;
  @override
  String get description;
  @override
  int get cost;
  @override
  bool get isRepeatable;
  @override
  String? get iconPath;
  @override
  Map<String, dynamic> get benefits;
  @override
  List<String> get requirements;

  /// Create a copy of UnlockableRewardConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnlockableRewardConfigImplCopyWith<_$UnlockableRewardConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UserUnlock _$UserUnlockFromJson(Map<String, dynamic> json) {
  return _UserUnlock.fromJson(json);
}

/// @nodoc
mixin _$UserUnlock {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  UnlockableReward get reward => throw _privateConstructorUsedError;
  int get pointsSpent => throw _privateConstructorUsedError;
  DateTime get unlockedAt => throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this UserUnlock to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserUnlock
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserUnlockCopyWith<UserUnlock> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserUnlockCopyWith<$Res> {
  factory $UserUnlockCopyWith(
          UserUnlock value, $Res Function(UserUnlock) then) =
      _$UserUnlockCopyWithImpl<$Res, UserUnlock>;
  @useResult
  $Res call(
      {String id,
      String userId,
      UnlockableReward reward,
      int pointsSpent,
      DateTime unlockedAt,
      String? transactionId,
      int quantity,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$UserUnlockCopyWithImpl<$Res, $Val extends UserUnlock>
    implements $UserUnlockCopyWith<$Res> {
  _$UserUnlockCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserUnlock
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? reward = null,
    Object? pointsSpent = null,
    Object? unlockedAt = null,
    Object? transactionId = freezed,
    Object? quantity = null,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as UnlockableReward,
      pointsSpent: null == pointsSpent
          ? _value.pointsSpent
          : pointsSpent // ignore: cast_nullable_to_non_nullable
              as int,
      unlockedAt: null == unlockedAt
          ? _value.unlockedAt
          : unlockedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserUnlockImplCopyWith<$Res>
    implements $UserUnlockCopyWith<$Res> {
  factory _$$UserUnlockImplCopyWith(
          _$UserUnlockImpl value, $Res Function(_$UserUnlockImpl) then) =
      __$$UserUnlockImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      UnlockableReward reward,
      int pointsSpent,
      DateTime unlockedAt,
      String? transactionId,
      int quantity,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$UserUnlockImplCopyWithImpl<$Res>
    extends _$UserUnlockCopyWithImpl<$Res, _$UserUnlockImpl>
    implements _$$UserUnlockImplCopyWith<$Res> {
  __$$UserUnlockImplCopyWithImpl(
      _$UserUnlockImpl _value, $Res Function(_$UserUnlockImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserUnlock
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? reward = null,
    Object? pointsSpent = null,
    Object? unlockedAt = null,
    Object? transactionId = freezed,
    Object? quantity = null,
    Object? metadata = freezed,
  }) {
    return _then(_$UserUnlockImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as UnlockableReward,
      pointsSpent: null == pointsSpent
          ? _value.pointsSpent
          : pointsSpent // ignore: cast_nullable_to_non_nullable
              as int,
      unlockedAt: null == unlockedAt
          ? _value.unlockedAt
          : unlockedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserUnlockImpl implements _UserUnlock {
  const _$UserUnlockImpl(
      {required this.id,
      required this.userId,
      required this.reward,
      required this.pointsSpent,
      required this.unlockedAt,
      this.transactionId,
      this.quantity = 1,
      final Map<String, dynamic>? metadata})
      : _metadata = metadata;

  factory _$UserUnlockImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserUnlockImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final UnlockableReward reward;
  @override
  final int pointsSpent;
  @override
  final DateTime unlockedAt;
  @override
  final String? transactionId;
  @override
  @JsonKey()
  final int quantity;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'UserUnlock(id: $id, userId: $userId, reward: $reward, pointsSpent: $pointsSpent, unlockedAt: $unlockedAt, transactionId: $transactionId, quantity: $quantity, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserUnlockImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.pointsSpent, pointsSpent) ||
                other.pointsSpent == pointsSpent) &&
            (identical(other.unlockedAt, unlockedAt) ||
                other.unlockedAt == unlockedAt) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      reward,
      pointsSpent,
      unlockedAt,
      transactionId,
      quantity,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of UserUnlock
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserUnlockImplCopyWith<_$UserUnlockImpl> get copyWith =>
      __$$UserUnlockImplCopyWithImpl<_$UserUnlockImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserUnlockImplToJson(
      this,
    );
  }
}

abstract class _UserUnlock implements UserUnlock {
  const factory _UserUnlock(
      {required final String id,
      required final String userId,
      required final UnlockableReward reward,
      required final int pointsSpent,
      required final DateTime unlockedAt,
      final String? transactionId,
      final int quantity,
      final Map<String, dynamic>? metadata}) = _$UserUnlockImpl;

  factory _UserUnlock.fromJson(Map<String, dynamic> json) =
      _$UserUnlockImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  UnlockableReward get reward;
  @override
  int get pointsSpent;
  @override
  DateTime get unlockedAt;
  @override
  String? get transactionId;
  @override
  int get quantity;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of UserUnlock
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserUnlockImplCopyWith<_$UserUnlockImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
