import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/constants/game_constants.dart';

part 'game_session.freezed.dart';
part 'game_session.g.dart';

@freezed
class GameSession with _$GameSession {
  const factory GameSession({
    required String id,
    required String userId,
    required GameType gameType,
    required int level,
    required double accuracy,
    required Duration duration,
    required int score,
    required DateTime startTime,
    required DateTime endTime,
    required Map<String, dynamic> gameSpecificData,
    @Default(0) int xpEarned,
    @Default(false) bool isCompleted,
    @Default([]) List<GameEvent> events,
  }) = _GameSession;

  factory GameSession.fromJson(Map<String, dynamic> json) => _$GameSessionFromJson(json);
}

@freezed
class GameEvent with _$GameEvent {
  const factory GameEvent({
    required DateTime timestamp,
    required String eventType,
    required Map<String, dynamic> data,
  }) = _GameEvent;

  factory GameEvent.fromJson(Map<String, dynamic> json) => _$GameEventFromJson(json);
}

@freezed
class DualNBackSessionData with _$DualNBackSessionData {
  const factory DualNBackSessionData({
    required int nLevel,
    required int totalTrials,
    required int correctVisual,
    required int correctAudio,
    required int incorrectVisual,
    required int incorrectAudio,
    @Default([]) List<DualNBackTrial> trials,
  }) = _DualNBackSessionData;

  factory DualNBackSessionData.fromJson(Map<String, dynamic> json) => _$DualNBackSessionDataFromJson(json);
}

@freezed
class DualNBackTrial with _$DualNBackTrial {
  const factory DualNBackTrial({
    required int trialNumber,
    required String visualPosition,
    required String audioStimulus,
    required bool shouldMatchVisual,
    required bool shouldMatchAudio,
    required bool userResponseVisual,
    required bool userResponseAudio,
    required Duration responseTime,
  }) = _DualNBackTrial;

  factory DualNBackTrial.fromJson(Map<String, dynamic> json) => _$DualNBackTrialFromJson(json);
}