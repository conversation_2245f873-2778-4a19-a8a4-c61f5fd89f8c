import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/constants/game_constants.dart';

part 'achievement.freezed.dart';
part 'achievement.g.dart';

@freezed
class Achievement with _$Achievement {
  const factory Achievement({
    required String id,
    required String title,
    required String description,
    required String iconPath,
    required AchievementType type,
    required Map<String, dynamic> criteria,
    required int xpReward,
    @Default(false) bool isUnlocked,
    DateTime? unlockedAt,
    @Default(0) int progress,
    @Default(1) int maxProgress,
  }) = _Achievement;

  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);
}

@freezed
class UserAchievement with _$UserAchievement {
  const factory UserAchievement({
    required String userId,
    required String achievementId,
    required DateTime unlockedAt,
    @Default(0) int progress,
  }) = _UserAchievement;

  factory UserAchievement.fromJson(Map<String, dynamic> json) => _$UserAchievementFromJson(json);
}

@freezed
class LeaderboardEntry with _$LeaderboardEntry {
  const factory LeaderboardEntry({
    required String userId,
    required String displayName,
    String? photoURL,
    required int score,
    required int rank,
    required DateTime lastUpdated,
    @Default({}) Map<String, dynamic> metadata,
  }) = _LeaderboardEntry;

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) => _$LeaderboardEntryFromJson(json);
}