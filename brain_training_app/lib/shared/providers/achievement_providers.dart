import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/database_service.dart';
import '../../core/constants/game_constants.dart';
import '../models/achievement.dart';
import '../models/user.dart';
import '../models/game_session.dart';

final achievementServiceProvider = Provider<AchievementService>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return AchievementService(databaseService);
});

final userAchievementsProvider = FutureProvider.family<List<Achievement>, String>((ref, userId) async {
  final achievementService = ref.read(achievementServiceProvider);
  return achievementService.getUserAchievements(userId);
});

final achievementProgressProvider = FutureProvider.family<Map<String, int>, String>((ref, userId) async {
  final achievementService = ref.read(achievementServiceProvider);
  return achievementService.getAchievementProgress(userId);
});

final recentAchievementsProvider = FutureProvider.family<List<Achievement>, String>((ref, userId) async {
  final achievementService = ref.read(achievementServiceProvider);
  return achievementService.getRecentAchievements(userId, limit: 5);
});

class AchievementService {
  final DatabaseService _databaseService;

  AchievementService(this._databaseService);

  // Default achievements that are available for all users
  static final List<Achievement> _defaultAchievements = [
    // Streak achievements
    Achievement(
      id: 'streak_3',
      title: '3일 연속',
      description: '3일 연속으로 게임을 플레이하세요',
      iconPath: 'assets/images/achievements/streak_3.png',
      type: AchievementType.streak,
      criteria: {'streak_days': 3},
      xpReward: 100,
      maxProgress: 3,
    ),
    Achievement(
      id: 'streak_7',
      title: '일주일 달성',
      description: '7일 연속으로 게임을 플레이하세요',
      iconPath: 'assets/images/achievements/streak_7.png',
      type: AchievementType.streak,
      criteria: {'streak_days': 7},
      xpReward: 300,
      maxProgress: 7,
    ),
    Achievement(
      id: 'streak_30',
      title: '한 달 마스터',
      description: '30일 연속으로 게임을 플레이하세요',
      iconPath: 'assets/images/achievements/streak_30.png',
      type: AchievementType.streak,
      criteria: {'streak_days': 30},
      xpReward: 1000,
      maxProgress: 30,
    ),
    
    // Dual N-Back achievements
    Achievement(
      id: 'dual_nback_first',
      title: '첫 번째 도전',
      description: 'Dual N-Back 게임을 처음 완료하세요',
      iconPath: 'assets/images/achievements/dual_nback_first.png',
      type: AchievementType.gameSpecific,
      criteria: {'game_type': 'dualNBack', 'games_played': 1},
      xpReward: 50,
      maxProgress: 1,
    ),
    Achievement(
      id: 'dual_nback_level_5',
      title: '5-Back 달성',
      description: 'Dual N-Back에서 5-Back 레벨에 도달하세요',
      iconPath: 'assets/images/achievements/dual_nback_level_5.png',
      type: AchievementType.level,
      criteria: {'game_type': 'dualNBack', 'level': 5},
      xpReward: 500,
      maxProgress: 5,
    ),
    Achievement(
      id: 'dual_nback_accuracy_90',
      title: '정확도 마스터',
      description: 'Dual N-Back에서 90% 이상의 정확도를 달성하세요',
      iconPath: 'assets/images/achievements/dual_nback_accuracy.png',
      type: AchievementType.gameSpecific,
      criteria: {'game_type': 'dualNBack', 'accuracy': 0.9},
      xpReward: 200,
      maxProgress: 1,
    ),
    
    // Flash Cards achievements
    Achievement(
      id: 'flashcards_first_deck',
      title: '첫 번째 덱',
      description: '첫 번째 플래시카드 덱을 완성하세요',
      iconPath: 'assets/images/achievements/flashcards_first.png',
      type: AchievementType.gameSpecific,
      criteria: {'game_type': 'flashCards', 'decks_completed': 1},
      xpReward: 100,
      maxProgress: 1,
    ),
    Achievement(
      id: 'flashcards_100_cards',
      title: '카드 마스터',
      description: '100개의 카드를 학습하세요',
      iconPath: 'assets/images/achievements/flashcards_100.png',
      type: AchievementType.total,
      criteria: {'game_type': 'flashCards', 'cards_studied': 100},
      xpReward: 300,
      maxProgress: 100,
    ),
    
    // Total achievements
    Achievement(
      id: 'total_games_10',
      title: '게임 초보자',
      description: '총 10게임을 완료하세요',
      iconPath: 'assets/images/achievements/total_10.png',
      type: AchievementType.total,
      criteria: {'total_games': 10},
      xpReward: 150,
      maxProgress: 10,
    ),
    Achievement(
      id: 'total_games_50',
      title: '게임 애호가',
      description: '총 50게임을 완료하세요',
      iconPath: 'assets/images/achievements/total_50.png',
      type: AchievementType.total,
      criteria: {'total_games': 50},
      xpReward: 500,
      maxProgress: 50,
    ),
    Achievement(
      id: 'total_games_100',
      title: '게임 마스터',
      description: '총 100게임을 완료하세요',
      iconPath: 'assets/images/achievements/total_100.png',
      type: AchievementType.total,
      criteria: {'total_games': 100},
      xpReward: 1000,
      maxProgress: 100,
    ),
    
    // Level achievements
    Achievement(
      id: 'level_5',
      title: '레벨 5 달성',
      description: '사용자 레벨 5에 도달하세요',
      iconPath: 'assets/images/achievements/level_5.png',
      type: AchievementType.level,
      criteria: {'user_level': 5},
      xpReward: 250,
      maxProgress: 5,
    ),
    Achievement(
      id: 'level_10',
      title: '레벨 10 달성',
      description: '사용자 레벨 10에 도달하세요',
      iconPath: 'assets/images/achievements/level_10.png',
      type: AchievementType.level,
      criteria: {'user_level': 10},
      xpReward: 750,
      maxProgress: 10,
    ),
    
    // Performance-based achievements
    Achievement(
      id: 'consistency_master',
      title: '일관성 마스터',
      description: '최근 5게임에서 정확도 편차가 5% 이하를 유지하세요',
      iconPath: 'assets/images/achievements/consistency.png',
      type: AchievementType.gameSpecific,
      criteria: {'consistency_threshold': 0.05},
      xpReward: 400,
      maxProgress: 1,
    ),
    Achievement(
      id: 'improvement_streak',
      title: '성장하는 마음',
      description: '최근 성과가 이전보다 5% 향상되었을 때',
      iconPath: 'assets/images/achievements/improvement.png',
      type: AchievementType.gameSpecific,
      criteria: {'improvement_threshold': 0.05},
      xpReward: 300,
      maxProgress: 1,
    ),
    Achievement(
      id: 'weekly_warrior',
      title: '주간 전사',
      description: '일주일 동안 10게임 이상 플레이하세요',
      iconPath: 'assets/images/achievements/weekly.png',
      type: AchievementType.total,
      criteria: {'weekly_games': 10},
      xpReward: 200,
      maxProgress: 10,
    ),
    Achievement(
      id: 'dual_nback_perfectionist',
      title: 'N-Back 완벽주의자',
      description: 'Dual N-Back에서 95% 이상의 정확도를 3회 달성하세요',
      iconPath: 'assets/images/achievements/perfectionist.png',
      type: AchievementType.gameSpecific,
      criteria: {'game_type': 'dualNBack', 'high_accuracy_count': 3, 'accuracy_threshold': 0.95},
      xpReward: 600,
      maxProgress: 3,
    ),
  ];

  List<Achievement> getAllAchievements() {
    return _defaultAchievements;
  }

  Future<List<Achievement>> getUserAchievements(String userId) async {
    final userAchievements = await _databaseService.getUserAchievements(userId);
    final allAchievements = getAllAchievements();
    
    return allAchievements.map((achievement) {
      final userAchievement = userAchievements.firstWhere(
        (ua) => ua.achievementId == achievement.id,
        orElse: () => UserAchievement(
          userId: '',
          achievementId: '',
          unlockedAt: DateTime.now(), // This will be ignored since userId is empty
          progress: 0,
        ),
      );
      
      if (userAchievement.userId.isNotEmpty) {
        return achievement.copyWith(
          isUnlocked: true,
          unlockedAt: userAchievement.unlockedAt,
          progress: userAchievement.progress,
        );
      } else {
        return achievement.copyWith(progress: 0);
      }
    }).toList();
  }

  Future<List<Achievement>> getRecentAchievements(String userId, {int limit = 5}) async {
    final userAchievements = await getUserAchievements(userId);
    final unlockedAchievements = userAchievements
        .where((a) => a.isUnlocked && a.unlockedAt != null)
        .toList();
    
    unlockedAchievements.sort((a, b) => b.unlockedAt!.compareTo(a.unlockedAt!));
    
    return unlockedAchievements.take(limit).toList();
  }

  Future<Map<String, int>> getAchievementProgress(String userId) async {
    final userStats = await _databaseService.getUserStats(userId);
    final gameSessions = await _databaseService.getGameSessions(userId);
    
    if (userStats == null) {
      return {};
    }
    
    final progress = <String, int>{};
    
    for (final achievement in _defaultAchievements) {
      progress[achievement.id] = _calculateProgress(achievement, userStats, gameSessions);
    }
    
    return progress;
  }

  int _calculateProgress(Achievement achievement, UserStats userStats, List<GameSession> sessions) {
    switch (achievement.type) {
      case AchievementType.streak:
        final streakDays = achievement.criteria['streak_days'] as int;
        return (userStats.currentStreak / streakDays * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
        
      case AchievementType.total:
        if (achievement.criteria.containsKey('total_games')) {
          final targetGames = achievement.criteria['total_games'] as int;
          return (userStats.totalGamesPlayed / targetGames * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
        }
        if (achievement.criteria.containsKey('cards_studied')) {
          // Calculate cards studied from flash card sessions
          final flashCardSessions = sessions.where((s) => s.gameType == GameType.flashCards).toList();
          final totalCardsStudied = flashCardSessions.fold<int>(0, (sum, session) {
            final cardsReviewed = session.gameSpecificData['cardsReviewed'] as int? ?? 0;
            return sum + cardsReviewed;
          });
          final targetCards = achievement.criteria['cards_studied'] as int;
          return (totalCardsStudied / targetCards * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
        }
        return 0;
        
      case AchievementType.level:
        if (achievement.criteria.containsKey('user_level')) {
          final targetLevel = achievement.criteria['user_level'] as int;
          return (userStats.level / targetLevel * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
        }
        if (achievement.criteria.containsKey('game_type') && achievement.criteria.containsKey('level')) {
          final gameType = GameType.values.firstWhere(
            (gt) => gt.name == achievement.criteria['game_type'],
            orElse: () => GameType.dualNBack,
          );
          final targetLevel = achievement.criteria['level'] as int;
          final gameStats = userStats.gameStats[gameType];
          if (gameStats != null) {
            return (gameStats.currentLevel / targetLevel * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
          }
        }
        return 0;
        
      case AchievementType.gameSpecific:
        if (achievement.criteria.containsKey('game_type')) {
          final gameType = GameType.values.firstWhere(
            (gt) => gt.name == achievement.criteria['game_type'],
            orElse: () => GameType.dualNBack,
          );
          
          if (achievement.criteria.containsKey('games_played')) {
            final gameStats = userStats.gameStats[gameType];
            final targetGames = achievement.criteria['games_played'] as int;
            final currentGames = gameStats?.gamesPlayed ?? 0;
            return (currentGames / targetGames * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
          }
          
          if (achievement.criteria.containsKey('accuracy')) {
            final targetAccuracy = achievement.criteria['accuracy'] as double;
            final gameStats = userStats.gameStats[gameType];
            if (gameStats != null && gameStats.averageAccuracy >= targetAccuracy) {
              return achievement.maxProgress;
            }
            return 0;
          }
          
          if (achievement.criteria.containsKey('decks_completed')) {
            // Check for flash card deck completion
            final flashCardSessions = sessions.where((s) => s.gameType == GameType.flashCards).toList();
            final completedDecks = flashCardSessions.fold<Set<String>>(
              <String>{},
              (decks, session) {
                final deckId = session.gameSpecificData['deckId'] as String?;
                final isCompleted = session.gameSpecificData['deckCompleted'] as bool? ?? false;
                if (deckId != null && isCompleted) {
                  decks.add(deckId);
                }
                return decks;
              },
            );
            return completedDecks.length;
          }
          
          if (achievement.criteria.containsKey('high_accuracy_count')) {
            // Count sessions with high accuracy
            final threshold = achievement.criteria['accuracy_threshold'] as double;
            final count = sessions.where((s) => 
              s.gameType == gameType && s.accuracy >= threshold
            ).length;
            return count;
          }
        }
        
        // Handle special achievements
        if (achievement.criteria.containsKey('consistency_threshold')) {
          final threshold = achievement.criteria['consistency_threshold'] as double;
          final recentSessions = sessions.take(5).toList();
          if (recentSessions.length >= 5) {
            final accuracies = recentSessions.map((s) => s.accuracy).toList();
            final variance = _calculateVariance(accuracies);
            return variance <= threshold ? achievement.maxProgress : 0;
          }
        }
        
        if (achievement.criteria.containsKey('improvement_threshold')) {
          final threshold = achievement.criteria['improvement_threshold'] as double;
          if (sessions.length >= 6) {
            final recent = sessions.take(3).toList();
            final older = sessions.skip(3).take(3).toList();
            final recentAvg = recent.fold<double>(0.0, (sum, s) => sum + s.accuracy) / recent.length;
            final olderAvg = older.fold<double>(0.0, (sum, s) => sum + s.accuracy) / older.length;
            return (recentAvg - olderAvg) >= threshold ? achievement.maxProgress : 0;
          }
        }
        
        if (achievement.criteria.containsKey('weekly_games')) {
          final targetGames = achievement.criteria['weekly_games'] as int;
          final now = DateTime.now();
          final weekAgo = now.subtract(const Duration(days: 7));
          final weeklyGames = sessions.where((s) => s.endTime.isAfter(weekAgo)).length;
          return (weeklyGames / targetGames * achievement.maxProgress).clamp(0, achievement.maxProgress).round();
        }
        
        return 0;
    }
  }

  Future<List<Achievement>> checkAchievements(String userId, GameSession? newSession) async {
    final userStats = await _databaseService.getUserStats(userId);
    final currentAchievements = await _databaseService.getUserAchievements(userId);
    final unlockedIds = currentAchievements.map((ua) => ua.achievementId).toSet();
    
    if (userStats == null) return [];
    
    final newlyUnlocked = <Achievement>[];
    final sessions = await _databaseService.getGameSessions(userId);
    
    for (final achievement in _defaultAchievements) {
      if (unlockedIds.contains(achievement.id)) continue;
      
      if (_isAchievementUnlocked(achievement, userStats, sessions)) {
        await _unlockAchievement(userId, achievement);
        newlyUnlocked.add(achievement);
      }
    }
    
    return newlyUnlocked;
  }

  bool _isAchievementUnlocked(Achievement achievement, UserStats userStats, List<GameSession> sessions) {
    final progress = _calculateProgress(achievement, userStats, sessions);
    return progress >= achievement.maxProgress;
  }

  Future<void> _unlockAchievement(String userId, Achievement achievement) async {
    final userAchievement = UserAchievement(
      userId: userId,
      achievementId: achievement.id,
      unlockedAt: DateTime.now(),
      progress: achievement.maxProgress,
    );
    
    await _databaseService.saveUserAchievement(userAchievement);
  }

  double _calculateVariance(List<double> values) {
    if (values.length < 2) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDiffs = values.map((v) => (v - mean) * (v - mean));
    return squaredDiffs.reduce((a, b) => a + b) / values.length;
  }
}