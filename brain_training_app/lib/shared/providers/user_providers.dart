import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/database_service.dart';
import '../../core/utils/extensions.dart';
import '../../shared/models/user.dart';
import '../../features/auth/auth_provider.dart' show authStateProvider, currentUserProvider;

final userServiceProvider = Provider<UserService>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return UserService(databaseService);
});

final userDataProvider = FutureProvider<User?>((ref) async {
  print('[DEBUG] userDataProvider - Starting to get user data');
  
  // Read the auth state directly to avoid circular dependency
  final authState = ref.watch(authStateProvider);
  
  return authState.when(
    data: (currentUser) async {
      print('[DEBUG] userDataProvider - Current user: $currentUser');
      
      if (currentUser == null) {
        print('[DEBUG] userDataProvider - Current user is null, returning null');
        return null;
      }
      
      final userService = ref.read(userServiceProvider);
      print('[DEBUG] userDataProvider - Getting user data for ID: ${currentUser.id}');
      try {
        final result = await userService.getUser(currentUser.id);
        print('[DEBUG] userDataProvider - User data result: $result');
        return result;
      } catch (e) {
        print('[ERROR] userDataProvider - Error getting user data: $e');
        return null;
      }
    },
    loading: () {
      print('[DEBUG] userDataProvider - Auth state loading, returning null');
      return null;
    },
    error: (error, stack) {
      print('[ERROR] userDataProvider - Auth state error: $error');
      return null;
    },
  );
});

final userStatsProvider = FutureProvider<UserStats?>((ref) async {
  print('[DEBUG] userStatsProvider - Starting to get user stats');
  final userData = ref.watch(userDataProvider);
  print('[DEBUG] userStatsProvider - User data: $userData');
  
  final result = userData.when(
    data: (user) {
      print('[DEBUG] userStatsProvider - User data loaded: $user');
      final stats = user?.stats;
      print('[DEBUG] userStatsProvider - User stats: $stats');
      return stats;
    },
    loading: () {
      print('[DEBUG] userStatsProvider - User data loading');
      return null;
    },
    error: (error, stack) {
      print('[DEBUG] userStatsProvider - User data error: $error');
      return null;
    },
  );
  print('[DEBUG] userStatsProvider - Returning: $result');
  return result;
});

final userPreferencesProvider = StateNotifierProvider<UserPreferencesNotifier, UserPreferences>((ref) {
  return UserPreferencesNotifier(ref);
});

class UserService {
  final DatabaseService _databaseService;

  UserService(this._databaseService);

  Future<User?> getUser(String userId) async {
    print('[DEBUG] UserService.getUser() - Getting user for ID: $userId');
    try {
      final result = await _databaseService.getUser(userId);
      print('[DEBUG] UserService.getUser() - Database result: $result');
      return result;
    } catch (e) {
      print('[ERROR] UserService.getUser() - Error: $e');
      rethrow;
    }
  }

  Future<void> saveUser(User user) async {
    await _databaseService.saveUser(user);
  }

  Future<void> updateUserStats(String userId, UserStats stats) async {
    await _databaseService.updateUserStats(userId, stats);
  }

  Future<void> updateUserPreferences(String userId, UserPreferences preferences) async {
    await _databaseService.updateUserPreferences(userId, preferences);
  }

  Future<User> createUserProfile(String userId, String email,
      {String? displayName, bool isGuest = false}) async {
    print(
        '[DEBUG] UserService.createUserProfile() - Creating profile for ID: $userId, email: $email');

    final user = User(
      id: userId,
      email: email,
      displayName: displayName,
      createdAt: DateTime.now(),
      stats: const UserStats(),
      preferences: const UserPreferences(),
      isGuest: isGuest,
    );

    print('[DEBUG] UserService.createUserProfile() - Created user object: $user');
    
    try {
      await saveUser(user);
      print('[DEBUG] UserService.createUserProfile() - User saved successfully');
      return user;
    } catch (e) {
      print('[ERROR] UserService.createUserProfile() - Error saving user: $e');
      rethrow;
    }
  }

  Future<UserStats> updateStatsAfterGame({
    required String userId,
    required UserStats currentStats,
    required int xpEarned,
    required bool isNewStreak,
  }) async {
    final now = DateTime.now();
    final lastPlayed = currentStats.lastGamePlayedAt;
    
    // Calculate streak
    int newStreak = currentStats.currentStreak;
    if (lastPlayed == null || isNewStreak) {
      if (lastPlayed != null && 
          now.difference(lastPlayed).inDays == 1) {
        newStreak += 1;
      } else if (lastPlayed == null || !lastPlayed.isToday) {
        newStreak = 1;
      }
    }

    // Calculate level
    final newTotalXP = currentStats.totalXP + xpEarned;
    final newLevel = _calculateLevel(newTotalXP);

    final updatedStats = currentStats.copyWith(
      totalGamesPlayed: currentStats.totalGamesPlayed + 1,
      currentStreak: newStreak,
      maxStreak: newStreak > currentStats.maxStreak ? newStreak : currentStats.maxStreak,
      totalXP: newTotalXP,
      level: newLevel,
      lastGamePlayedAt: now,
    );

    await updateUserStats(userId, updatedStats);
    return updatedStats;
  }

  int _calculateLevel(int totalXP) {
    for (int level = 10; level >= 1; level--) {
      final requiredXP = _getLevelXPRequirement(level);
      if (totalXP >= requiredXP) {
        return level;
      }
    }
    return 1;
  }

  int _getLevelXPRequirement(int level) {
    const requirements = {
      1: 0,
      2: 1000,
      3: 2500,
      4: 5000,
      5: 8500,
      6: 13000,
      7: 18500,
      8: 25000,
      9: 32500,
      10: 41000,
    };
    return requirements[level] ?? 0;
  }
}

class UserPreferencesNotifier extends StateNotifier<UserPreferences> {
  final Ref _ref;

  UserPreferencesNotifier(this._ref) : super(const UserPreferences()) {
    print('[DEBUG] UserPreferencesNotifier - Constructor called with default preferences');
    // Don't load preferences immediately to avoid circular dependencies
    // They will be loaded later when the user data is available
  }

  void loadUserPreferences(User userData) {
    print('[DEBUG] UserPreferencesNotifier.loadUserPreferences() - Loading preferences for user: ${userData.id}');
    state = userData.preferences;
  }

  void resetToDefaults() {
    print('[DEBUG] UserPreferencesNotifier.resetToDefaults() - Resetting to default preferences');
    state = const UserPreferences();
  }

  Future<void> updateLanguage(String language) async {
    final newPreferences = state.copyWith(language: language);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateSoundEnabled(bool enabled) async {
    final newPreferences = state.copyWith(soundEnabled: enabled);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateMusicEnabled(bool enabled) async {
    final newPreferences = state.copyWith(musicEnabled: enabled);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateNotificationsEnabled(bool enabled) async {
    final newPreferences = state.copyWith(notificationsEnabled: enabled);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateDailyReminderEnabled(bool enabled) async {
    final newPreferences = state.copyWith(dailyReminderEnabled: enabled);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateDailyReminderTime(TimeOfDay time) async {
    final newPreferences = state.copyWith(dailyReminderTime: time);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateDarkMode(bool isDarkMode) async {
    final newPreferences = state.copyWith(isDarkMode: isDarkMode);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateGameSpeed(double speed) async {
    final newPreferences = state.copyWith(gameSpeed: speed);
    await _updatePreferences(newPreferences);
  }

  Future<void> updateHapticFeedback(bool enabled) async {
    final newPreferences = state.copyWith(hapticFeedbackEnabled: enabled);
    await _updatePreferences(newPreferences);
  }

  Future<void> _updatePreferences(UserPreferences preferences) async {
    final currentUser = _ref.read(currentUserProvider);
    if (currentUser != null) {
      final userService = _ref.read(userServiceProvider);
      await userService.updateUserPreferences(currentUser.id, preferences);
      state = preferences;
    }
  }
}
