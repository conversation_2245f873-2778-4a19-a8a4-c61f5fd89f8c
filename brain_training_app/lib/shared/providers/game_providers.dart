import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/database_service.dart';
import '../../core/services/reward_service.dart';
import '../../shared/models/game_session.dart';
import '../../shared/models/user.dart';
import '../../core/constants/game_constants.dart';
import 'achievement_providers.dart';

final gameServiceProvider = Provider<GameService>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  final rewardService = ref.read(rewardServiceProvider);
  final achievementService = ref.read(achievementServiceProvider);
  return GameService(databaseService, rewardService, achievementService);
});

final gameSessionsProvider = FutureProvider.family<List<GameSession>, String>((ref, userId) async {
  final gameService = ref.read(gameServiceProvider);
  return gameService.getUserGameHistory(userId);
});

final gameSessionsByTypeProvider = FutureProvider.family<List<GameSession>, GameSessionsByTypeParams>((ref, params) async {
  final gameService = ref.read(gameServiceProvider);
  return gameService.getUserGameHistory(params.userId, gameType: params.gameType);
});

final currentGameSessionProvider = StateNotifierProvider.family<GameSessionNotifier, GameSessionState, GameType>((ref, gameType) {
  final gameService = ref.read(gameServiceProvider);
  return GameSessionNotifier(gameType, gameService);
});

class GameSessionsByTypeParams {
  final String userId;
  final GameType? gameType;

  GameSessionsByTypeParams({required this.userId, this.gameType});
}

class GameService {
  final DatabaseService _databaseService;
  final RewardService _rewardService;
  final AchievementService _achievementService;

  GameService(this._databaseService, this._rewardService, this._achievementService);

  Future<void> saveGameSession(GameSession session) async {
    await _databaseService.saveGameSession(session);
    
    // Award reward points for game completion
    if (session.isCompleted) {
      try {
        await _rewardService.awardGameCompletionBonus(
          userId: session.userId,
          score: session.score,
          gameType: session.gameType.name,
        );
      } catch (e) {
        print('Error awarding game completion bonus: $e');
        // Don't fail the game session save if reward fails
      }
      
      // Check for newly unlocked achievements
      try {
        final newAchievements = await _achievementService.checkAchievements(
          session.userId, 
          session,
        );
        
        if (newAchievements.isNotEmpty) {
          print('New achievements unlocked: ${newAchievements.map((a) => a.title).join(', ')}');
          // TODO: Trigger achievement notification/UI update
        }
      } catch (e) {
        print('Error checking achievements: $e');
        // Don't fail the game session save if achievement check fails
      }
    }
  }

  Future<List<GameSession>> getUserGameHistory(String userId, {GameType? gameType}) async {
    return _databaseService.getGameSessions(userId, gameType: gameType);
  }

  Future<UserProgress> calculateProgress(String userId) async {
    final sessions = await getUserGameHistory(userId);
    return UserProgress.fromSessions(sessions);
  }

  Future<Map<GameType, GameStats>> calculateGameStats(String userId) async {
    final stats = <GameType, GameStats>{};
    
    for (final gameType in GameType.values) {
      final sessions = await getUserGameHistory(userId, gameType: gameType);
      if (sessions.isNotEmpty) {
        // Sort sessions by date for trend analysis
        sessions.sort((a, b) => b.endTime.compareTo(a.endTime));
        
        final totalPlayTime = sessions.fold<Duration>(
          Duration.zero,
          (sum, session) => sum + session.duration,
        );
        
        final averageAccuracy = sessions.fold<double>(
          0.0,
          (sum, session) => sum + session.accuracy,
        ) / sessions.length;
        
        final bestScore = sessions.fold<int>(
          0,
          (max, session) => session.score > max ? session.score : max,
        );
        
        final totalXP = sessions.fold<int>(
          0,
          (sum, session) => sum + session.xpEarned,
        );

        // Calculate current level based on recent performance
        final currentLevel = _calculateCurrentLevel(sessions, gameType);
        
        // Calculate game-specific statistics
        final gameSpecificStats = _calculateGameSpecificStats(sessions, gameType);

        stats[gameType] = GameStats(
          gamesPlayed: sessions.length,
          bestScore: bestScore,
          averageAccuracy: averageAccuracy,
          currentLevel: currentLevel,
          totalPlayTime: totalPlayTime,
          lastPlayedAt: sessions.first.endTime,
          totalXP: totalXP,
          gameSpecificStats: gameSpecificStats,
        );
      }
    }
    
    return stats;
  }

  int _calculateCurrentLevel(List<GameSession> sessions, GameType gameType) {
    if (sessions.isEmpty) return 1;
    
    switch (gameType) {
      case GameType.dualNBack:
        // Use the most recent session's level for Dual N-Back
        return sessions.first.level;
      case GameType.flashCards:
        // For flash cards, calculate effective level based on performance
        final recentSessions = sessions.take(10).toList();
        final avgAccuracy = recentSessions.fold<double>(
          0.0, (sum, session) => sum + session.accuracy
        ) / recentSessions.length;
        return (avgAccuracy * 10).round().clamp(1, 10);
      case GameType.workingMemory:
      case GameType.attentionTraining:
        // Use average of recent levels
        final recentSessions = sessions.take(5).toList();
        final avgLevel = recentSessions.fold<double>(
          0.0, (sum, session) => sum + session.level
        ) / recentSessions.length;
        return avgLevel.round().clamp(1, 20);
    }
  }

  Map<String, dynamic> _calculateGameSpecificStats(List<GameSession> sessions, GameType gameType) {
    if (sessions.isEmpty) return {};
    
    final recentSessions = sessions.take(10).toList();
    final last30Days = sessions.where((s) => 
      DateTime.now().difference(s.endTime).inDays <= 30
    ).toList();
    
    final baseStats = {
      'accuracyTrend': _calculateTrend(recentSessions.map((s) => s.accuracy).toList()),
      'scoreTrend': _calculateTrend(recentSessions.map((s) => s.score.toDouble()).toList()),
      'last30DaysCount': last30Days.length,
      'last30DaysAvgAccuracy': last30Days.isEmpty ? 0.0 : 
        last30Days.fold<double>(0.0, (sum, s) => sum + s.accuracy) / last30Days.length,
      'averageSessionDuration': sessions.fold<Duration>(
        Duration.zero, (sum, s) => sum + s.duration
      ).inMinutes / sessions.length,
    };

    switch (gameType) {
      case GameType.dualNBack:
        return {
          ...baseStats,
          'maxNLevel': sessions.map((s) => s.level).reduce((a, b) => a > b ? a : b),
          'currentNLevel': sessions.first.level,
          'nLevelProgression': _calculateNLevelProgression(sessions),
        };
      case GameType.flashCards:
        return {
          ...baseStats,
          'cardsReviewed': _getCardsReviewedFromSessions(sessions),
          'retentionRate': _calculateRetentionRate(sessions),
        };
      case GameType.workingMemory:
      case GameType.attentionTraining:
        return {
          ...baseStats,
          'difficultyProgression': _calculateDifficultyProgression(sessions),
        };
    }
  }

  double _calculateTrend(List<double> values) {
    if (values.length < 2) return 0.0;
    
    // Simple linear regression slope calculation
    final n = values.length;
    final sumX = List.generate(n, (i) => i + 1).reduce((a, b) => a + b);
    final sumY = values.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => (i + 1) * values[i]).reduce((a, b) => a + b);
    final sumXX = List.generate(n, (i) => (i + 1) * (i + 1)).reduce((a, b) => a + b);
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope;
  }

  List<int> _calculateNLevelProgression(List<GameSession> sessions) {
    return sessions.map((s) => s.level).toList().reversed.toList();
  }

  int _getCardsReviewedFromSessions(List<GameSession> sessions) {
    return sessions.fold<int>(0, (sum, session) {
      final cardsReviewed = session.gameSpecificData['cardsReviewed'] as int? ?? 0;
      return sum + cardsReviewed;
    });
  }

  double _calculateRetentionRate(List<GameSession> sessions) {
    if (sessions.isEmpty) return 0.0;
    
    final totalCorrect = sessions.fold<int>(0, (sum, session) {
      final correct = session.gameSpecificData['correctAnswers'] as int? ?? 0;
      return sum + correct;
    });
    
    final totalAttempts = sessions.fold<int>(0, (sum, session) {
      final attempts = session.gameSpecificData['totalAttempts'] as int? ?? 0;
      return sum + attempts;
    });
    
    return totalAttempts > 0 ? totalCorrect / totalAttempts : 0.0;
  }

  List<double> _calculateDifficultyProgression(List<GameSession> sessions) {
    return sessions.map((s) => s.level.toDouble()).toList().reversed.toList();
  }
}

class GameSessionState {
  final GameType gameType;
  final bool isPlaying;
  final int currentLevel;
  final int score;
  final Duration elapsed;
  final List<GameEvent> events;
  final Map<String, dynamic> gameData;

  const GameSessionState({
    required this.gameType,
    this.isPlaying = false,
    this.currentLevel = 1,
    this.score = 0,
    this.elapsed = Duration.zero,
    this.events = const [],
    this.gameData = const {},
  });

  GameSessionState copyWith({
    GameType? gameType,
    bool? isPlaying,
    int? currentLevel,
    int? score,
    Duration? elapsed,
    List<GameEvent>? events,
    Map<String, dynamic>? gameData,
  }) {
    return GameSessionState(
      gameType: gameType ?? this.gameType,
      isPlaying: isPlaying ?? this.isPlaying,
      currentLevel: currentLevel ?? this.currentLevel,
      score: score ?? this.score,
      elapsed: elapsed ?? this.elapsed,
      events: events ?? this.events,
      gameData: gameData ?? this.gameData,
    );
  }
}

class GameSessionNotifier extends StateNotifier<GameSessionState> {
  final GameType _gameType;
  final GameService _gameService;
  DateTime? _sessionStartTime;

  GameSessionNotifier(this._gameType, this._gameService)
      : super(GameSessionState(gameType: _gameType));

  void startGame() {
    _sessionStartTime = DateTime.now();
    state = state.copyWith(
      isPlaying: true,
      score: 0,
      elapsed: Duration.zero,
      events: [],
      gameData: {},
    );
  }

  void updateScore(int newScore) {
    state = state.copyWith(score: newScore);
  }

  void updateLevel(int newLevel) {
    state = state.copyWith(currentLevel: newLevel);
  }

  void addEvent(GameEvent event) {
    state = state.copyWith(
      events: [...state.events, event],
    );
  }

  void updateGameData(Map<String, dynamic> data) {
    state = state.copyWith(
      gameData: {...state.gameData, ...data},
    );
  }

  void updateElapsed(Duration elapsed) {
    state = state.copyWith(elapsed: elapsed);
  }

  Future<void> endGame(String userId, double accuracy) async {
    if (!state.isPlaying || _sessionStartTime == null) return;

    final endTime = DateTime.now();
    final sessionId = '${userId}_${_gameType.name}_${endTime.millisecondsSinceEpoch}';

    final session = GameSession(
      id: sessionId,
      userId: userId,
      gameType: _gameType,
      level: state.currentLevel,
      accuracy: accuracy,
      duration: state.elapsed,
      score: state.score,
      startTime: _sessionStartTime!,
      endTime: endTime,
      gameSpecificData: state.gameData,
      xpEarned: _calculateXP(),
      isCompleted: true,
      events: state.events,
    );

    await _gameService.saveGameSession(session);

    state = state.copyWith(isPlaying: false);
  }

  int _calculateXP() {
    final baseXP = GameConstants.baseXpRewards[_gameType] ?? 100;
    final levelMultiplier = state.currentLevel * 0.1;
    final scoreMultiplier = (state.score / 1000).clamp(0.1, 2.0);
    
    return (baseXP * (1 + levelMultiplier) * scoreMultiplier).round();
  }
}

class UserProgress {
  final int totalGames;
  final Duration totalPlayTime;
  final double averageAccuracy;
  final Map<GameType, int> gamesCounts;
  final List<GameSession> recentSessions;
  final PerformanceInsights insights;
  final Map<String, dynamic> trends;

  UserProgress({
    required this.totalGames,
    required this.totalPlayTime,
    required this.averageAccuracy,
    required this.gamesCounts,
    required this.recentSessions,
    required this.insights,
    required this.trends,
  });

  factory UserProgress.fromSessions(List<GameSession> sessions) {
    final gamesCounts = <GameType, int>{};
    var totalPlayTime = Duration.zero;
    var totalAccuracy = 0.0;

    // Sort sessions by date for trend analysis
    sessions.sort((a, b) => b.endTime.compareTo(a.endTime));

    for (final session in sessions) {
      gamesCounts[session.gameType] = (gamesCounts[session.gameType] ?? 0) + 1;
      totalPlayTime += session.duration;
      totalAccuracy += session.accuracy;
    }

    final averageAccuracy = sessions.isEmpty ? 0.0 : totalAccuracy / sessions.length;
    final recentSessions = sessions.take(10).toList();
    
    // Calculate performance insights
    final insights = _calculatePerformanceInsights(sessions);
    
    // Calculate trends
    final trends = _calculateTrends(sessions);

    return UserProgress(
      totalGames: sessions.length,
      totalPlayTime: totalPlayTime,
      averageAccuracy: averageAccuracy,
      gamesCounts: gamesCounts,
      recentSessions: recentSessions,
      insights: insights,
      trends: trends,
    );
  }

  static PerformanceInsights _calculatePerformanceInsights(List<GameSession> sessions) {
    if (sessions.isEmpty) {
      return PerformanceInsights(
        strengths: [],
        improvements: [],
        recommendations: [],
        streakInfo: StreakInfo(current: 0, longest: 0, lastPlayed: null),
      );
    }

    final strengths = <String>[];
    final improvements = <String>[];
    final recommendations = <String>[];

    // Analyze performance by game type
    final gameStats = <GameType, List<GameSession>>{};
    for (final session in sessions) {
      gameStats[session.gameType] = (gameStats[session.gameType] ?? [])..add(session);
    }

    // Find strengths and areas for improvement
    gameStats.forEach((gameType, gameSessions) {
      final avgAccuracy = gameSessions.fold<double>(0.0, (sum, s) => sum + s.accuracy) / gameSessions.length;
      
      if (avgAccuracy >= 0.8) {
        strengths.add('Strong performance in ${gameType.name} (${(avgAccuracy * 100).toStringAsFixed(1)}% accuracy)');
      } else if (avgAccuracy < 0.6) {
        improvements.add('Focus on ${gameType.name} accuracy (${(avgAccuracy * 100).toStringAsFixed(1)}% current)');
        recommendations.add('Practice ${gameType.name} for 10-15 minutes daily to improve accuracy');
      }
    });

    // Analyze consistency
    final recentSessions = sessions.take(5).toList();
    if (recentSessions.length >= 3) {
      final accuracyVariance = _calculateVariance(recentSessions.map((s) => s.accuracy).toList());
      if (accuracyVariance < 0.01) {
        strengths.add('Consistent performance across recent sessions');
      } else if (accuracyVariance > 0.05) {
        improvements.add('Work on performance consistency');
        recommendations.add('Try shorter, more frequent practice sessions');
      }
    }

    // Calculate streak information
    final streakInfo = _calculateStreakInfo(sessions);

    // Add streak-based recommendations
    if (streakInfo.current == 0) {
      recommendations.add('Start a daily practice streak to build cognitive momentum');
    } else if (streakInfo.current >= 7) {
      strengths.add('Great consistency with ${streakInfo.current}-day streak!');
    }

    return PerformanceInsights(
      strengths: strengths,
      improvements: improvements,
      recommendations: recommendations,
      streakInfo: streakInfo,
    );
  }

  static Map<String, dynamic> _calculateTrends(List<GameSession> sessions) {
    if (sessions.length < 2) return {};

    final last30Days = sessions.where((s) => 
      DateTime.now().difference(s.endTime).inDays <= 30
    ).toList();

    final last7Days = sessions.where((s) => 
      DateTime.now().difference(s.endTime).inDays <= 7
    ).toList();

    return {
      'weeklyGames': last7Days.length,
      'monthlyGames': last30Days.length,
      'weeklyAvgAccuracy': last7Days.isEmpty ? 0.0 : 
        last7Days.fold<double>(0.0, (sum, s) => sum + s.accuracy) / last7Days.length,
      'monthlyAvgAccuracy': last30Days.isEmpty ? 0.0 : 
        last30Days.fold<double>(0.0, (sum, s) => sum + s.accuracy) / last30Days.length,
      'isImproving': _isPerformanceImproving(sessions),
      'mostPlayedGame': _getMostPlayedGame(last30Days),
      'averageSessionLength': last30Days.isEmpty ? 0.0 : 
        last30Days.fold<Duration>(Duration.zero, (sum, s) => sum + s.duration).inMinutes / last30Days.length,
    };
  }

  static double _calculateVariance(List<double> values) {
    if (values.length < 2) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDiffs = values.map((v) => (v - mean) * (v - mean));
    return squaredDiffs.reduce((a, b) => a + b) / values.length;
  }

  static StreakInfo _calculateStreakInfo(List<GameSession> sessions) {
    if (sessions.isEmpty) {
      return StreakInfo(current: 0, longest: 0, lastPlayed: null);
    }

    // Group sessions by date
    final sessionsByDate = <DateTime, List<GameSession>>{};
    for (final session in sessions) {
      final date = DateTime(session.endTime.year, session.endTime.month, session.endTime.day);
      sessionsByDate[date] = (sessionsByDate[date] ?? [])..add(session);
    }

    final uniqueDates = sessionsByDate.keys.toList()..sort((a, b) => b.compareTo(a));
    
    int currentStreak = 0;
    int longestStreak = 0;
    int tempStreak = 0;

    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    final yesterday = todayDate.subtract(const Duration(days: 1));

    // Calculate current streak
    DateTime? checkDate = uniqueDates.contains(todayDate) ? todayDate : yesterday;
    
    for (int i = 0; i < uniqueDates.length; i++) {
      if (checkDate != null && uniqueDates[i] == checkDate) {
        currentStreak++;
        checkDate = checkDate!.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }

    // Calculate longest streak
    for (int i = 0; i < uniqueDates.length; i++) {
      if (i == 0 || uniqueDates[i].difference(uniqueDates[i-1]).inDays == -1) {
        tempStreak++;
        longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
      } else {
        tempStreak = 1;
      }
    }

    return StreakInfo(
      current: currentStreak,
      longest: longestStreak,
      lastPlayed: sessions.first.endTime,
    );
  }

  static bool _isPerformanceImproving(List<GameSession> sessions) {
    if (sessions.length < 6) return false;

    final recent = sessions.take(3).toList();
    final older = sessions.skip(3).take(3).toList();

    final recentAvg = recent.fold<double>(0.0, (sum, s) => sum + s.accuracy) / recent.length;
    final olderAvg = older.fold<double>(0.0, (sum, s) => sum + s.accuracy) / older.length;

    return recentAvg > olderAvg + 0.05; // 5% improvement threshold
  }

  static String _getMostPlayedGame(List<GameSession> sessions) {
    if (sessions.isEmpty) return 'None';

    final gameCounts = <GameType, int>{};
    for (final session in sessions) {
      gameCounts[session.gameType] = (gameCounts[session.gameType] ?? 0) + 1;
    }

    final mostPlayed = gameCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
    return mostPlayed.key.name;
  }
}

class PerformanceInsights {
  final List<String> strengths;
  final List<String> improvements;
  final List<String> recommendations;
  final StreakInfo streakInfo;

  PerformanceInsights({
    required this.strengths,
    required this.improvements,
    required this.recommendations,
    required this.streakInfo,
  });
}

class StreakInfo {
  final int current;
  final int longest;
  final DateTime? lastPlayed;

  StreakInfo({
    required this.current,
    required this.longest,
    this.lastPlayed,
  });
}