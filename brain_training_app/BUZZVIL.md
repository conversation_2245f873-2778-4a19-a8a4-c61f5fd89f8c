# Buzzvil SDK 연동 및 BenefitHub 구현 계획

## 1. Buzzvil SDK 설치 (완료)

- `settings.gradle.kts`에 버즈빌 Maven 저장소 추가
- `app/build.gradle.kts`에 Buzzvil SDK 라이브러리 추가 및 `compileSdk`, `targetSdk` 버전 34로 업데이트

## 2. SDK 초기화 (완료)

- `MainApplication.kt` 파일을 생성하여 `Application` 클래스를 상속받고, `onCreate`에서 `BuzzvilSdk.initialize()` 호출
- `AndroidManifest.xml`에 `MainApplication` 등록

## 3. BenefitHub 구현

### 3.1. 네이티브 연동 (Android)

- `MainActivity.kt`에 `MethodChannel`을 설정 (채널명: `com.windroamer.neuroplasticity/buzzvil`)
- `showBenefitHub` 메소드를 구현하여 `BuzzBenefitHub.show(context)` 호출
- `login` 메소드를 구현하여 Flutter에서 전달받은 유저 정보로 `BuzzvilSdk.login()` 호출

### 3.2. Flutter 연동

- `AppUser` 모델(`lib/core/models/app_user.dart`)에 `birthYear` (int?), `gender` (String?) 필드 추가
- `build_runner`를 실행하여 `freezed` 파일 재생성
- `home_screen.dart`에 `MethodChannel`을 통해 네이티브 기능을 호출하는 로직 추가
  - `initState`에서 `_loginToBuzzvil` 메소드를 호출하여 Buzzvil 로그인
  - "포인트 받기" UI를 추가하고, 탭하면 `_showBenefitHub` 메소드를 호출

## 4. 남은 작업 및 확인 사항

- `MainApplication.kt`의 `YOUR_APP_ID`를 실제 발급받은 App ID로 교체
- 회원가입 또는 프로필 수정 페이지에서 `birthYear`와 `gender` 정보를 입력받아 `AppUser` 객체에 저장하는 기능 구현
- 실제 기기에서 테스트 진행
