package com.windroamer.neuroplasticity

import android.app.Application
import com.buzzvil.buzzbenefit.BuzzBenefitConfig
import com.buzzvil.sdk.BuzzvilSdk

class MainApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        // BuzzBenefit 설정
        val buzzBenefitConfig = BuzzBenefitConfig.Builder("test-app-id")
            .build()
        // Buzzvil SDK 초기화
        BuzzvilSdk.initialize(
            application = this@MainApplication,
            buzzBenefitConfig = buzzBenefitConfig
        )
    }
}
