package com.windroamer.neuroplasticity

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.buzzvil.buzzbenefit.benefithub.BuzzBenefitHub
import com.buzzvil.sdk.BuzzvilSdk
import com.buzzvil.sdk.BuzzvilSdkUser
import com.buzzvil.sdk.BuzzvilSdkLoginListener

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.windroamer.neuroplasticity/buzzvil"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showBenefitHub" -> {
                    BuzzBenefitHub.show(this)
                    result.success(null)
                }
                "login" -> {
                    val userId = call.argument<String>("userId")
                    val birthYear = call.argument<Int>("birthYear")
                    val genderStr = call.argument<String>("gender")

                    if (userId == null) {
                        result.error("INVALID_ARGUMENT", "userId is required.", null)
                        return@setMethodCallHandler
                    }

                    val gender = when (genderStr) {
                        "MALE" -> BuzzvilSdkUser.Gender.MALE
                        "FEMALE" -> BuzzvilSdkUser.Gender.FEMALE
                        else -> null
                    }

                    val buzzvilSdkUser = BuzzvilSdkUser(
                        userId = userId,
                        birthYear = birthYear ?: 0,
                        gender = gender ?: BuzzvilSdkUser.Gender.MALE
                    )

                    BuzzvilSdk.login(buzzvilSdkUser, object : BuzzvilSdkLoginListener {
                        override fun onSuccess() {
                            result.success(true)
                        }

                        override fun onFailure(errorType: BuzzvilSdkLoginListener.ErrorType) {
                            result.error("LOGIN_FAILED", errorType.name, null)
                        }
                    })
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
