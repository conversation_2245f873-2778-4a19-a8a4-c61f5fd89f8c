# Brain Training App - 두뇌 훈련 앱

과학적 근거 기반 두뇌 훈련 앱입니다. Dual N-back, Flash card 등의 인지 훈련 게임을 제공하며, Firebase를 통한 사용자 관리와 SQLite를 통한 로컬 데이터 저장을 지원합니다.

## 📋 목차

- [기능](#기능)
- [기술 스택](#기술-스택)
- [프로젝트 구조](#프로젝트-구조)
- [설치 및 실행](#설치-및-실행)
- [개발 환경 설정](#개발-환경-설정)
- [빌드 및 배포](#빌드-및-배포)
- [현재 상태](#현재-상태)
- [개발 로드맵](#개발-로드맵)

## 🎯 기능

### ✅ 구현 완료
- **사용자 인증**: Firebase Auth를 통한 이메일/게스트 로그인
- **사용자 프로필**: 통계 및 설정 관리
- **테마 지원**: 라이트/다크 모드
- **데이터베이스**: SQLite 기반 로컬 데이터 저장
- **상태 관리**: Riverpod를 통한 반응형 상태 관리

### 🚧 개발 예정
- **Dual N-Back 게임**: 시각적/청각적 자극 기반 워킹메모리 훈련
- **Flash Cards 시스템**: 간격 반복 학습 알고리즘
- **작업기억 훈련**: 숫자/패턴 기억 게임
- **주의력 훈련**: 선택적 주의력 향상 게임
- **성취 시스템**: 배지 및 레벨 시스템
- **리더보드**: 일간/주간/월간 순위
- **다국어 지원**: 한국어, 영어, 일본어, 중국어

## 🛠 기술 스택

- **Frontend**: Flutter (Dart)
- **State Management**: Riverpod
- **Authentication**: Firebase Auth
- **Database**: SQLite (로컬), Firebase (서버 동기화 예정)
- **Code Generation**: Freezed, json_annotation
- **UI**: Material Design 3

## 📁 프로젝트 구조

```
lib/
├── core/                           # 핵심 기능
│   ├── constants/                  # 상수 정의
│   │   ├── app_constants.dart
│   │   └── game_constants.dart
│   ├── models/                     # 핵심 모델
│   │   └── app_user.dart
│   ├── services/                   # 서비스 계층
│   │   ├── auth_service.dart
│   │   └── database_service.dart
│   ├── themes/                     # UI 테마
│   │   └── app_theme.dart
│   └── utils/                      # 유틸리티
│       ├── extensions.dart
│       └── validators.dart
├── features/                       # 기능별 모듈
│   ├── auth/                       # 인증
│   │   ├── auth_provider.dart
│   │   └── auth_screen.dart
│   ├── games/                      # 게임들
│   │   ├── dual_n_back/
│   │   ├── flash_cards/
│   │   ├── working_memory/
│   │   └── attention_training/
│   └── profile/                    # 프로필
│       └── profile_screen.dart
├── shared/                         # 공통 요소
│   ├── models/                     # 데이터 모델
│   │   ├── user.dart
│   │   ├── game_session.dart
│   │   ├── achievement.dart
│   │   └── flash_card.dart
│   ├── providers/                  # 상태 관리
│   │   ├── user_providers.dart
│   │   └── game_providers.dart
│   └── widgets/                    # 공통 위젯
│       ├── home_screen.dart
│       └── loading_screen.dart
└── main.dart                       # 앱 진입점
```

## 🚀 설치 및 실행

### 1. 사전 요구사항

- Flutter SDK (3.10.0 이상)
- Dart SDK (3.0.0 이상)
- Android Studio / VS Code
- Firebase 프로젝트 설정

### 2. 프로젝트 클론 및 의존성 설치

```bash
# 프로젝트 클론
git clone <repository-url>
cd brain_training_app

# 의존성 설치
flutter pub get

# 코드 생성 실행
flutter packages pub run build_runner build
```

### 3. Firebase 설정

1. [Firebase Console](https://console.firebase.google.com/)에서 새 프로젝트 생성
2. Authentication 활성화 (이메일/비밀번호, 익명 인증)
3. Firebase CLI 설치 및 설정:

```bash
# Firebase CLI 설치
npm install -g firebase-tools

# Firebase 로그인
firebase login

# Flutter용 Firebase 설정
dart pub global activate flutterfire_cli
flutterfire configure
```

### 4. 앱 실행

```bash
# 개발 모드로 실행
flutter run

# 또는 특정 디바이스에서 실행
flutter run -d <device-id>
```

## ⚙️ 개발 환경 설정

### 코드 생성

모델 클래스 수정 후 코드 재생성:

```bash
# 코드 생성 (일회성)
flutter packages pub run build_runner build

# 코드 생성 (변경사항 감지)
flutter packages pub run build_runner watch

# 기존 생성파일 삭제 후 재생성
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 코드 분석

```bash
# 코드 분석
flutter analyze

# 포맷팅
flutter format .

# 테스트 실행
flutter test
```

## 📦 빌드 및 배포

### Android APK 빌드

```bash
# Debug APK
flutter build apk

# Release APK
flutter build apk --release

# AAB (Play Store 배포용)
flutter build appbundle --release
```

### iOS 빌드

```bash
# iOS 빌드 (macOS에서만 가능)
flutter build ios --release
```

### 웹 빌드

```bash
# 웹 빌드
flutter build web
```

## 📊 현재 상태

### ✅ 완료된 기능

- [x] 프로젝트 구조 설정
- [x] Firebase Auth 연동
- [x] 사용자 데이터 모델
- [x] SQLite 데이터베이스
- [x] Riverpod 상태 관리
- [x] 기본 UI 구조
- [x] 테마 시스템
- [x] 로그인/회원가입 화면
- [x] 홈 화면
- [x] 프로필 화면

### 🔧 알려진 이슈

현재 몇 가지 컴파일 에러가 있으며 다음 작업이 필요합니다:

1. **타입 에러 수정**: UserStats, GameStats 타입 참조 수정
2. **확장 메서드 추가**: DateTime.isToday 구현
3. **withOpacity 업데이트**: deprecated 메서드를 withValues로 변경
4. **테스트 파일 수정**: MyApp 클래스 참조 업데이트

## 🗺 개발 로드맵

### Phase 1: 핵심 게임 구현 (우선순위 높음)
- [ ] Dual N-Back 게임 완전 구현
- [ ] Flash Cards 시스템 및 간격 반복 알고리즘
- [ ] 컴파일 에러 모두 수정
- [ ] 기본 성취 시스템

### Phase 2: 게임 확장 (우선순위 중간)
- [ ] 작업기억 훈련 게임
- [ ] 주의력 훈련 게임
- [ ] 리더보드 시스템
- [ ] 상세 통계 페이지

### Phase 3: 고급 기능 (우선순위 낮음)
- [ ] 버즈빌 SDK 연동
- [ ] 다국어 지원 (i18n)
- [ ] 오프라인 모드 최적화
- [ ] 사용자 정의 테마

### Phase 4: 출시 준비
- [ ] 통합 테스트 작성
- [ ] 성능 최적화
- [ ] 앱스토어 준비 (아이콘, 스크린샷 등)
- [ ] 베타 테스트

## 🤝 기여하기

1. 이슈 생성 또는 기존 이슈 확인
2. 브랜치 생성: `git checkout -b feature/새기능`
3. 변경사항 커밋: `git commit -m '새기능 추가'`
4. 브랜치 푸시: `git push origin feature/새기능`
5. Pull Request 생성

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 제공됩니다.

## 📞 문의

- 이메일: <EMAIL>
- 이슈 트래커: GitHub Issues 사용

---

**참고**: 이 앱은 과학적 연구를 바탕으로 한 두뇌 훈련 도구이며, 의료용 목적이 아닌 교육 및 엔터테인먼트 용도입니다.
