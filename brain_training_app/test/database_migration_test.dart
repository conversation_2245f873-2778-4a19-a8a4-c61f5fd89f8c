import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart' as path;
import 'dart:io';
import 'dart:convert';

import '../lib/core/services/database_migration_manager.dart';
import '../lib/core/services/database_backup_service.dart';
import '../lib/core/services/database_integrity_service.dart';

class DatabaseMigrationTestFramework {
  static late Database _testDb;
  static late DatabaseMigrationManager _migrationManager;
  static late DatabaseIntegrityService _integrityService;
  
  static Future<void> setUpAll() async {
    // Initialize test framework
    TestWidgetsFlutterBinding.ensureInitialized();
    
    // Initialize FFI for testing
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
    
    _migrationManager = DatabaseMigrationManager();
    _integrityService = DatabaseIntegrityService();
    
    // Register all migrations for testing
    MigrationRegistry.registerAllMigrations(_migrationManager);
  }
  
  static Future<Database> createTestDatabase({int version = 1}) async {
    final tempDir = await Directory.systemTemp.createTemp('db_test_');
    final dbPath = path.join(tempDir.path, 'test_brain_training.db');
    
    return await openDatabase(
      dbPath,
      version: version,
      onCreate: (db, version) async {
        await _createInitialSchema(db);
      },
    );
  }
  
  static Future<void> _createInitialSchema(Database db) async {
    // Create version 1 schema
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        display_name TEXT,
        photo_url TEXT,
        created_at INTEGER NOT NULL,
        last_login_at INTEGER,
        is_guest INTEGER DEFAULT 0,
        stats TEXT NOT NULL,
        preferences TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE game_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        game_type TEXT NOT NULL,
        level INTEGER NOT NULL,
        accuracy REAL NOT NULL,
        duration INTEGER NOT NULL,
        score INTEGER NOT NULL,
        start_time INTEGER NOT NULL,
        end_time INTEGER NOT NULL,
        game_specific_data TEXT NOT NULL,
        xp_earned INTEGER DEFAULT 0,
        is_completed INTEGER DEFAULT 0,
        events TEXT DEFAULT '[]',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE achievements (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        icon_path TEXT NOT NULL,
        type TEXT NOT NULL,
        criteria TEXT NOT NULL,
        xp_reward INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE user_achievements (
        user_id TEXT NOT NULL,
        achievement_id TEXT NOT NULL,
        unlocked_at INTEGER NOT NULL,
        progress INTEGER DEFAULT 0,
        PRIMARY KEY (user_id, achievement_id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (achievement_id) REFERENCES achievements (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE card_decks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER,
        is_active INTEGER DEFAULT 1,
        daily_goal INTEGER DEFAULT 20,
        settings TEXT DEFAULT '{}',
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE flash_cards (
        id TEXT PRIMARY KEY,
        deck_id TEXT NOT NULL,
        front TEXT NOT NULL,
        back TEXT NOT NULL,
        hint TEXT,
        tags TEXT DEFAULT '[]',
        created_at INTEGER NOT NULL,
        updated_at INTEGER,
        review TEXT NOT NULL,
        FOREIGN KEY (deck_id) REFERENCES card_decks (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE card_study_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        deck_id TEXT NOT NULL,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        reviews TEXT DEFAULT '[]',
        cards_reviewed INTEGER DEFAULT 0,
        correct_answers INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (deck_id) REFERENCES card_decks (id)
      )
    ''');
  }
  
  static Future<void> seedTestData(Database db) async {
    // Insert test user
    await db.insert('users', {
      'id': 'test_user_1',
      'email': '<EMAIL>',
      'display_name': 'Test User',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'last_login_at': DateTime.now().millisecondsSinceEpoch,
      'is_guest': 0,
      'stats': jsonEncode({"totalGamesPlayed": 5, "currentStreak": 3, "maxStreak": 5}),
      'preferences': jsonEncode({"theme": "light", "soundEnabled": true}),
    });
    
    // Insert test game session
    await db.insert('game_sessions', {
      'id': 'session_1',
      'user_id': 'test_user_1',
      'game_type': 'dualNBack',
      'level': 2,
      'accuracy': 0.85,
      'duration': 300000,
      'score': 850,
      'start_time': DateTime.now().subtract(Duration(minutes: 5)).millisecondsSinceEpoch,
      'end_time': DateTime.now().millisecondsSinceEpoch,
      'game_specific_data': jsonEncode({"nLevel": 2, "correctResponses": 17}),
      'xp_earned': 50,
      'is_completed': 1,
      'events': '[]',
    });
    
    // Insert test deck and cards
    await db.insert('card_decks', {
      'id': 'deck_1',
      'user_id': 'test_user_1',
      'name': 'Test Deck',
      'description': 'A test deck for migration testing',
      'category': 'test',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'is_active': 1,
      'daily_goal': 20,
      'settings': '{}',
    });
    
    await db.insert('flash_cards', {
      'id': 'card_1',
      'deck_id': 'deck_1',
      'front': 'Test Question',
      'back': 'Test Answer',
      'hint': 'Test Hint',
      'tags': jsonEncode(["test"]),
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'review': jsonEncode({"easeFactor": 2.5, "interval": 1, "repetitions": 0}),
    });
  }
  
  static Future<void> tearDown() async {
    try {
      if (_testDb.isOpen) {
        await _testDb.close();
      }
    } catch (e) {
      print('Error closing test database: $e');
    }
  }
}

void main() {
  group('Database Migration Tests', () {
    setUpAll(() async {
      await DatabaseMigrationTestFramework.setUpAll();
    });
    
    tearDown(() async {
      await DatabaseMigrationTestFramework.tearDown();
    });

    group('Version 1 to 2 Migration', () {
      test('should successfully migrate from version 1 to 2', () async {
        // Create version 1 database
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        // Verify initial state
        final initialVersion = await db.rawQuery('PRAGMA user_version');
        expect(initialVersion.first['user_version'], equals(1));
        
        // Perform migration
        final migrationManager = DatabaseMigrationManager();
        MigrationRegistry.registerAllMigrations(migrationManager);
        
        await migrationManager.performMigration(db, 1, 2);
        
        // Verify migration result
        final newVersion = await db.rawQuery('PRAGMA user_version');
        expect(newVersion.first['user_version'], equals(2));
        
        // Check if new tables exist
        final rewardPointsTable = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='reward_points'"
        );
        expect(rewardPointsTable.isNotEmpty, isTrue);
        
        final rewardTransactionsTable = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='reward_transactions'"
        );
        expect(rewardTransactionsTable.isNotEmpty, isTrue);
        
        // Verify data integrity after migration
        final integrityService = DatabaseIntegrityService();
        final integrityResult = await integrityService.performFullIntegrityCheck(db);
        expect(integrityResult.isValid, isTrue);
        
        await db.close();
      });
      
      test('should preserve existing data during migration', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        // Count existing data
        final userCountBefore = await db.rawQuery('SELECT COUNT(*) as count FROM users');
        final sessionCountBefore = await db.rawQuery('SELECT COUNT(*) as count FROM game_sessions');
        final cardCountBefore = await db.rawQuery('SELECT COUNT(*) as count FROM flash_cards');
        
        // Perform migration
        final migrationManager = DatabaseMigrationManager();
        MigrationRegistry.registerAllMigrations(migrationManager);
        await migrationManager.performMigration(db, 1, 2);
        
        // Verify data preservation
        final userCountAfter = await db.rawQuery('SELECT COUNT(*) as count FROM users');
        final sessionCountAfter = await db.rawQuery('SELECT COUNT(*) as count FROM game_sessions');
        final cardCountAfter = await db.rawQuery('SELECT COUNT(*) as count FROM flash_cards');
        
        expect(userCountAfter.first['count'], equals(userCountBefore.first['count']));
        expect(sessionCountAfter.first['count'], equals(sessionCountBefore.first['count']));
        expect(cardCountAfter.first['count'], equals(cardCountBefore.first['count']));
        
        await db.close();
      });
    });

    group('Version 2 to 3 Migration', () {
      test('should add performance indexes', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        // Migrate to version 3
        final migrationManager = DatabaseMigrationManager();
        MigrationRegistry.registerAllMigrations(migrationManager);
        await migrationManager.performMigration(db, 1, 3);
        
        // Check if indexes were created
        final indexes = await db.rawQuery('''
          SELECT name FROM sqlite_master 
          WHERE type='index' AND name IN (
            'idx_game_sessions_user_id',
            'idx_game_sessions_timestamp',
            'idx_flash_cards_deck_id',
            'idx_reward_points_user_id'
          )
        ''');
        
        expect(indexes.length, equals(4));
        
        await db.close();
      });
    });

    group('Version 3 to 4 Migration', () {
      test('should add full-text search support', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        // Migrate to version 4
        final migrationManager = DatabaseMigrationManager();
        MigrationRegistry.registerAllMigrations(migrationManager);
        await migrationManager.performMigration(db, 1, 4);
        
        // Check if FTS table was created
        final ftsTable = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='flash_cards_fts'"
        );
        expect(ftsTable.isNotEmpty, isTrue);
        
        // Check if triggers were created
        final triggers = await db.rawQuery('''
          SELECT name FROM sqlite_master 
          WHERE type='trigger' AND name LIKE 'flash_cards_fts_%'
        ''');
        expect(triggers.length, equals(3));
        
        // Test FTS functionality
        final searchResults = await db.rawQuery(
          "SELECT * FROM flash_cards_fts WHERE flash_cards_fts MATCH 'Test'"
        );
        expect(searchResults.isNotEmpty, isTrue);
        
        await db.close();
      });
    });

    group('Migration Rollback Tests', () {
      test('should rollback on migration failure', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        // Create a migration manager with a failing migration
        final migrationManager = DatabaseMigrationManager();
        migrationManager.registerMigration(2, (db) async {
          throw Exception('Simulated migration failure');
        });
        
        // Attempt migration and expect failure
        expect(
          () => migrationManager.performMigration(db, 1, 2),
          throwsA(isA<MigrationException>()),
        );
        
        // Verify database is still at version 1
        final version = await db.rawQuery('PRAGMA user_version');
        expect(version.first['user_version'], equals(1));
        
        // Verify data integrity
        final userCount = await db.rawQuery('SELECT COUNT(*) as count FROM users');
        expect(userCount.first['count'], equals(1));
        
        await db.close();
      });
    });

    group('Data Integrity Tests', () {
      test('should detect and report integrity issues', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 2);
        
        // Create some invalid data
        await db.insert('game_sessions', {
          'id': 'invalid_session',
          'user_id': 'non_existent_user',
          'game_type': 'test',
          'level': 1,
          'accuracy': -0.5, // Invalid accuracy
          'duration': -1000, // Invalid duration
          'score': 0,
          'start_time': DateTime.now().add(Duration(hours: 1)).millisecondsSinceEpoch,
          'end_time': DateTime.now().millisecondsSinceEpoch,
          'game_specific_data': '{}',
        });
        
        final integrityService = DatabaseIntegrityService();
        final result = await integrityService.performFullIntegrityCheck(db);
        
        expect(result.isValid, isFalse);
        expect(result.errors.isNotEmpty, isTrue);
        expect(result.warnings.isNotEmpty, isTrue);
        
        await db.close();
      });
    });

    group('Backup and Recovery Tests', () {
      test('should create and restore backups', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        await DatabaseMigrationTestFramework.seedTestData(db);
        
        final backupService = DatabaseBackupService();
        
        // Create backup
        final backupPath = await backupService.createBackup(db, 'test_backup');
        expect(File(backupPath).existsSync(), isTrue);
        
        // Modify database
        await db.insert('users', {
          'id': 'user_to_be_lost',
          'email': '<EMAIL>',
          'display_name': 'Lost User',
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'last_login_at': DateTime.now().millisecondsSinceEpoch,
          'is_guest': 0,
          'stats': '{}',
          'preferences': '{}',
        });
        
        final userCountAfterInsert = await db.rawQuery('SELECT COUNT(*) as count FROM users');
        expect(userCountAfterInsert.first['count'], equals(2));
        
        // Restore backup
        await db.close();
        await backupService.restoreBackup(backupPath, db.path);
        
        // Reopen database and verify restoration
        final restoredDb = await openDatabase(db.path);
        final userCountAfterRestore = await restoredDb.rawQuery('SELECT COUNT(*) as count FROM users');
        expect(userCountAfterRestore.first['count'], equals(1));
        
        await restoredDb.close();
        await backupService.deleteBackup(backupPath);
      });
    });

    group('Performance Tests', () {
      test('should complete migration within acceptable time', () async {
        final db = await DatabaseMigrationTestFramework.createTestDatabase(version: 1);
        
        // Create larger dataset
        await db.transaction((txn) async {
          for (int i = 0; i < 1000; i++) {
            await txn.insert('users', {
              'id': 'user_$i',
              'email': 'user$<EMAIL>',
              'display_name': 'User $i',
              'created_at': DateTime.now().millisecondsSinceEpoch,
              'last_login_at': DateTime.now().millisecondsSinceEpoch,
              'is_guest': 0,
              'stats': jsonEncode({"totalGamesPlayed": i}),
              'preferences': jsonEncode({}),
            });
          }
        });
        
        final stopwatch = Stopwatch()..start();
        
        final migrationManager = DatabaseMigrationManager();
        MigrationRegistry.registerAllMigrations(migrationManager);
        await migrationManager.performMigration(db, 1, 4);
        
        stopwatch.stop();
        
        // Migration should complete within 10 seconds for 1000 users
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
        
        await db.close();
      });
    });
  });
}