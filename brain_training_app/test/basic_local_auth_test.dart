import 'package:flutter_test/flutter_test.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

void main() {
  group('Basic Local Auth Logic Tests', () {
    test('Password hashing should work consistently', () {
      String hashPassword(String password) {
        final bytes = utf8.encode(password);
        final digest = sha256.convert(bytes);
        return digest.toString();
      }

      final password = 'test123';
      final hash1 = hashPassword(password);
      final hash2 = hashPassword(password);
      
      expect(hash1, equals(hash2));
      expect(hash1, isNot(equals(password))); // Should be hashed
      expect(hash1.length, equals(64)); // SHA256 hash length
    });

    test('User ID generation logic', () {
      // Mock UUID generation
      String generateId() {
        return 'user-${DateTime.now().millisecondsSinceEpoch}';
      }

      final id1 = generateId();
      final id2 = generateId();
      
      expect(id1, isNot(equals(id2)));
      expect(id1, startsWith('user-'));
    });

    test('Email validation logic', () {
      bool isValidEmail(String email) {
        final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
        return emailRegex.hasMatch(email);
      }

      expect(isValidEmail('<EMAIL>'), isTrue);
      expect(isValidEmail('<EMAIL>'), isTrue);
      expect(isValidEmail('invalid-email'), isFalse);
      expect(isValidEmail('missing@domain'), isFalse);
      expect(isValidEmail('@domain.com'), isFalse);
    });

    test('Password strength validation', () {
      bool isPasswordStrong(String password) {
        return password.length >= 6;
      }

      expect(isPasswordStrong('password123'), isTrue);
      expect(isPasswordStrong('hello1'), isTrue);
      expect(isPasswordStrong('test'), isFalse);
      expect(isPasswordStrong(''), isFalse);
    });

    test('Guest user creation logic', () {
      Map<String, dynamic> createGuestUser() {
        final now = DateTime.now();
        return {
          'id': 'guest-${now.millisecondsSinceEpoch}',
          'email': '<EMAIL>',
          'displayName': '게스트 사용자',
          'isGuest': true,
          'createdAt': now.toIso8601String(),
          'lastLoginAt': now.toIso8601String(),
        };
      }

      final guestUser = createGuestUser();
      
      expect(guestUser['isGuest'], isTrue);
      expect(guestUser['email'], equals('<EMAIL>'));
      expect(guestUser['displayName'], equals('게스트 사용자'));
      expect(guestUser['id'], startsWith('guest-'));
    });
  });
}