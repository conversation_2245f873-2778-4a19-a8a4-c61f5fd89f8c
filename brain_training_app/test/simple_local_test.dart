import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Simple Local Tests', () {
    test('Basic test to verify testing works', () {
      expect(1 + 1, equals(2));
      expect('hello'.toUpperCase(), equals('HELLO'));
    });

    test('DateTime operations work', () {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      
      expect(tomorrow.isAfter(now), isTrue);
      expect(now.isBefore(tomorrow), isTrue);
    });

    test('JSON serialization basics', () {
      final data = {
        'name': 'Test User',
        'score': 100,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      expect(data['name'], equals('Test User'));
      expect(data['score'], equals(100));
      expect(data['timestamp'], isA<int>());
    });
  });
}