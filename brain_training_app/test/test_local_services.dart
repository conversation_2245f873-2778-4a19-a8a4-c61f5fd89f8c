import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'dart:io';

import '../lib/core/services/local_database_service.dart';
import '../lib/core/services/local_auth_service.dart';
import '../lib/core/models/app_user.dart';
import '../lib/shared/models/user.dart';
import '../lib/shared/models/game_result.dart';

void main() {
  group('Local Services Integration Tests', () {
    late LocalDatabaseService localDb;
    late LocalAuthService localAuth;

    setUpAll(() async {
      // Initialize sqflite_ffi for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      localDb = LocalDatabaseService();
      localAuth = LocalAuthService();
      
      // Use in-memory database for testing
      await localDb.database;
    });

    tearDown(() async {
      await localDb.close();
    });

    group('Local Authentication Tests', () {
      test('Guest login should work', () async {
        // Mock flutter secure storage
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage')
            .setMockMethodCallHandler((MethodCall methodCall) async {
          if (methodCall.method == 'read') {
            return null; // No existing data
          } else if (methodCall.method == 'write') {
            return null; // Success
          } else if (methodCall.method == 'delete') {
            return null; // Success
          }
          return null;
        });

        await localAuth.initialize();
        final guestUser = await localAuth.signInAsGuest();
        
        expect(guestUser, isNotNull);
        expect(guestUser!.isGuest, isTrue);
        expect(guestUser.email, equals('<EMAIL>'));
        expect(guestUser.displayName, equals('게스트 사용자'));
      });

      test('Email/password registration and login should work', () async {
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage')
            .setMockMethodCallHandler((MethodCall methodCall) async {
          if (methodCall.method == 'read') {
            if (methodCall.arguments['key'] == 'user_password_test-user-id') {
              // Return hashed password for test
              return 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'; // 'hello' hashed
            }
            return null;
          } else if (methodCall.method == 'write') {
            return null;
          } else if (methodCall.method == 'delete') {
            return null;
          }
          return null;
        });

        await localAuth.initialize();
        
        // Register new user
        final newUser = await localAuth.createUserWithEmailAndPassword(
          '<EMAIL>',
          'hello123',
          'Test User',
        );
        
        expect(newUser, isNotNull);
        expect(newUser!.email, equals('<EMAIL>'));
        expect(newUser.displayName, equals('Test User'));
        expect(newUser.isGuest, isFalse);

        // Sign out
        await localAuth.signOut();
        expect(localAuth.currentUser, isNull);

        // Sign in with same credentials
        final signedInUser = await localAuth.signInWithEmailAndPassword(
          '<EMAIL>',
          'hello123',
        );
        
        expect(signedInUser, isNotNull);
        expect(signedInUser!.email, equals('<EMAIL>'));
      });
    });

    group('Local Database Tests', () {
      test('User CRUD operations should work', () async {
        final testUser = AppUser(
          id: 'test-user-1',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isGuest: false,
          stats: const UserStats(),
          preferences: const UserPreferences(),
        );

        // Create user
        await localDb.saveUser(testUser);

        // Read user
        final retrievedUser = await localDb.getUser('test-user-1');
        expect(retrievedUser, isNotNull);
        expect(retrievedUser!.email, equals('<EMAIL>'));
        expect(retrievedUser.displayName, equals('Test User'));

        // Update user
        final updatedUser = testUser.copyWith(displayName: 'Updated User');
        await localDb.updateUser(updatedUser);

        final retrievedUpdatedUser = await localDb.getUser('test-user-1');
        expect(retrievedUpdatedUser!.displayName, equals('Updated User'));
      });

      test('Game result operations should work', () async {
        final testGameResult = GameResult(
          id: 'game-1',
          userId: 'test-user-1',
          gameType: GameType.dualNBack,
          level: 2,
          score: 850,
          accuracy: 0.85,
          duration: const Duration(minutes: 5),
          timestamp: DateTime.now(),
          gameSpecificData: {'n_level': 2, 'visual_accuracy': 0.9},
        );

        // Save game result
        await localDb.saveGameResult(testGameResult);

        // Get game results
        final gameResults = await localDb.getGameResults('test-user-1');
        expect(gameResults.length, equals(1));
        expect(gameResults.first.gameType, equals(GameType.dualNBack));
        expect(gameResults.first.score, equals(850));
        expect(gameResults.first.accuracy, equals(0.85));
      });
    });

    group('Data Consistency Tests', () {
      test('Multiple operations should maintain data integrity', () async {
        // Create a user
        final user = AppUser(
          id: 'user-consistency-test',
          email: '<EMAIL>',
          displayName: 'Consistency User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isGuest: false,
          stats: const UserStats(),
          preferences: const UserPreferences(),
        );
        
        await localDb.saveUser(user);

        // Add multiple game results
        for (int i = 0; i < 5; i++) {
          final gameResult = GameResult(
            id: 'game-$i',
            userId: user.id,
            gameType: GameType.dualNBack,
            level: i + 1,
            score: (i + 1) * 100,
            accuracy: 0.7 + (i * 0.05),
            duration: Duration(minutes: 3 + i),
            timestamp: DateTime.now().subtract(Duration(days: i)),
            gameSpecificData: {'session': i},
          );
          
          await localDb.saveGameResult(gameResult);
        }

        // Verify all data is stored correctly
        final retrievedUser = await localDb.getUser(user.id);
        expect(retrievedUser, isNotNull);

        final gameResults = await localDb.getGameResults(user.id);
        expect(gameResults.length, equals(5));
        
        // Results should be ordered by timestamp DESC
        expect(gameResults.first.level, equals(1)); // Most recent (i=0)
        expect(gameResults.last.level, equals(5));  // Oldest (i=4)
      });
    });

    group('Error Handling Tests', () {
      test('Should handle non-existent user gracefully', () async {
        final user = await localDb.getUser('non-existent-user');
        expect(user, isNull);
      });

      test('Should handle empty game results gracefully', () async {
        final gameResults = await localDb.getGameResults('non-existent-user');
        expect(gameResults, isEmpty);
      });

      test('Should handle invalid login gracefully', () async {
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage')
            .setMockMethodCallHandler((MethodCall methodCall) async {
          return null; // No stored data
        });

        await localAuth.initialize();
        
        expect(
          () => localAuth.signInWithEmailAndPassword('<EMAIL>', 'wrongpass'),
          throwsA(isA<AuthException>()),
        );
      });
    });
  });
}