# Brain Training App - 두뇌 훈련 앱

## 프로젝트 개요
매일 사용하는 두뇌 훈련 앱을 개발합니다. Dual N-back, Flash card 등의 과학적으로 입증된 인지 훈련 게임을 제공하며, 버즈빌을 통한 수익화 모델을 구현합니다.

## 기술 스택
- **Frontend**: Flutter (Dart)
- **Backend**: FastAPI (Python) 
- **Database**: SQLite (로컬), PostgreSQL (서버)
- **State Management**: Riverpod
- **Authentication**: Firebase Auth
- **Analytics**: Firebase Analytics
- **Monetization**: 버즈빌 SDK

## 프로젝트 구조
```
brain_training_app/
├── lib/
│   ├── main.dart
│   ├── core/
│   │   ├── constants/
│   │   ├── themes/
│   │   ├── utils/
│   │   └── services/
│   ├── features/
│   │   ├── auth/
│   │   ├── games/
│   │   │   ├── dual_n_back/
│   │   │   ├── flash_cards/
│   │   │   ├── working_memory/
│   │   │   └── attention_training/
│   │   ├── profile/
│   │   ├── leaderboard/
│   │   └── achievements/
│   ├── shared/
│   │   ├── widgets/
│   │   ├── models/
│   │   └── providers/
│   └── l10n/
├── assets/
│   ├── images/
│   ├── sounds/
│   └── fonts/
├── test/
└── integration_test/
```

## 핵심 기능 요구사항

### 1. 인증 시스템
- 이메일/소셜 로그인 (Google, Apple)
- 게스트 모드 지원
- 프로필 관리

### 2. 두뇌 훈련 게임들

#### Dual N-back 게임
- 시각적/청각적 자극 제시
- N-back 레벨 조정 (1-back부터 시작)
- 정확도 기반 난이도 자동 조절
- 세션별 성과 추적

#### Flash Card 게임
- 간격 반복 시스템 (Spaced Repetition)
- 카테고리별 카드 덱
- 사용자 정의 카드 생성
- 학습 진도 시각화

#### 작업기억 훈련
- 숫자/문자 순서 기억
- 시각적 패턴 인식
- 다중 작업 처리

#### 주의력 훈련
- 선택적 주의력 게임
- 지속적 주의력 테스트
- 시각 탐색 과제

### 3. 게임화 시스템
- 일일 스트릭 (연속 사용일)
- 레벨 시스템 및 경험치
- 배지 및 성취 시스템
- 리더보드 (일간/주간/월간)

### 4. 개인화 기능
- 사용자 성과 분석
- 취약 영역 식별
- 맞춤형 훈련 계획 추천
- 진행도 시각화

### 5. 수익화 연동
- 버즈빌 BuzzAd Benefit SDK 통합
- 보상형 광고 (힌트, 추가 기회)
- 프리미엄 구독 모델
- 인앱 구매 (테마, 추가 콘텐츠)

### 6. 리워드 포인트 시스템 ✅ **2025-01-08 완전 구현**
- **포인트 획득**: 일일 로그인(10P), 게임 완료(5-10P), 연속 로그인 보너스(7일마다 50P)
- **포인트 사용**: 플래시카드 저장소 확장 (+100/+500/+1000 슬롯), 게임 힌트
- **저장소 시스템**: 동적 한도 계산 (기본 한도 + 리워드 보너스)
- **이중 스토리지**: SQLite(게스트/로컬) + Firestore(로그인 사용자) 자동 라우팅
- **실시간 알림**: 로그인 보너스, 스트릭 달성 시 자동 알림
- **거래 내역**: 완전한 포인트 획득/사용 내역 추적

## 구현 가이드라인

### UI/UX 원칙
- Material Design 3 준수
- 접근성 지원 (시각/청각 장애인 고려)
- 다크/라이트 테마 지원
- 반응형 디자인 (태블릿 지원)

### 성능 요구사항
- 앱 시작 시간: 3초 이내
- 게임 반응 시간: 100ms 이내
- 메모리 사용량: 150MB 이하
- 배터리 효율성 최적화

### 데이터 관리
- 로컬 데이터: SQLite + Hive
- 서버 동기화: REST API
- 오프라인 모드 지원
- 데이터 암호화 (AES-256)

## 주요 모델 정의

### User Model
```dart
class User {
  final String id;
  final String email;
  final String? displayName;
  final String? photoURL;
  final DateTime createdAt;
  final UserStats stats;
  final UserPreferences preferences;
}

class UserStats {
  final int totalGamesPlayed;
  final int currentStreak;
  final int maxStreak;
  final Map<GameType, GameStats> gameStats;
  final int totalXP;
  final int level;
}
```

### Game Session Model
```dart
class GameSession {
  final String id;
  final String userId;
  final GameType gameType;
  final int level;
  final double accuracy;
  final Duration duration;
  final int score;
  final DateTime startTime;
  final DateTime endTime;
  final Map<String, dynamic> gameSpecificData;
}
```

### Achievement Model
```dart
class Achievement {
  final String id;
  final String title;
  final String description;
  final String iconPath;
  final AchievementType type;
  final Map<String, dynamic> criteria;
  final int xpReward;
  final bool isUnlocked;
  final DateTime? unlockedAt;
}
```

## 게임별 구현 세부사항

### Dual N-back 게임
```dart
class DualNBackGame extends StatefulWidget {
  final int nLevel;
  final Duration stimulusDuration;
  final Duration interstimulusInterval;
  
  @override
  _DualNBackGameState createState() => _DualNBackGameState();
}

// 핵심 로직
class DualNBackLogic {
  List<Position> visualSequence = [];
  List<AudioStimulus> audioSequence = [];
  
  bool checkVisualMatch(int currentIndex, int nLevel) {
    if (currentIndex < nLevel) return false;
    return visualSequence[currentIndex] == 
           visualSequence[currentIndex - nLevel];
  }
  
  bool checkAudioMatch(int currentIndex, int nLevel) {
    if (currentIndex < nLevel) return false;
    return audioSequence[currentIndex] == 
           audioSequence[currentIndex - nLevel];
  }
}
```

### Flash Card 시스템
```dart
class SpacedRepetitionAlgorithm {
  // SM2 알고리즘 구현
  CardReview calculateNextReview(Card card, int quality) {
    double easeFactor = card.easeFactor;
    int interval = card.interval;
    
    if (quality >= 3) {
      if (card.repetitions == 0) {
        interval = 1;
      } else if (card.repetitions == 1) {
        interval = 6;
      } else {
        interval = (interval * easeFactor).round();
      }
      card.repetitions++;
    } else {
      card.repetitions = 0;
      interval = 1;
    }
    
    easeFactor = math.max(1.3, 
        easeFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)));
    
    return CardReview(
      nextReviewDate: DateTime.now().add(Duration(days: interval)),
      easeFactor: easeFactor,
      interval: interval,
    );
  }
}
```

## 상태 관리 구조

### Riverpod Providers
```dart
// 사용자 상태
final userProvider = StateNotifierProvider<UserNotifier, AsyncValue<User?>>((ref) {
  return UserNotifier(ref.read(authServiceProvider));
});

// 게임 세션 상태
final gameSessionProvider = StateNotifierProvider.family<GameSessionNotifier, 
    GameSessionState, GameType>((ref, gameType) {
  return GameSessionNotifier(gameType, ref.read(gameServiceProvider));
});

// 리더보드 상태
final leaderboardProvider = FutureProvider.family<List<LeaderboardEntry>, 
    LeaderboardFilter>((ref, filter) {
  return ref.read(leaderboardServiceProvider).getLeaderboard(filter);
});
```

## 서비스 구현

### 게임 서비스
```dart
class GameService {
  final DatabaseService _db;
  final AnalyticsService _analytics;
  
  Future<void> saveGameSession(GameSession session) async {
    await _db.saveGameSession(session);
    await _analytics.logGameCompleted(session);
    await _updateUserStats(session);
  }
  
  Future<List<GameSession>> getUserGameHistory(
      String userId, GameType? gameType) async {
    return _db.getGameSessions(userId, gameType: gameType);
  }
  
  Future<UserProgress> calculateProgress(String userId) async {
    final sessions = await getUserGameHistory(userId, null);
    return UserProgress.fromSessions(sessions);
  }
}
```

### 버즈빌 연동 서비스
```dart
class BuzzvilService {
  static const MethodChannel _channel = MethodChannel('buzzvil_integration');
  
  Future<void> initializeBuzzvil() async {
    await _channel.invokeMethod('initialize');
  }
  
  Future<bool> showRewardedAd() async {
    try {
      final result = await _channel.invokeMethod('showRewardedAd');
      return result == true;
    } catch (e) {
      print('광고 로드 실패: $e');
      return false;
    }
  }
  
  Future<void> logRevenue(double amount) async {
    await _channel.invokeMethod('logRevenue', {'amount': amount});
  }
}
```

## 테스트 전략

### 단위 테스트
- 각 게임 로직 테스트
- 상태 관리 테스트
- 서비스 계층 테스트

### 위젯 테스트
- 게임 UI 상호작용 테스트
- 폼 검증 테스트
- 네비게이션 테스트

### 통합 테스트
- 완전한 게임 세션 플로우
- 사용자 인증 플로우
- 데이터 동기화 테스트

## 배포 및 CI/CD

### 빌드 구성
```yaml
# pubspec.yaml
name: brain_training_app
description: 과학적 근거 기반 두뇌 훈련 앱

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  riverpod: ^2.4.0
  flutter_riverpod: ^2.4.0
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  firebase_analytics: ^10.4.5
  sqflite: ^2.3.0
  hive: ^2.2.3
  dio: ^5.3.2
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  go_router: ^10.1.2
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
```

### GitHub Actions 워크플로우
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.13.0'
    - run: flutter pub get
    - run: flutter analyze
    - run: flutter test
    
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
    - run: flutter build apk --release
    - run: flutter build appbundle --release
```

## 보안 및 프라이버시

### 데이터 보호
- 로컬 데이터 암호화
- API 통신 HTTPS 강제
- 민감 정보 secure storage 사용
- GDPR/CCPA 준수

### 인증 보안
- JWT 토큰 관리
- 생체 인증 지원
- 세션 타임아웃 관리

## 성능 최적화

### 메모리 관리
- 이미지 캐싱 최적화
- 불필요한 위젯 rebuild 방지
- 메모리 누수 방지

### 배터리 최적화
- 백그라운드 작업 최소화
- 애니메이션 최적화
- 센서 사용 최적화

## 다국어 지원

### ARB 파일 구조
```
l10n/
├── app_en.arb
├── app_ko.arb
├── app_ja.arb
└── app_zh.arb
```

## 출시 체크리스트

### 앱스토어 준비
- [ ] 앱 아이콘 (1024x1024)
- [ ] 스크린샷 (모든 디바이스 크기)
- [ ] 앱 설명 (한국어, 영어)
- [ ] 개인정보 처리방침
- [ ] 이용약관
- [ ] 연령 등급 검토

### 품질 보증
- [ ] 모든 기기에서 테스트 완료
- [ ] 성능 벤치마크 통과
- [ ] 보안 감사 완료
- [ ] 접근성 테스트 완료

### 개발 중 확인 사항
- 특정 주제로 TODO 생성 후 계획 재 수립하며 문제가 없는지 확인
- TODO 단위가 끝날때마다 적절히 git commit 생성
